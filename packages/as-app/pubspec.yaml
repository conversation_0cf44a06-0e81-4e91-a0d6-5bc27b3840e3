name: asmarketplace
description: 'Marketplace App'
publish_to: 'none'
version: 0.1.0+2

environment:
  sdk: '>=3.3.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cached_network_image: ^3.3.1
  change_case: ^2.0.1
  collection: ^1.18.0
  dartx: ^1.2.0
  dio: ^5.4.2+1
  easy_localization: ^3.0.5
  equatable: ^2.0.5
  flutter_easyloading: ^3.0.5
  flutter_flavor: ^3.1.3
  flutter_hooks: ^0.20.5
  flutter_secure_storage: ^9.0.0
  flutter_svg: ^2.0.10+1
  fpdart: ^1.1.0
  freezed_annotation: ^2.4.1
  gap: ^3.0.1
  go_router: ^14.0.2
  google_fonts: ^6.2.1
  hive_flutter: ^1.1.0
  hooks_riverpod: ^2.5.1
  jiffy: ^6.2.1
  json_annotation: ^4.9.0
  jwt_decoder: ^2.0.1
  loadmore: ^2.1.0
  permission_handler: ^11.3.0
  riverpod_annotation: ^2.3.5
  rxdart: ^0.28.0
  stack_trace: ^1.11.1
  talker_dio_logger: ^4.1.5
  talker_flutter: ^4.7.5
  talker_riverpod_logger: ^4.6.7
  firebase_core: ^3.12.0
  connectivity_plus: ^6.1.4
  openapi:
    path: ./openapi
  intl: ^0.19.0
  keyboard_dismisser: ^3.0.0
  firebase_remote_config: ^5.4.7
  version: ^3.0.2
  appsflyer_sdk: ^6.15.2
  package_info_plus: ^8.3.0
  url_launcher: ^6.3.1
  firebase_messaging: ^15.2.4
  flutter_local_notifications: ^18.0.1
  app_links: ^6.4.0
  dotted_line: ^3.2.3
  badges: ^3.1.2
  popover: ^0.3.1
  country_flags: ^3.2.0
  alphabet_list_view: ^1.1.4
  flutter_spinkit: ^5.2.1
  carousel_slider: ^5.0.0
  dynamic_height_grid_view: ^0.0.4
  phone_form_field: ^10.0.6
  share_plus: ^10.1.4
  in_app_purchase: ^3.2.1
  flutter_rating_bar: ^4.0.1
  dotted_border: ^3.0.1
  flutter_html: ^3.0.0
  webview_flutter: ^4.12.0
  image_picker: ^1.1.2
  in_app_purchase_storekit: ^0.4.0
  flutter_slidable: ^4.0.0
  app_tracking_transparency: ^2.0.6+1
  encrypt: ^5.0.3
  pointycastle: ^3.9.1
  mobile_device_identifier: ^0.0.3
  firebase_crashlytics: ^4.3.9
  fl_chart: ^1.0.0
  syncfusion_flutter_datepicker: ^30.1.40

dev_dependencies:
  build_runner: ^2.4.9
  custom_lint: ^0.7.0
  flutter_gen_runner: ^5.4.0
  flutter_lints: ^5.0.0
  freezed: ^2.4.7
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1
  mocktail: ^1.0.3
  pubspec_dependency_sorter: ^1.0.4
  riverpod_generator: ^2.4.0
  riverpod_lint: ^2.3.10

  flutter_test:
    sdk: flutter

flutter_gen:
  output: lib/core/assets/
  integrations:
    flutter_svg: true
  assets:
    outputs:
      class_name: Assets
      package_parameter_enabled: false
    enabled: true
  fonts:
    outputs:
      class_name: AppFonts
    enabled: true
  line_length: 80

flutter:
  assets:
    - assets/translations/
    - assets/icons/svgs/
  uses-material-design: true
