import 'dart:async';
import 'dart:io';

import 'package:asmarketplace/app_bootstrap.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'core/core.dart';
import 'firebase_options.dart';
import 'router/app_router.dart';

void runMyApp() async {
  final talker = TalkerFlutter.init();

  runZonedGuarded<Future<void>>(() async {
    // Ensure Flutter binding is initialized
    final binding = WidgetsFlutterBinding.ensureInitialized();
    await EasyLocalization.ensureInitialized();
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

    if (Platform.isIOS) {
      /// https://github.com/flutter/flutter/issues/169335
      InAppPurchaseStoreKitPlatform.registerPlatform();
    }

    runApp(
      UncontrolledProviderScope(
        container: await appBootstrap(binding),
        child: const MyApp(),
      ),
    );
  }, (e, s) {
    FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
    talker.handle(e, s, 'ZonedGuarded: Uncaught exception!');
  });
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goRouter = ref.watch(goRouterProvider);

    return EasyLocalization(
        path: LocaleConstants.path,
        startLocale: LocaleConstants.en,
        fallbackLocale: LocaleConstants.ja,
        supportedLocales: const [
          LocaleConstants.ja,
          LocaleConstants.en,
          LocaleConstants.ko,
          LocaleConstants.zh,
          LocaleConstants.fr,
          LocaleConstants.es,
          LocaleConstants.it,
        ],
        child: Builder(builder: (context) {
          return MaterialApp.router(
            routerConfig: goRouter,
            debugShowCheckedModeBanner: false,
            restorationScopeId: 'app',
            theme: AppThemes.lightTheme,
            localizationsDelegates: [
              ...context.localizationDelegates,
            ],
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            builder: EasyLoading.init(
              builder: (context, child) {
                // Init services here to make sure storage, router, etc...
                // are ready
                initService(ref);
                return child!;
              },
            ),
          );
        }));
  }
}

void initService(WidgetRef ref) {}
