import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../core.dart';

class Common<PERSON>ie<PERSON>hart extends StatelessWidget {
  final List<PieChartSectionData> sections;
  final double radius;
  final double centerSpaceRadius;
  final double sectionsSpace;
  final double startDegreeOffset;
  final TextStyle? titleStyle;

  const CommonPieChart({
    super.key,
    required this.sections,
    this.radius = 40,
    this.centerSpaceRadius = 32,
    this.sectionsSpace = 0,
    this.startDegreeOffset = 270,
    this.titleStyle,
  });

  @override
  Widget build(BuildContext context) {
    final defaultTitleStyle =
        titleStyle ?? BodyTypography.md.withColor(const Color(0xFF1A1A1A));

    return PieChart(
      PieChartData(
        sections: sections
            .map((section) => section.copyWith(
                titleStyle: section.titleStyle ?? defaultTitleStyle))
            .toList(),
        sectionsSpace: sectionsSpace,
        centerSpaceRadius: centerSpaceRadius,
        startDegreeOffset: startDegreeOffset,
      ),
    );
  }
}

class PieChartSection {
  final double value;
  final Color color;
  final String title;

  const PieChartSection({
    required this.value,
    required this.color,
    required this.title,
  });
}

class CommonPieChartBuilder {
  static List<PieChartSectionData> buildSections(
    List<PieChartSection> sections, {
    double radius = 40,
    TextStyle? titleStyle,
  }) {
    return sections
        .map((section) => PieChartSectionData(
              value: section.value,
              color: section.color,
              title: section.title,
              radius: radius,
              titleStyle: titleStyle,
            ))
        .toList();
  }
}
