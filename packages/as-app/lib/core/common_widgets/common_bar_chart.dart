import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class CommonBarChart extends StatelessWidget {
  final List<BarChartGroupData> barGroups;
  final FlTitlesData? titlesData;
  final FlGridData? gridData;
  final FlBorderData? borderData;
  final double minY;
  final double maxY;

  const CommonBarChart({
    super.key,
    required this.barGroups,
    this.titlesData,
    this.gridData,
    this.borderData,
    this.minY = 0,
    this.maxY = 100,
  });

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        minY: minY,
        maxY: maxY,
        alignment: BarChartAlignment.center,
        barTouchData: const BarTouchData(
          enabled: false,
        ),
        barGroups: barGroups,
        titlesData: titlesData,
        gridData: gridData,
        borderData: borderData,
      ),
    );
  }
}
