import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../core.dart';

enum DatePickerType {
  year,
  monthYear,
  fullDate,
}

class CommonSelectDatePicker extends HookWidget {
  final String? hint;
  final String? firstLabel;
  final String? secondLabel;
  final String? errorText;
  final TextEditingController controller;
  final Function(DateTime?) onChanged;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final DatePickerType pickerType;

  const CommonSelectDatePicker({
    super.key,
    this.hint,
    this.firstLabel,
    this.secondLabel,
    this.errorText,
    required this.controller,
    required this.onChanged,
    this.prefixIcon,
    this.suffixIcon,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.pickerType = DatePickerType.fullDate,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return CommonTextfield(
      firstLabel: firstLabel,
      secondLabel: secondLabel,
      readOnly: true,
      errorText: errorText,
      controller: controller,
      backgroundColor: colorScheme.surfaceTint.withValues(alpha: 0.12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: colorScheme.outlineVariant,
        ),
      ),
      prefixIcon: GestureDetector(
        onTap: () => _showDatePicker(context),
        child: prefixIcon ?? const Icon(Icons.calendar_month_outlined),
      ),
      onTap: () => _showDatePicker(context),
      suffixIcon: IconButton(
        onPressed: () => _showDatePicker(context),
        padding: const EdgeInsets.all(12),
        icon: suffixIcon ?? const Icon(Icons.search),
      ),
    );
  }

  Future<void> _showDatePicker(BuildContext context) async {
    switch (pickerType) {
      case DatePickerType.year:
        await _showYearPicker(context);
        break;
      case DatePickerType.monthYear:
        await _showMonthYearPicker(context);
        break;
      case DatePickerType.fullDate:
        await _showFullDatePicker(context);
        break;
    }
  }

  Future<void> _showYearPicker(BuildContext context) async {
    final currentYear = DateTime.now().year;
    final initialYear = initialDate?.year ?? currentYear;
    final firstYear = firstDate?.year ?? 1900;
    final lastYear = lastDate?.year ?? currentYear;

    final selectedYear = await showDialog<int>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: lastYear - firstYear + 1,
              itemBuilder: (context, index) {
                final year = lastYear - index;
                return ListTile(
                  title: Text(year.toString()),
                  selected: year == initialYear,
                  onTap: () => Navigator.of(context).pop(year),
                );
              },
            ),
          ),
        );
      },
    );

    if (selectedYear != null) {
      final selectedDate = DateTime(selectedYear);
      onChanged(selectedDate);
      controller.text = selectedYear.toString();
    }
  }

  Future<void> _showMonthYearPicker(BuildContext context) async {
    final selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SizedBox(
            width: 300,
            height: 400,
            child: SfDateRangePicker(
              view: DateRangePickerView.year,
              selectionMode: DateRangePickerSelectionMode.single,
              initialSelectedDate: initialDate ?? DateTime.now(),
              initialDisplayDate: initialDate ?? DateTime.now(),
              showNavigationArrow: true,
              allowViewNavigation: true,
              onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                if (args.value is DateTime) {
                  Navigator.of(context).pop(args.value as DateTime);
                }
              },
            ),
          ),
        );
      },
    );

    if (selectedDate != null) {
      onChanged(selectedDate);
      controller.text =
          AppHelper.formatDate(selectedDate, format: DateFormatConstants.month)
              .toUpperCase();
    }
  }

  Future<void> _showFullDatePicker(BuildContext context) async {
    final selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SizedBox(
            width: 300,
            height: 400,
            child: SfDateRangePicker(
              view: DateRangePickerView.month,
              selectionMode: DateRangePickerSelectionMode.single,
              initialSelectedDate: initialDate ?? DateTime.now(),
              initialDisplayDate: initialDate ?? DateTime.now(),
              showNavigationArrow: true,
              allowViewNavigation: true,
              onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                if (args.value is DateTime) {
                  Navigator.of(context).pop(args.value as DateTime);
                }
              },
            ),
          ),
        );
      },
    );

    if (selectedDate != null) {
      onChanged(selectedDate);
      controller.text = AppHelper.formatDate(selectedDate);
    }
  }
}
