import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../core.dart';

class CommonSalesOverviewSection extends StatelessWidget {
  final String title;
  final int numberOfOrders;
  final double orderAmount;
  final double finalizedSalesAmount;
  final double itemPrice;
  final double vat;
  final double salesFee;
  final String? dateRange;

  const CommonSalesOverviewSection({
    super.key,
    required this.title,
    required this.numberOfOrders,
    required this.orderAmount,
    required this.finalizedSalesAmount,
    required this.itemPrice,
    required this.vat,
    required this.salesFee,
    this.dateRange,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      spacing: 4,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 24),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 4,
                children: [
                  Text(
                    title,
                    style: TitleTypography.lg.bold,
                  ),
                  if (dateRange != null)
                    Text(
                      dateRange!,
                      style: LabelTypography.sm.withColor(colorScheme.primary),
                    ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                spacing: 4,
                children: [
                  Text(
                    LocaleKeys.strCommonShopOrderCount.tr(),
                    style: LabelTypography.sm.bold.withColor(
                      colorScheme.tertiary,
                    ),
                  ),
                  Text(
                    numberOfOrders.toString(),
                    style: TitleTypography.lg.bold.withColor(
                      const Color(0xFFFF8D44),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFE6E6E6),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4,
                  children: [
                    Text(
                      LocaleKeys.strCommonShopOrderAmount.tr(),
                      style: LabelTypography.sm.bold.withColor(
                        colorScheme.primary,
                      ),
                    ),
                    Text(
                      AppHelper.formatCurrency(orderAmount),
                      style: TitleTypography.lg.bold,
                    ),
                  ],
                ),
              ),
              Container(
                width: 1,
                height: 40,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                color: theme.colorScheme.outline,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4,
                  children: [
                    Text(
                      LocaleKeys.strCommonShopFinalizedSales.tr(),
                      style: LabelTypography.sm.bold.withColor(
                        colorScheme.primary,
                      ),
                    ),
                    Text(
                      AppHelper.formatCurrency(finalizedSalesAmount),
                      style: TitleTypography.lg.bold,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Row(
          spacing: 4,
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFFE6E6E6),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4,
                  children: [
                    Text(
                      LocaleKeys.valueTitleShopItemItemPrice.tr(),
                      style: LabelTypography.sm.bold.withColor(
                        colorScheme.tertiary,
                      ),
                    ),
                    Text(
                      AppHelper.formatCurrency(itemPrice),
                      style: BodyTypography.md.withColor(
                        colorScheme.tertiary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFFE6E6E6),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4,
                  children: [
                    Text(
                      LocaleKeys.strCommonCartTaxVAT.tr(),
                      style: LabelTypography.sm.bold.withColor(
                        colorScheme.tertiary,
                      ),
                    ),
                    Text(
                      AppHelper.formatCurrency(vat),
                      style: BodyTypography.md.withColor(
                        colorScheme.tertiary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFFE6E6E6),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4,
                  children: [
                    Text(
                      LocaleKeys.strCommonCartFeeSales.tr(),
                      style: LabelTypography.sm.bold.withColor(
                        colorScheme.tertiary,
                      ),
                    ),
                    Text(
                      '- ${AppHelper.formatCurrency(salesFee)}',
                      style: BodyTypography.md.withColor(
                        colorScheme.error,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
