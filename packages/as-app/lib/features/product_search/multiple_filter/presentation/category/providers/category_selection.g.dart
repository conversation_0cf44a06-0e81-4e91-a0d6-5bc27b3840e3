// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_selection.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categorySelectionHash() => r'025e55cf84593520aabe7e3c4e954abe2961fc19';

/// See also [CategorySelection].
@ProviderFor(CategorySelection)
final categorySelectionProvider =
    AutoDisposeNotifierProvider<CategorySelection, Set<String>>.internal(
  CategorySelection.new,
  name: r'categorySelectionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categorySelectionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CategorySelection = AutoDisposeNotifier<Set<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
