// Mock data cho YearlySalesScreen

class SalesMockData {
  static const orderAmount = 12345678;
  static const finalizedSalesAmount = 12345678;
  static const itemPrice = 123456789;
  static const vat = 12345678;
  static const salesFee = 12345678;
  static const numberOfOrders = 123;

  static const yearlyRange = '2025.01.01 - 2025.12.31';
  static const monthlyRange = '2025.12.01 - 2025.12.31';
  static const dailyRange = '2025.12.31';
  static const numberOfOrdersByQuarter = [60, 100, 20, 70];
  static const numberOfOrdersByQuarterMonth = [
    60,
    100,
    20,
    70,
    40,
    50,
    80,
    50,
    70,
    0,
    10,
    70,
  ];

  static const numberOfOrdersByQuarterDays = [
    60,
    100,
    20,
    70,
    40,
    50,
    80,
    50,
    70,
    0,
    10,
    70,
    60,
    100,
    20,
    70,
    40,
    50,
    80,
    50,
    70,
    0,
    10,
    70,
    50,
    80,
    50,
    70,
    0,
    10,
    70,
  ];

  static const quarters = ['2025Q3', '2025Q4', '2026Q1', '2026Q2'];
  static const quarterMonths = [
    'JAN',
    'FEB',
    'MAR',
    'APR',
    'MAY',
    'JUN',
    'JUL',
    'AUG',
    'SEP',
    'OCT',
    'NOV',
    'DEC'
  ];

  static const domesticTransactionCount = 60;
  static const internationalTransactionCount = 50;
  static const domesticTransactionAmount = 123456789;
  static const internationalTransactionAmount = 123456789;

  static const pieDomesticPercent = 55;
  static const pieInternationalPercent = 45;

  static const tableDataOrder = [
    {
      'orderNumber': '0000000000000000',
      'amount': '123,456,789',
    },
    {
      'orderNumber': '0000000000000000',
      'amount': '123,456,789',
    },
    {
      'orderNumber': '0000000000000000',
      'amount': '123,456,789',
    },
  ];

  static const tableDataYearly = [
    {
      'dateTime': '2026',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
  ];

  static const tableDataMonthly = [
    {
      'dateTime': 'Dec. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Nov. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Oct. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Sep. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Aug. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Jul. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Jun. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'May. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Apr. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Mar. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Feb. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': 'Jun. 2025',
      'orders': '1,234',
      'amount': '123,456,789',
    },
  ];

  static const tableDataDaily = [
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
    {
      'dateTime': '2025.12.31',
      'orders': '1,234',
      'amount': '123,456,789',
    },
  ];
}
