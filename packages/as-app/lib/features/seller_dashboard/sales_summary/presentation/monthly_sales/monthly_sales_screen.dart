import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../../core/core.dart';
import '../../mock/sales_summary_mock.dart';

class MonthlySalesScreen extends HookConsumerWidget {
  const MonthlySalesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final releaseDateController = useTextEditingController(
      text: 'DEC',
    );
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CommonSelectDatePicker(
                firstLabel: '2025',
                controller: releaseDateController,
                initialDate: DateTime.now(),
                prefixIcon: Icon(
                  Icons.date_range_outlined,
                  size: 24,
                  color: colorScheme.outline,
                ),
                suffixIcon:
                    const Icon(Icons.arrow_drop_down_outlined, size: 24),
                onChanged: (date) => {},
                firstDate: DateTime.now(),
                lastDate: DateTime(2050),
                pickerType: DatePickerType.monthYear,
              ),
              CommonSalesOverviewSection(
                title: LocaleKeys.strCommonShopSalesSummary.tr(),
                numberOfOrders: SalesMockData.numberOfOrders,
                dateRange: SalesMockData.monthlyRange,
                orderAmount: SalesMockData.orderAmount.toDouble(),
                finalizedSalesAmount:
                    SalesMockData.finalizedSalesAmount.toDouble(),
                itemPrice: SalesMockData.itemPrice.toDouble(),
                vat: SalesMockData.vat.toDouble(),
                salesFee: SalesMockData.salesFee.toDouble(),
              ),
              Gap(24),
              _ChartsSection(),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: CommonDottedLine(),
              ),
              _MonthlySalesTable(),
            ],
          ),
        ),
      ),
    );
  }
}

class _ChartsSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    Widget leftTitles(double value, TitleMeta meta,
        {bool isShowAmount = false}) {
      final intValue = value.toInt();
      return intValue % 20 == 0 && intValue <= 100
          ? Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Text(
                isShowAmount
                    ? '${AppHelper.formatCurrency(intValue)}K'
                    : intValue.toString(),
                style: BodyTypography.sm.withColor(
                  colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.right,
              ),
            )
          : const SizedBox();
    }

    Widget bottomTitles(double value, TitleMeta meta) {
      final intValue = value.toInt() + 1;
      final daysInMonth = 31;

      final showDays = <int>[1, 5, 10, 15, 20, 25];
      if (daysInMonth >= 29) showDays.add(daysInMonth);

      return showDays.contains(intValue) &&
              intValue >= 1 &&
              intValue <= daysInMonth
          ? SideTitleWidget(
              angle: -0.5,
              meta: meta,
              fitInside: SideTitleFitInsideData(
                enabled: true,
                distanceFromEdge: -5,
                parentAxisSize: 0,
                axisPosition: 0,
              ),
              child: Text(
                '12/${intValue.toString()}',
                style: BodyTypography.sm.withColor(
                  colorScheme.onSurfaceVariant,
                ),
              ),
            )
          : const SizedBox();
    }

    BorderSide borderData() {
      return BorderSide(
        color: colorScheme.outline.withValues(alpha: 0.3),
        width: 1,
      );
    }

    FlTitlesData titlesDataWidget({bool isShowAmount = false}) {
      return FlTitlesData(
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 20,
            reservedSize: 45,
            getTitlesWidget: (value, meta) =>
                leftTitles(value, meta, isShowAmount: isShowAmount),
          ),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 25,
            getTitlesWidget: bottomTitles,
          ),
        ),
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      );
    }

    List<BarChartGroupData> barGroupsWidget({bool isShowAmount = false}) {
      return List.generate(
          31,
          (i) => BarChartGroupData(x: i, barRods: [
                BarChartRodData(
                    toY:
                        SalesMockData.numberOfOrdersByQuarterDays[i].toDouble(),
                    color: isShowAmount
                        ? const Color(0xFFFF8D44)
                        : const Color(0xFF2BC8E4),
                    width: 7,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(2),
                    ))
              ]));
    }

    FlGridData gridDataWidget() {
      return FlGridData(
        show: true,
        horizontalInterval: 20,
        drawVerticalLine: false,
        checkToShowHorizontalLine: (value) {
          return value % 20 == 0 && value >= 0 && value <= 100;
        },
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: colorScheme.outline.withValues(alpha: 0.3),
            strokeWidth: 1,
          );
        },
      );
    }

    FlBorderData borderDataWidget() {
      return FlBorderData(
        border: Border(
          bottom: borderData(),
          top: borderData(),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 24,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 24,
          children: [
            Text(
              LocaleKeys.strCommonShopOrderCount.tr(),
              textAlign: TextAlign.left,
              style: TitleTypography.sm.bold.withColor(
                colorScheme.tertiary,
              ),
            ),
            SizedBox(
              height: 120,
              child: CommonBarChart(
                barGroups: barGroupsWidget(),
                titlesData: titlesDataWidget(),
                gridData: gridDataWidget(),
                borderData: borderDataWidget(),
                minY: 0,
                maxY: 100,
              ),
            ),
            Row(
              children: [
                SizedBox(
                  width: 160,
                  height: 160,
                  child: CommonPieChart(
                    sections: CommonPieChartBuilder.buildSections([
                      PieChartSection(
                        value: SalesMockData.pieDomesticPercent.toDouble(),
                        color: const Color(0xFF99F2FF),
                        title: '${SalesMockData.pieDomesticPercent}%',
                      ),
                      PieChartSection(
                        value: SalesMockData.pieInternationalPercent.toDouble(),
                        color: const Color(0xFFCCCCCC),
                        title: '${SalesMockData.pieInternationalPercent}%',
                      ),
                    ]),
                    radius: 40,
                    centerSpaceRadius: 32,
                    sectionsSpace: 0,
                    startDegreeOffset: 270,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 4,
                    children: [
                      Row(
                        spacing: 4,
                        children: [
                          Container(
                              width: 16,
                              height: 16,
                              color: const Color(0xFF99F2FF)),
                          Expanded(
                            child: Text(
                              LocaleKeys.strCommonShopDomesticTransaction.tr(),
                              style: LabelTypography.sm.bold.withColor(
                                colorScheme.tertiary,
                              ),
                            ),
                          ),
                          Text(
                            ': ${SalesMockData.domesticTransactionCount}',
                            style: LabelTypography.sm.bold.withColor(
                              colorScheme.tertiary,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Container(
                              width: 16,
                              height: 16,
                              color: const Color(0xFFCCCCCC)),
                          Expanded(
                            child: Text(
                              LocaleKeys.strCommonShopInternationalTransaction
                                  .tr(),
                              style: LabelTypography.sm.bold.withColor(
                                colorScheme.tertiary,
                              ),
                            ),
                          ),
                          Text(
                            ': ${SalesMockData.internationalTransactionCount}',
                            style: LabelTypography.sm.bold.withColor(
                              colorScheme.tertiary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 24,
          children: [
            Text(
              LocaleKeys.strCommonShopOrderAmount.tr(),
              textAlign: TextAlign.left,
              style: TitleTypography.sm.bold.withColor(
                colorScheme.tertiary,
              ),
            ),
            SizedBox(
              height: 120,
              child: CommonBarChart(
                barGroups: barGroupsWidget(isShowAmount: true),
                titlesData: titlesDataWidget(isShowAmount: true),
                gridData: gridDataWidget(),
                borderData: borderDataWidget(),
                minY: 0,
                maxY: 100,
              ),
            ),
            Row(
              spacing: 24,
              children: [
                SizedBox(
                  width: 160,
                  height: 160,
                  child: CommonPieChart(
                    sections: CommonPieChartBuilder.buildSections([
                      PieChartSection(
                        value: SalesMockData.pieDomesticPercent.toDouble(),
                        color: const Color(0xFFFFC199),
                        title: '${SalesMockData.pieDomesticPercent}%',
                      ),
                      PieChartSection(
                        value: SalesMockData.pieInternationalPercent.toDouble(),
                        color: const Color(0xFFCCCCCC),
                        title: '${SalesMockData.pieInternationalPercent}%',
                      ),
                    ]),
                    radius: 40,
                    centerSpaceRadius: 32,
                    sectionsSpace: 0,
                    startDegreeOffset: 270,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 4,
                    children: [
                      Row(
                        spacing: 4,
                        children: [
                          Container(
                              width: 16,
                              height: 16,
                              color: const Color(0xFFFFC199)),
                          Expanded(
                            child: Text(
                              LocaleKeys.strCommonShopDomesticTransaction.tr(),
                              style: LabelTypography.sm.bold.withColor(
                                colorScheme.tertiary,
                              ),
                            ),
                          ),
                          Text(
                            ': ${SalesMockData.domesticTransactionAmount}',
                            style: LabelTypography.sm.bold.withColor(
                              colorScheme.tertiary,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        spacing: 4,
                        children: [
                          Container(
                              width: 16,
                              height: 16,
                              color: const Color(0xFFCCCCCC)),
                          Expanded(
                            child: Text(
                              LocaleKeys.strCommonShopInternationalTransaction
                                  .tr(),
                              style: LabelTypography.sm.bold.withColor(
                                colorScheme.tertiary,
                              ),
                            ),
                          ),
                          Text(
                            ': ${SalesMockData.internationalTransactionAmount}',
                            style: LabelTypography.sm.bold.withColor(
                              colorScheme.tertiary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class _MonthlySalesTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tableData = SalesMockData.tableDataDaily;

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(1),
        2: FlexColumnWidth(1.5),
      },
      border: TableBorder.symmetric(
        inside: BorderSide.none,
        outside: BorderSide.none,
      ),
      children: [
        // Header Row
        TableRow(
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
          ),
          children: const [
            TableHeaderCell(textKey: LocaleKeys.valueTitleTimeYear),
            TableHeaderCell(textKey: LocaleKeys.strCommonShopOrderCount),
            TableHeaderCell(textKey: LocaleKeys.strCommonShopOrderAmount),
            SizedBox(),
          ],
        ),
        // Body Rows
        ...List.generate(tableData.length, (i) {
          final row = tableData[i];
          final isEven = i % 2 == 0;
          final bgColor = isEven
              ? colorScheme.surfaceContainerLow
              : colorScheme.surfaceBright;

          return TableRow(
            decoration: BoxDecoration(color: bgColor),
            children: [
              TableBodyCell(text: row['dateTime']!, isBold: true),
              TableBodyCell(text: row['orders']!),
              TableBodyCell(text: row['amount']!),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: _MonthlySalesButton(),
              ),
            ],
          );
        }),
      ],
    );
  }
}

class TableHeaderCell extends StatelessWidget {
  final String textKey;

  const TableHeaderCell({super.key, required this.textKey});

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.bottom,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Text(
          textKey.tr(),
          style: BodyTypography.sm.bold,
        ),
      ),
    );
  }
}

class TableBodyCell extends StatelessWidget {
  final String text;
  final bool isBold;

  const TableBodyCell({super.key, required this.text, this.isBold = false});

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 7),
        child: Text(
          text,
          style: isBold ? BodyTypography.md.bold : BodyTypography.md,
        ),
      ),
    );
  }
}

class _MonthlySalesButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      // TODO: handle click
      onTap: () {},
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 7),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: colorScheme.onPrimary,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colorScheme.outlineVariant,
            ),
          ),
          child: SizedBox(
            height: 24,
            child: FittedBox(
              child: Text(
                LocaleKeys.strCommonShopSalesSummaryAnnual.tr(),
                style: LabelTypography.sm.withColor(
                  colorScheme.primary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
