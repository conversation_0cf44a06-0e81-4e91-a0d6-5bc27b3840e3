import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../../core/core.dart';
import 'daily_sales/daily_sales_screen.dart';
import 'monthly_sales/monthly_sales_screen.dart';
import 'yearly_sales/yearly_sales_screen.dart';

class SalesSummaryTabScreen extends StatefulWidget {
  const SalesSummaryTabScreen({super.key});

  @override
  State<SalesSummaryTabScreen> createState() => _SalesSummaryTabScreenState();
}

class _SalesSummaryTabScreenState extends State<SalesSummaryTabScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: context.pop,
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.stacked_bar_chart_outlined,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 8),
            Text(LocaleKeys.strCommonShopSalesSummary.tr()),
          ],
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: LocaleKeys.strCommonShopSalesSummaryAnnual.tr()),
            Tab(text: LocaleKeys.strCommonShopSalesSummaryMonthly.tr()),
            Tab(text: LocaleKeys.strCommonShopSalesSummaryDaily.tr()),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          YearlySalesScreen(),
          MonthlySalesScreen(),
          DailySalesScreen(),
        ],
      ),
    );
  }
}
