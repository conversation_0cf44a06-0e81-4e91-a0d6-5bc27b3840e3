import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../../core/core.dart';
import '../../mock/sales_summary_mock.dart';

class DailySalesScreen extends HookConsumerWidget {
  const DailySalesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final releaseDateController = useTextEditingController(text: '2025.12.31');
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CommonSelectDatePicker(
                controller: releaseDateController,
                initialDate: DateTime.now(),
                prefixIcon: Icon(
                  Icons.date_range_outlined,
                  size: 24,
                  color: colorScheme.outline,
                ),
                onChanged: (date) => {},
                firstDate: DateTime.now(),
                lastDate: DateTime(2050),
              ),
              CommonSalesOverviewSection(
                title: LocaleKeys.strCommonShopSalesSummary.tr(),
                numberOfOrders: SalesMockData.numberOfOrders,
                dateRange: SalesMockData.dailyRange,
                orderAmount: SalesMockData.orderAmount.toDouble(),
                finalizedSalesAmount:
                    SalesMockData.finalizedSalesAmount.toDouble(),
                itemPrice: SalesMockData.itemPrice.toDouble(),
                vat: SalesMockData.vat.toDouble(),
                salesFee: SalesMockData.salesFee.toDouble(),
              ),
              Gap(24),
              _ChartsSection(),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: CommonDottedLine(),
              ),
              _DailySalesTable(),
            ],
          ),
        ),
      ),
    );
  }
}

class _ChartsSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 24,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.strCommonShopOrderCount.tr(),
                textAlign: TextAlign.left,
                style: TitleTypography.sm.bold.withColor(
                  colorScheme.tertiary,
                ),
              ),
              SizedBox(
                height: 160,
                child: CommonPieChart(
                  sections: CommonPieChartBuilder.buildSections([
                    PieChartSection(
                      value: SalesMockData.pieDomesticPercent.toDouble(),
                      color: const Color(0xFF99F2FF),
                      title: '${SalesMockData.pieDomesticPercent}%',
                    ),
                    PieChartSection(
                      value: SalesMockData.pieInternationalPercent.toDouble(),
                      color: const Color(0xFFCCCCCC),
                      title: '${SalesMockData.pieInternationalPercent}%',
                    ),
                  ]),
                  radius: 40,
                  centerSpaceRadius: 32,
                  sectionsSpace: 0,
                  startDegreeOffset: 270,
                ),
              ),
              Gap(16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 4,
                children: [
                  Row(
                    spacing: 4,
                    children: [
                      Container(
                          width: 16,
                          height: 16,
                          color: const Color(0xFF99F2FF)),
                      Expanded(
                        child: Text(
                          LocaleKeys.strCommonShopDomesticTransaction.tr(),
                          style: LabelTypography.sm.bold.withColor(
                            colorScheme.tertiary,
                          ),
                        ),
                      ),
                      Text(
                        ': ${SalesMockData.domesticTransactionCount}',
                        style: LabelTypography.sm.bold.withColor(
                          colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    spacing: 4,
                    children: [
                      Container(
                          width: 16,
                          height: 16,
                          color: const Color(0xFFCCCCCC)),
                      Expanded(
                        child: Text(
                          LocaleKeys.strCommonShopInternationalTransaction.tr(),
                          style: LabelTypography.sm.bold.withColor(
                            colorScheme.tertiary,
                          ),
                        ),
                      ),
                      Text(
                        ': ${SalesMockData.internationalTransactionCount}',
                        style: LabelTypography.sm.bold.withColor(
                          colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        Gap(24),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.strCommonShopOrderAmount.tr(),
                textAlign: TextAlign.left,
                style: TitleTypography.sm.bold.withColor(
                  colorScheme.tertiary,
                ),
              ),
              SizedBox(
                height: 160,
                child: CommonPieChart(
                  sections: CommonPieChartBuilder.buildSections([
                    PieChartSection(
                      value: SalesMockData.pieDomesticPercent.toDouble(),
                      color: const Color(0xFFFFC199),
                      title: '${SalesMockData.pieDomesticPercent}%',
                    ),
                    PieChartSection(
                      value: SalesMockData.pieInternationalPercent.toDouble(),
                      color: const Color(0xFFCCCCCC),
                      title: '${SalesMockData.pieInternationalPercent}%',
                    ),
                  ]),
                  radius: 40,
                  centerSpaceRadius: 32,
                  sectionsSpace: 0,
                  startDegreeOffset: 270,
                ),
              ),
              Gap(16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 4,
                children: [
                  Row(
                    spacing: 4,
                    children: [
                      Container(
                          width: 16,
                          height: 16,
                          color: const Color(0xFFFFC199)),
                      Expanded(
                        child: Text(
                          LocaleKeys.strCommonShopDomesticTransaction.tr(),
                          style: LabelTypography.sm.bold.withColor(
                            colorScheme.tertiary,
                          ),
                        ),
                      ),
                      Text(
                        ': ${SalesMockData.domesticTransactionAmount}',
                        style: LabelTypography.sm.bold.withColor(
                          colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    spacing: 4,
                    children: [
                      Container(
                          width: 16,
                          height: 16,
                          color: const Color(0xFFCCCCCC)),
                      Expanded(
                        child: Text(
                          LocaleKeys.strCommonShopInternationalTransaction.tr(),
                          style: LabelTypography.sm.bold.withColor(
                            colorScheme.tertiary,
                          ),
                        ),
                      ),
                      Text(
                        ': ${SalesMockData.internationalTransactionAmount}',
                        style: LabelTypography.sm.bold.withColor(
                          colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _DailySalesTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tableData = SalesMockData.tableDataOrder;

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(1.5),
        1: FlexColumnWidth(1),
      },
      border: TableBorder.symmetric(
        inside: BorderSide.none,
        outside: BorderSide.none,
      ),
      children: [
        // Header Row
        TableRow(
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
          ),
          children: const [
            TableHeaderCell(textKey: LocaleKeys.strCommonCartOrderNumber),
            TableHeaderCell(textKey: LocaleKeys.strCommonShopOrderAmount),
            SizedBox(),
          ],
        ),
        // Body Rows
        ...List.generate(tableData.length, (i) {
          final row = tableData[i];
          final isEven = i % 2 == 0;
          final bgColor = isEven
              ? colorScheme.surfaceContainerLow
              : colorScheme.surfaceBright;

          return TableRow(
            decoration: BoxDecoration(color: bgColor),
            children: [
              TableBodyCell(text: row['orderNumber']!, isBold: true),
              TableBodyCell(text: row['amount']!),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: _DailySalesButton(),
              ),
            ],
          );
        }),
      ],
    );
  }
}

class TableHeaderCell extends StatelessWidget {
  final String textKey;

  const TableHeaderCell({super.key, required this.textKey});

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.bottom,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Text(
          textKey.tr(),
          style: BodyTypography.sm.bold,
        ),
      ),
    );
  }
}

class TableBodyCell extends StatelessWidget {
  final String text;
  final bool isBold;

  const TableBodyCell({super.key, required this.text, this.isBold = false});

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 7),
        child: Text(
          text,
          style: isBold ? BodyTypography.md.bold : BodyTypography.md,
        ),
      ),
    );
  }
}

class _DailySalesButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      // TODO: handle click
      onTap: () {},
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 7),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: colorScheme.onPrimary,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colorScheme.outlineVariant,
            ),
          ),
          child: SizedBox(
            height: 24,
            child: FittedBox(
              child: Text(
                LocaleKeys.strCommonShopTransactionDetails.tr(),
                style: LabelTypography.sm.withColor(
                  colorScheme.primary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
