import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import '../../../../../core/core.dart';
import '../../domain/entities/follower_trends_entity.dart';

class DemographicTable extends StatelessWidget {
  final List<AgeGroupData> ageGroups;
  final int total;

  const DemographicTable({
    super.key,
    required this.ageGroups,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFE6E6E6),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        children: [
          _TableHeader(total: total),
          Divider(
            height: 1,
            color: colorScheme.secondary,
          ),
          ...ageGroups.asMap().entries.map((entry) {
            final index = entry.key;
            final ageGroup = entry.value;
            final isLast = index == ageGroups.length - 1;
            final isTotalRow = index == 0;
            return Column(
              children: [
                _TableRow(
                  ageGroup: ageGroup,
                  colorScheme: colorScheme,
                  showBorder: !isLast,
                  isTotalRow: isTotalRow,
                ),
                if (!isLast) CommonDottedLine(),
              ],
            );
          }),
          Container(
            height: 1,
            color: colorScheme.primary,
          ),
          const Gap(16),
        ],
      ),
    );
  }
}

class _TableHeader extends StatelessWidget {
  final int total;

  const _TableHeader({required this.total});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.fromLTRB(4, 8, 4, 4),
      child: Column(
        children: [
          Row(
            spacing: 16,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  LocaleKeys.strCommonCartTotal.tr(),
                  style: LabelTypography.sm.bold
                      .withColor(colorScheme.onSurfaceVariant),
                ),
              ),
              Expanded(
                child: Text(
                  LocaleKeys.valueTitleProfileBioFemale.tr(),
                  style: LabelTypography.sm.bold
                      .withColor(colorScheme.onSurfaceVariant),
                ),
              ),
              Expanded(
                child: Text(
                  LocaleKeys.valueTitleProfileBioMale.tr(),
                  style: LabelTypography.sm.bold
                      .withColor(colorScheme.onSurfaceVariant),
                ),
              ),
              Expanded(
                child: Text(
                  LocaleKeys.valueDummyProfileBioGenderOther.tr(),
                  style: LabelTypography.sm.bold
                      .withColor(colorScheme.onSurfaceVariant),
                ),
              ),
            ],
          ),
          Row(
            spacing: 16,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  AppHelper.formatNumber(total),
                  style: BodyTypography.lg.withColor(colorScheme.surfaceTint),
                ),
              ),
              Expanded(
                child: Text(
                  AppHelper.formatNumber(total),
                  style: BodyTypography.lg.withColor(colorScheme.surfaceTint),
                ),
              ),
              Expanded(
                child: Text(
                  AppHelper.formatNumber(total),
                  style: BodyTypography.lg.withColor(colorScheme.surfaceTint),
                ),
              ),
              Expanded(
                child: Text(
                  AppHelper.formatNumber(total),
                  style: BodyTypography.lg.withColor(colorScheme.surfaceTint),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _TableRow extends StatelessWidget {
  final AgeGroupData ageGroup;
  final ColorScheme colorScheme;
  final bool showBorder;
  final bool isTotalRow;

  const _TableRow({
    required this.ageGroup,
    required this.colorScheme,
    this.showBorder = true,
    this.isTotalRow = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        spacing: 16,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              ageGroup.ageGroup,
              style: LabelTypography.md,
            ),
          ),
          Expanded(
            child: Text(
              AppHelper.formatNumber(ageGroup.female),
              style: LabelTypography.md.bold.withColor(colorScheme.surfaceTint),
            ),
          ),
          Expanded(
            child: Text(
              AppHelper.formatNumber(ageGroup.male),
              style: LabelTypography.md.bold.withColor(colorScheme.surfaceTint),
            ),
          ),
          Expanded(
            child: Text(
              AppHelper.formatNumber(ageGroup.other),
              style: LabelTypography.md.bold.withColor(colorScheme.surfaceTint),
            ),
          ),
        ],
      ),
    );
  }
}
