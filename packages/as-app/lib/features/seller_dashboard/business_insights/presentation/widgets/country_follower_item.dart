import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../core/core.dart';
import '../../domain/entities/follower_trends_entity.dart';
import '../providers/follower_trends_provider.dart';
import 'demographic_table.dart';

class CountryFollowerItem extends ConsumerWidget {
  final CountryFollowerData country;

  const CountryFollowerItem({
    super.key,
    required this.country,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final notifier = ref.read(followerTrendsProvider.notifier);

    return Column(
      children: [
        InkWell(
          onTap: () => notifier.toggleCountryExpansion(country.countryCode),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            child: Row(
              spacing: 16,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CountryFlag.fromCountryCode(
                    country.countryCode,
                    height: 24,
                    width: 24,
                  ),
                ),
                Expanded(
                  child: Text(
                    country.countryName,
                    style: TitleTypography.sm.bold,
                  ),
                ),
                Text(
                  AppHelper.formatNumber(country.totalFollowers),
                  style: LabelTypography.sm.bold.withColor(
                    colorScheme.surfaceTint,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    country.isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          color: colorScheme.outlineVariant,
          height: 1,
        ),
        if (country.isExpanded) ...[
          DemographicTable(
            ageGroups: country.ageGroups,
            total: country.totalFollowers,
          ),
          const Gap(8),
        ],
      ],
    );
  }
}
