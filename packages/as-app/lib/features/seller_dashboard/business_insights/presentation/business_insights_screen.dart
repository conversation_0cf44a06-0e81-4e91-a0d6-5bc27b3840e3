import 'package:asmarketplace/features/seller_dashboard/business_insights/presentation/providers/follower_trends_provider.dart';
import 'package:asmarketplace/features/seller_dashboard/business_insights/presentation/widgets/country_follower_item.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/core.dart';

class BusinessInsightsScreen extends HookConsumerWidget {
  const BusinessInsightsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final followerTrends = ref.watch(followerTrendsProvider);
    final notifier = ref.read(followerTrendsProvider.notifier);

    useEffect(() {
      Future.microtask(() {
        notifier.resetAllCountriesToCollapsed();
      });
      return null;
    }, []);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: Column(
          children: [
            _buildHeader(colorScheme),
            Gap(8),
            ...followerTrends.countries
                .map((country) => CountryFollowerItem(country: country)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ColorScheme colorScheme) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          alignment: Alignment.centerLeft,
          child: Text(
            LocaleKeys.valueTitleInsightsFollowerTrends.tr(),
            style: TitleTypography.lg.bold.withColor(colorScheme.secondary),
          ),
        ),
        Divider(
          height: 1,
          color: colorScheme.secondary,
        ),
      ],
    );
  }
}
