import 'package:country_flags/country_flags.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../core/core.dart';

class UserBlacklistScreen extends HookConsumerWidget {
  const UserBlacklistScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final itemCount = 10;
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          spacing: 2,
          children: [
            Icon(Icons.block_outlined),
            Text(LocaleKeys.strCommonTitleBlocklist.tr(),
                style: TitleTypography.lg.bold),
          ],
        ),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Icons.arrow_back),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              child: CommonSearchTextField(
                autoFocus: true,
                hint: LocaleKeys.strCommonFormInputText.tr(),
                onChanged: (value) {},
              ),
            ),
            Expanded(
              child: Builder(builder: (context) {
                if (itemCount == 0) {
                  return Center(
                    child: Text(
                      LocaleKeys.strCommonShopNoBlockedUsers.tr(),
                      style: TitleTypography.lg.bold.withColor(
                        colorScheme.tertiary.withValues(alpha: 0.5),
                      ),
                    ),
                  );
                }
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return Slidable(
                      endActionPane: ActionPane(
                        extentRatio: 0.22,
                        motion: DrawerMotion(),
                        children: [
                          SlidableAction(
                            onPressed: (context) {
                              showDialog(
                                context: context,
                                builder: (dialogContext) => CommonSuccessDialog(
                                  title: LocaleKeys
                                      .strCommonButtonActionConfirmUnblockUser
                                      .tr(),
                                  cancelText: LocaleKeys
                                      .strCommonButtonActionCancel
                                      .tr(),
                                  onConfirm: () {
                                    dialogContext.pop();
                                    // ref.read(Provider.notifier)();
                                  },
                                ),
                              );
                            },
                            backgroundColor: const Color(0xFF51B883),
                            foregroundColor: Colors.white,
                            label: LocaleKeys.strCommonButtonActionRemove.tr(),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 0, vertical: 0),
                          ),
                        ],
                      ),
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.88,
                        padding:
                            EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: colorScheme.outline),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CommonAvatarCircle(
                                  name: 'John Doe',
                                  radius: 20,
                                  backgroundColor:
                                      colorScheme.tertiaryContainer,
                                ),
                                const Gap(16),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '#nickname',
                                      style: LabelTypography.sm.bold,
                                    ),
                                    const Gap(4),
                                    Text(
                                      '#account ID No.',
                                      style: LabelTypography.sm.bold.withColor(
                                        colorScheme.tertiary,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            CountryFlag.fromCountryCode("US",
                                height: 24, width: 24),
                          ],
                        ),
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
