import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../data/mock/follower_trends_mock_data.dart';
import '../../domain/entities/follower_trends_entity.dart';

class FollowerTrendsNotifier extends StateNotifier<FollowerTrendsEntity> {
  FollowerTrendsNotifier() : super(FollowerTrendsMockData.getMockData());

  void toggleCountryExpansion(String countryCode) {
    final updatedCountries = state.countries.map((country) {
      if (country.countryCode == countryCode) {
        return country.copyWith(isExpanded: !country.isExpanded);
      }
      return country;
    }).toList();

    state = state.copyWith(countries: updatedCountries);
  }

  void resetAllCountriesToCollapsed() {
    final updatedCountries = state.countries.map((country) {
      return country.copyWith(isExpanded: false);
    }).toList();

    state = state.copyWith(countries: updatedCountries);
  }
}

final followerTrendsProvider =
    StateNotifierProvider<FollowerTrendsNotifier, FollowerTrendsEntity>(
  (ref) => FollowerTrendsNotifier(),
);
