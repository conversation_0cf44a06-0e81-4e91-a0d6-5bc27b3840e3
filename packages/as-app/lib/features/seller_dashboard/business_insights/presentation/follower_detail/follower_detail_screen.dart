import 'package:country_flags/country_flags.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../core/core.dart';
import '../../../../../router/app_router.dart';
import '../../../../report/domain/enums/report_type_enum.dart';
import '../../../../report/presentation/report_bottom_sheet.dart';
import '../../../transactions/presentation/constants/product_example.dart';
import '../../../transactions/presentation/transaction_order_detail/presentation/enum/transaction_status_enum.dart';
import '../../../transactions/presentation/transaction_order_detail/presentation/widgets/transaction_delete_bottom_sheet.dart';

class FollowerDetailScreen extends HookConsumerWidget {
  const FollowerDetailScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final totalItem = 10;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          spacing: 2,
          children: [
            Icon(Icons.account_circle_outlined),
            Text(LocaleKeys.strCommonTitleFollowerDetails.tr(),
                style: TitleTypography.lg.bold),
          ],
        ),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Icons.arrow_back),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 4, 8, 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 8,
                children: [
                  CommonAvatarCircle(
                    name: 'John Doe',
                    avatarUrl: '',
                    radius: 20,
                    backgroundColor: colorScheme.tertiaryContainer,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        spacing: 4,
                        children: [
                          CountryFlag.fromCountryCode(
                            'US',
                            height: 16,
                            width: 16,
                          ),
                          Text(
                            '#nickname',
                            style: BodyTypography.lg.bold,
                          ),
                        ],
                      ),
                      Row(
                        spacing: 2,
                        children: [
                          Text(
                            '#account ID No.',
                            style: LabelTypography.md.withColor(
                              colorScheme.tertiary,
                            ),
                          ),
                          InkWell(
                            onTap: () => Clipboard.setData(
                                ClipboardData(text: '#account ID No.')),
                            child: Icon(
                              Icons.copy,
                              size: 14,
                              color: colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 51,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 8,
                    children: [
                      Text(
                        LocaleKeys.valueTitleProfileBioGender.tr(),
                        style: LabelTypography.sm.bold.withColor(
                          colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        'Male',
                        style: BodyTypography.md,
                      ),
                    ],
                  ),
                  Column(
                    spacing: 8,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.valueTitleProfileBioAge.tr(),
                        style: LabelTypography.sm.bold.withColor(
                          colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        '25',
                        style: BodyTypography.md,
                      ),
                    ],
                  )
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                spacing: 11,
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 32,
                      child: Builder(builder: (context) {
                        // if (isBlocked) {
                        //   return ElevatedButton.icon(
                        //     onPressed: () {},
                        //     style: ElevatedButton.styleFrom(
                        //       backgroundColor: Color(0xffFF5454),
                        //     ),
                        //     label: Text(
                        //       LocaleKeys.strCommonButtonActionLabelBlockedUser
                        //           .tr(),
                        //       style: LabelTypography.sm.bold
                        //           .withColor(colorScheme.onPrimary),
                        //     ),
                        //     icon: Icon(
                        //       Icons.block_outlined,
                        //     ),
                        //   );
                        // }

                        // if (status?.isCanceled ?? false) ...[
                        //     Icon(
                        //       Icons.error,
                        //       size: 14,
                        //       color: colorScheme.error,
                        //     ),
                        //     const Gap(2),
                        // ],

                        return OutlinedButton.icon(
                          onPressed: () {
                            context.bottomSheet(
                              builder: (contextBottom) =>
                                  TransactionDeleteBottomSheet(),
                            );
                          },
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(
                              color: Color(0xffFF5454),
                            ),
                          ),
                          label: Text(
                            LocaleKeys.strCommonButtonActionBlock.tr(),
                            style: LabelTypography.sm.bold
                                .withColor(Color(0xffFF5454)),
                          ),
                          icon: Icon(
                            Icons.block_outlined,
                            color: Color(0xffFF5454),
                          ),
                        );
                      }),
                    ),
                  ),
                  Expanded(
                    child: SizedBox(
                      height: 32,
                      child: OutlinedButton.icon(
                        onPressed: () => context.bottomSheet(
                          builder: (context) => ReportBottomSheet(
                            reportType: ReportType.user,
                            id: '', // TODO: mapping id
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: colorScheme.onError,
                          foregroundColor: Color(0xFFFF5454),
                          side: BorderSide(color: Color(0xFFFF5454)),
                        ),
                        icon: Icon(
                          Icons.feedback_outlined,
                        ),
                        label: Text(
                          LocaleKeys.strCommonShopReportUser.tr(),
                          style: LabelTypography.sm.bold.withColor(
                            Color(0xFFFF5454),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Gap(24),
            CommonDottedLine(),
            Gap(24),
            Text(
              LocaleKeys.strCommonShopTransactions.tr(),
              style: TitleTypography.md.bold,
            ),
            Gap(4),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 23),
                child: Builder(builder: (context) {
                  if (totalItem == 0) {
                    return Center(
                      child: Text(
                        LocaleKeys.strCommonShopNoTransactionHistory.tr(),
                        style: TitleTypography.lg.bold.withColor(
                          colorScheme.tertiary.withValues(alpha: 0.5),
                        ),
                      ),
                    );
                  }
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: 2,
                          separatorBuilder: (context, index) {
                            return Divider();
                          },
                          itemBuilder: (context, index) {
                            return CommonItemTransaction(
                              transactionStatus: TransactionStatusEnum.onHold,
                              onTap: () {
                                context.pushNamed(
                                    AppRoutes.transactionDetail.name);
                              },
                              isError: true,
                              product: productExample,
                            );
                          },
                        ),
                        ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: totalItem,
                          separatorBuilder: (context, index) {
                            return Divider();
                          },
                          itemBuilder: (context, index) {
                            return CommonItemTransaction(
                                transactionStatus: TransactionStatusEnum.onHold,
                                onTap: () {
                                  context.pushNamed(
                                      AppRoutes.transactionDetail.name);
                                },
                                product: productExample);
                          },
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
