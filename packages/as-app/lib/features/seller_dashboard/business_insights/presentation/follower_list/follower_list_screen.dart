import 'package:country_flags/country_flags.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../core/core.dart';
import '../../../../../../router/app_router.dart';
import '../../../transactions/presentation/transaction_order_detail/presentation/widgets/transaction_delete_bottom_sheet.dart';

class FollowerListScreen extends HookConsumerWidget {
  const FollowerListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final itemCount = 10;
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          spacing: 2,
          children: [
            Icon(Icons.favorite_outline),
            Text(LocaleKeys.strCommonTitleFollowers.tr(),
                style: TitleTypography.lg.bold),
          ],
        ),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Icons.arrow_back),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.group_outlined),
                const Gap(4),
                Text(
                  AppHelper.formatNumber(1234),
                  style: TitleTypography.md.bold.withColor(
                    colorScheme.tertiary,
                  ),
                ),
                const Gap(4),
                Text(
                  LocaleKeys.strCommonTitleFollowers.tr(),
                  style: TitleTypography.md.bold.withColor(
                    colorScheme.tertiary,
                  ),
                ),
              ],
            ),
            Gap(11),
            Divider(),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              child: CommonSearchTextField(
                autoFocus: true,
                hint: LocaleKeys.strCommonFormInputText.tr(),
                onChanged: (value) {},
              ),
            ),
            Expanded(
              child: Builder(builder: (context) {
                if (itemCount == 0) {
                  return Center(
                    child: Text(
                      LocaleKeys.strCommonShopNoUsersFavorited.tr(),
                      style: TitleTypography.lg.bold.withColor(
                        colorScheme.tertiary.withValues(alpha: 0.5),
                      ),
                    ),
                  );
                }
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return Slidable(
                      endActionPane: ActionPane(
                        extentRatio: 0.12,
                        motion: DrawerMotion(),
                        children: [
                          SlidableAction(
                            onPressed: (context) {
                              context.bottomSheet(
                                builder: (contextBottom) =>
                                    TransactionDeleteBottomSheet(),
                              );
                            },
                            backgroundColor: Color(0xffFF7171),
                            foregroundColor: colorScheme.onError,
                            icon: Icons.block_outlined,
                            padding: EdgeInsets.symmetric(
                              horizontal: 10,
                            ),
                          ),
                        ],
                      ),
                      child: GestureDetector(
                        onTap: () =>
                            context.pushNamed(AppRoutes.followerDetail.name),
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.88,
                          padding:
                              EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: colorScheme.outline),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  CommonAvatarCircle(
                                    name: 'John Doe',
                                    radius: 20,
                                    backgroundColor:
                                        colorScheme.tertiaryContainer,
                                  ),
                                  const Gap(16),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '#nickname',
                                        style: LabelTypography.sm.bold,
                                      ),
                                      const Gap(4),
                                      Text(
                                        '#account ID No.',
                                        style:
                                            LabelTypography.sm.bold.withColor(
                                          colorScheme.tertiary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              CountryFlag.fromCountryCode("US",
                                  height: 24, width: 24),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
