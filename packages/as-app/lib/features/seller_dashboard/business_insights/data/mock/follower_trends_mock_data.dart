import '../../domain/entities/follower_trends_entity.dart';

class FollowerTrendsMockData {
  static List<AgeGroupData> _getDefaultAgeGroups() {
    return [
      const AgeGroupData(
        ageGroup: 'Under 20',
        female: 1234,
        male: 1234,
        other: 1234,
      ),
      const AgeGroupData(
        ageGroup: '20-29',
        female: 1234,
        male: 1234,
        other: 1234,
      ),
      const AgeGroupData(
        ageGroup: '30-39',
        female: 1234,
        male: 1234,
        other: 1234,
      ),
      const AgeGroupData(
        ageGroup: '40-49',
        female: 1234,
        male: 1234,
        other: 1234,
      ),
      const AgeGroupData(
        ageGroup: '50-59',
        female: 1234,
        male: 1234,
        other: 1234,
      ),
      const AgeGroupData(
        ageGroup: '60 and over',
        female: 1234,
        male: 1234,
        other: 1234,
      ),
    ];
  }

  static FollowerTrendsEntity getMockData() {
    return FollowerTrendsEntity(
      countries: [
        CountryFollowerData(
          countryCode: 'AU',
          countryName: 'Australia',
          totalFollowers: 1234,
          isExpanded: true,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'FR',
          countryName: 'France',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'IT',
          countryName: 'Italy',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'JP',
          countryName: 'Japan',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'MC',
          countryName: 'Monaco',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'SA',
          countryName: 'Saudi Arabia',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'SG',
          countryName: 'Singapore',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'KR',
          countryName: 'South Korea',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'ES',
          countryName: 'Spain',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'AE',
          countryName: 'United Arab Emirates',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'GB',
          countryName: 'United Kingdom',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
        CountryFollowerData(
          countryCode: 'US',
          countryName: 'United States',
          totalFollowers: 1234,
          isExpanded: false,
          ageGroups: _getDefaultAgeGroups(),
        ),
      ],
    );
  }
}
