class FollowerTrendsEntity {
  final List<CountryFollowerData> countries;

  const FollowerTrendsEntity({
    required this.countries,
  });

  FollowerTrendsEntity copyWith({
    List<CountryFollowerData>? countries,
  }) {
    return FollowerTrendsEntity(
      countries: countries ?? this.countries,
    );
  }
}

class CountryFollowerData {
  final String countryCode;
  final String countryName;
  final int totalFollowers;
  final List<AgeGroupData> ageGroups;
  final bool isExpanded;

  const CountryFollowerData({
    required this.countryCode,
    required this.countryName,
    required this.totalFollowers,
    required this.ageGroups,
    this.isExpanded = false,
  });

  CountryFollowerData copyWith({
    String? countryCode,
    String? countryName,
    int? totalFollowers,
    List<AgeGroupData>? ageGroups,
    bool? isExpanded,
  }) {
    return CountryFollowerData(
      countryCode: countryCode ?? this.countryCode,
      countryName: countryName ?? this.countryName,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      ageGroups: ageGroups ?? this.ageGroups,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

class AgeGroupData {
  final String ageGroup;
  final int female;
  final int male;
  final int other;

  const AgeGroupData({
    required this.ageGroup,
    required this.female,
    required this.male,
    required this.other,
  });
}
