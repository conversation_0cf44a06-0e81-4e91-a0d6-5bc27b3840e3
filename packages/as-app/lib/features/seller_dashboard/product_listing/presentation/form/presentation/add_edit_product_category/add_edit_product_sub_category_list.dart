import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../core/core.dart';
import '../../../../../../../router/app_router.dart';
import '../../../../../../product_search/search_by_category/domain/entities/product_category_entity.dart';
import '../providers/product_form.dart';
import 'providers/select_category.dart';
import 'widgets/add_edit_product_category_item_widget.dart';

class AddEditProductSubCategoryListScreen extends HookConsumerWidget {
  final ProductCategoryEntity category;

  const AddEditProductSubCategoryListScreen({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productForm = ref.watch(productFormProvider.notifier);
    final categoryState = ref.watch(selectCategoryProvider);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          category.multiLanguageName
              .getLocalizedName(context.locale.languageCode),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: SafeArea(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: category.subCategories.length,
            itemBuilder: (context, index) {
              final subCategory = category.subCategories[index];
              return AddEditProductCategoryItemWidget(
                name: subCategory.multiLanguageName
                    .getLocalizedName(context.locale.languageCode),
                subName: subCategory.multiLanguageName
                    .getLocalizedName(context.locale.languageCode),
                onPressed: () {
                  if (subCategory.subCategories.isNotEmpty) {
                    context.pushNamed(
                      AppRoutes.addEditProductSubCategory.name,
                      extra: subCategory,
                    );
                  } else {
                    productForm.onChangeSubCategory(subCategory);
                    productForm.onChangeParentCategory(categoryState);
                    GoRouter.of(context).popUntilPath(
                        AppRoutes.productListingForm.initLocation);
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
