import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../business_insights/presentation/business_insights_screen.dart';
import '../../product_listing/presentation/manage/manage_product_listing_screen.dart';
import '../../../../core/core.dart';
import '../../sales_summary/presentation/sales_summary_screen.dart';
import '../../transactions/presentation/transaction_screen.dart';
import '../domain/enums/shop_manager_tab_enum.dart';

class SellerDashboardScreen extends HookConsumerWidget {
  const SellerDashboardScreen({super.key, this.initialTab});
  final ShopManagerTabEnum? initialTab;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useAutomaticKeepAlive();
    final tabController = useTabController(initialLength: 5);

    useEffect(() {
      if (initialTab != null) {
        tabController.animateTo(initialTab!.indexTab);
      }
      return null;
    }, [initialTab]);

    return Column(
      children: [
        _TabBar(
          tabController: tabController,
        ),
        Expanded(
          child: TabBarView(
            controller: tabController,
            children: [
              const Center(child: Text('Dashboard')),
              const ManageProductListingScreen(),
              const TransactionScreen(),
              const SalesSummaryScreen(),
              const BusinessInsightsScreen(),
            ],
          ),
        ),
      ],
    );
  }
}

class _TabBar extends HookWidget {
  final TabController tabController;
  const _TabBar({required this.tabController});
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Material(
      // TODO: change this
      color: Color(0xFFDFE7E6),
      child: SizedBox(
        height: 48,
        child: TabBar(
          controller: tabController,
          dividerColor: colorScheme.outlineVariant,
          indicator: UnderlineTabIndicator(
            borderSide: BorderSide(
              color: colorScheme.primary,
              width: 3,
            ),
          ),
          isScrollable: true,
          tabs: [
            Tab(
              text: LocaleKeys.strCommonTitleDashboard.tr(),
            ),
            Tab(
              text: LocaleKeys.strCommonShopListing.tr(),
            ),
            Tab(
              text: LocaleKeys.strCommonShopTransactions.tr(),
            ),
            Tab(
              text: LocaleKeys.strCommonShopSalesSummary.tr(),
            ),
            Tab(
              text: LocaleKeys.strCommonTitleInsights.tr(),
            ),
          ],
        ),
      ),
    );
  }
}
