import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../core/core.dart';
import '../providers/transaction_order_detail_provider.dart';

class TransactionDeleteBottomSheet extends HookConsumerWidget {
  const TransactionDeleteBottomSheet({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      height: 330,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(8),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(24),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 4,
                vertical: 8,
              ),
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: colorScheme.secondary,
                    width: 2,
                  ),
                ),
              ),
              child: Text(
                LocaleKeys.strCommonButtonActionConfirmBlockUser.tr(),
                style: TitleTypography.lg.bold.withColor(
                  colorScheme.secondary,
                ),
              ),
            ),
            const Gap(24),
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonAvatarCircle(
                    name: 'Nickname',
                    radius: 20,
                    backgroundColor: colorScheme.tertiaryContainer,
                  ),
                  const Gap(16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.valueTitleProfileNameNickname.tr(),
                        style: TitleTypography.sm.bold.withColor(
                          colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '#account ID No.',
                        style: LabelTypography.sm.withColor(
                          colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                vertical: 28,
              ),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerLowest,
                border: Border(
                  top: BorderSide(
                    color: colorScheme.outlineVariant,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: context.pop,
                      child: Text(
                        LocaleKeys.strCommonButtonControllerBack.tr(),
                      ),
                    ),
                  ),
                  Gap(11),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        ref
                            .read(
                                transactionOrderDetailProviderProvider.notifier)
                            .changeIsBlocked(true);
                        context.pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xffFF5454),
                      ),
                      icon: Icon(
                        Icons.block_outlined,
                        color: colorScheme.onPrimary,
                      ),
                      label: Text(
                        LocaleKeys.strCommonButtonActionBlock.tr(),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
