import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/core.dart';
import 'seller_info/providers/seller_info.dart';
import 'seller_info/providers/seller_products.dart';
import 'seller_info/seller_info_tab.dart';
import 'shop_info/shop_info_tab.dart';

class SellerTopPageScreen extends HookConsumerWidget {
  final String? sellerId;

  const SellerTopPageScreen({
    super.key,
    this.sellerId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useAutomaticKeepAlive();

    final tabController = useTabController(initialLength: 2);
    final sellerInfoNotifier = ref.read(sellerInfoProvider.notifier);
    final sellerProductsNotifier = ref.read(sellerProductsProvider.notifier);
    final sellerInfoState = ref.watch(sellerInfoProvider);

    useEffect(() {
      Future.microtask(() {
        sellerInfoNotifier.initState(sellerId ?? '');
        sellerProductsNotifier.initState(sellerId ?? '');
        sellerInfoNotifier.fetchData();
        sellerProductsNotifier.fetchData();
      });
      return null;
    }, [sellerId]);

    return SafeArea(
      top: false,
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: context.pop,
          ),
          title: Text(sellerInfoState.seller?.shopName ?? ''),
          bottom: TabBar(
            controller: tabController,
            tabs: [
              Tab(text: LocaleKeys.strCommonShopListing.tr()),
              Tab(text: LocaleKeys.strCommonShopShopDetails.tr()),
            ],
          ),
        ),
        body: TabBarView(
          controller: tabController,
          children: [
            SellerInfoTab(sellerId: sellerId),
            ShopInfoTab(sellerId: sellerId),
          ],
        ),
      ),
    );
  }
}
