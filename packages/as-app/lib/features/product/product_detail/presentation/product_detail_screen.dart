import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:openapi/openapi.dart';

import '../../../../core/core.dart';
import '../../../shopping_cart/cart_products/presentation/providers/cart_products_provider.dart';
import 'providers/favorite_product_provider.dart';
import 'providers/product_detail_provider.dart';
import 'states/add_to_cart_state.dart';
import 'states/favorite_product_state.dart';
import 'states/product_detail_state.dart';
import 'widgets/add_to_cart_success_bottom_sheet.dart';
import 'widgets/other_items_widget.dart';
import 'widgets/product_actions_widget.dart';
import 'widgets/product_information_widget.dart';
import 'widgets/shop_details_widget.dart';

class ProductDetailScreen extends HookConsumerWidget {
  const ProductDetailScreen({
    super.key,
    required this.productId,
  });

  final String productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final size = MediaQuery.of(context).size;
    final colorScheme = Theme.of(context).colorScheme;
    final currentIndex = useState(0);
    final detailNotifier = ref.read(productDetailProvider.notifier);

    final state = ref.watch(productDetailProvider);
    final product = state.product;
    final isUpcoming = product.releaseDateTime != null &&
        product.releaseDateTime!.isAfter(DateTime.now());

    // Fetch product data when screen loads, using the initial product from route
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(productDetailProvider.notifier).fetchProductData(productId);
      });
      return null;
    }, [productId]);

    // Listen to favorite product state changes
    ref.listen(favoriteProductProvider, (previous, next) {
      if (next.isLoading) {
        EasyLoading.show();
      } else {
        EasyLoading.dismiss();
      }
    });

    ref.listen(addToCartProvider, (previous, next) {
      if (next.isLoading) {
        EasyLoading.show();
      } else {
        EasyLoading.dismiss();
      }
      next.maybeMap(
        orElse: () {},
        success: (state) {
          context.bottomSheet(
            builder: (context) => AddToCartSuccessBottomSheet(
              product: product,
            ),
          );
          ref.read(cartProductsProvider.notifier).getCartItems();
        },
        error: (state) {
          context.dialog(
            builder: (context) => CommonErrorDialog(
              title: LocaleKeys.error.tr(),
              description: LocaleKeys.defaultApiError.tr(),
            ),
          );
        },
      );
    });

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: context.pop,
        ),
        title: Text(
          product.name?.getLocalizedName(context.locale.languageCode) ?? '',
        ),
        actions: [
          IconButton(
            onPressed: () async => detailNotifier.shareProduct(context),
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      floatingActionButton: product.salesStatus == ProductSalesStatus.sold ||
              (isUpcoming &&
                  product.salesStatus == ProductSalesStatus.available)
          ? null
          : state.maybeMap(
              orElse: () => null,
              success: (state) => FloatingActionButton(
                foregroundColor: AppColors.white,
                backgroundColor: Color(0xFFFF8D44),
                onPressed: () => ref
                    .read(addToCartProvider.notifier)
                    .addToCart(productId: productId),
                child: Consumer(
                  builder: (context, ref, child) {
                    final stateAddCart = ref.watch(addToCartProvider);
                    return stateAddCart.maybeMap(
                      orElse: () => const Icon(Icons.add_shopping_cart),
                      loading: (state) => const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
      body: Column(
        children: [
          CommonLinearProgressIndicator(isLoading: state.isLoading),
          Expanded(
            child: state.maybeMap(
              orElse: () => SingleChildScrollView(
                child: Column(
                  children: [
                    if (product.images?.isNotEmpty == true)
                      Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          CarouselSlider(
                            options: CarouselOptions(
                              height: size.width,
                              autoPlay: true,
                              viewportFraction: 1,
                              onPageChanged: (index, reason) {
                                currentIndex.value = index;
                              },
                            ),
                            items: (product.images ?? [])
                                .map(
                                  (e) => CommonCacheNetworkImage(
                                    url: e,
                                    width: size.width,
                                    height: size.width,
                                    fit: BoxFit.fitWidth,
                                  ),
                                )
                                .toList(),
                          ),
                          Positioned(
                            bottom: 16,
                            child: DotsIndicator(
                              dotsCount: product.images?.length ?? 0,
                              position: currentIndex.value.toDouble(),
                              onTap: (index) {},
                              decorator: DotsDecorator(
                                color: AppColors.white,
                                activeColor: colorScheme.onSurfaceVariant,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  side: BorderSide(
                                    color: colorScheme.onSurfaceVariant,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    const ProductInformationWidget(),
                    const ProductActionsWidget(),
                    const ShopDetailsWidget(),
                    const OtherItemsWidget(),
                  ],
                ),
              ),
              error: (state) => Center(
                child: Text(
                  'Item not found.',
                  style: TitleTypography.lg.bold.withColor(
                    colorScheme.tertiary,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
