import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../core/core.dart';
import '../../../../../../router/app_router.dart';
import '../../../../../../services/services.dart';
import '../../../../../authentication/login/presentation/login_bottom_sheet.dart';
import '../../../../../profile/profile/domain/entities/extra_profile_entity.dart';
import '../../../../../profile/profile/domain/enums/profile_tab_enum.dart';
import '../../../../../seller_dashboard/dashboard/domain/enums/shop_manager_tab_enum.dart';
import '../providers/all_items.dart';

class SellerDashboardWidget extends HookConsumerWidget {
  const SellerDashboardWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider).value == true;
    final isSeller = ref.watch(userInfoProvider).value?.isSeller == true;

    return !isAuthenticated
        ? const _AccountGuestWidget()
        : isSeller
            ? const _SellerInfoWidget()
            : const SizedBox.shrink();
  }
}

class _SellerInfoWidget extends HookConsumerWidget {
  const _SellerInfoWidget();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final state = ref.watch(sellerDashboardProvider);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(sellerDashboardProvider.notifier).fetchData();
      });
      return null;
    }, []);

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 24,
      ),
      child: Column(
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              return IntrinsicHeight(
                child: Row(
                  children: [
                    _SellerActionWidget(
                      icon: Icons.storefront_outlined,
                      label: LocaleKeys.strCommonShopShopManager.tr(),
                      onTap: () => context.goNamed(
                        AppRoutes.me.name,
                        extra: ExtraProfileEntity(
                          profileTab: ProfileTabEnum.shopManager,
                          shopManagerTab: ShopManagerTabEnum.dashboard,
                        ),
                      ),
                      width: constraints.maxWidth / 4,
                    ),
                    _SellerActionWidget(
                      icon: Icons.receipt_long_outlined,
                      label: LocaleKeys.strCommonShopTransactions.tr(),
                      onTap: () => context.goNamed(
                        AppRoutes.me.name,
                        extra: ExtraProfileEntity(
                          profileTab: ProfileTabEnum.shopManager,
                          shopManagerTab: ShopManagerTabEnum.transactions,
                        ),
                      ),
                      width: constraints.maxWidth / 4,
                    ),
                    _SellerActionWidget(
                      icon: Icons.favorite,
                      label: LocaleKeys.strCommonTitleFollowers.tr(),
                      onTap: () =>
                          context.pushNamed(AppRoutes.followerList.name),
                      width: constraints.maxWidth / 4,
                    ),
                    _SellerActionWidget(
                      icon: Icons.local_atm,
                      label: LocaleKeys.strCommonShopWithdrawSales.tr(),
                      onTap: () => context.pushNamed(
                        AppRoutes.withdrawEarnings.name,
                      ),
                      width: constraints.maxWidth / 4,
                    ),
                  ],
                ),
              );
            },
          ),
          const Gap(16),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFDFE7E7),
              borderRadius: BorderRadius.circular(4),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.strCommonShopPreviousDayResult.tr(),
                  style: TitleTypography.md.bold.withColor(colorScheme.primary),
                ),
                const Gap(13),
                Row(
                  spacing: 16,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.valueTitleInsightsViews.tr(),
                          style: LabelTypography.sm.bold
                              .withColor(colorScheme.tertiary),
                        ),
                        const Gap(2),
                        Text(
                          state?.displayViews ?? '0',
                          style: BodyTypography.md,
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.strCommonButtonActionFavorited.tr(),
                          style: LabelTypography.sm.bold
                              .withColor(colorScheme.tertiary),
                        ),
                        const Gap(2),
                        IntrinsicHeight(
                          child: Row(
                            children: [
                              Text(
                                LocaleKeys.strCommonShopShop.tr(),
                                style: BodyTypography.md.bold,
                              ),
                              const Gap(4),
                              Text(
                                state?.displayShopFavorites ?? '0',
                                style: BodyTypography.md,
                              ),
                              const Gap(8),
                              Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 2),
                                width: 1,
                                height: 16,
                                color: colorScheme.outline,
                              ),
                              const Gap(8),
                              Text(
                                LocaleKeys.strCommonShopItem.tr(),
                                style: BodyTypography.md.bold,
                              ),
                              const Gap(4),
                              Text(
                                state?.displayProductFavorites ?? '0',
                                style: BodyTypography.md,
                              ),
                              const Gap(8),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const Gap(6),
                Row(
                  spacing: 8,
                  children: [
                    _AmountInfoWidget(
                      amount: state?.orderAmountTaxExcluded ?? 0,
                      label: LocaleKeys.strCommonShopOrderAmount.tr(),
                      subLabel: LocaleKeys.valueTitleShopItemTaxExcluded.tr(),
                    ),
                    _AmountInfoWidget(
                      amount: state?.orderAmountTaxIncluded ?? 0,
                      label: LocaleKeys.strCommonShopOrderAmount.tr(),
                      subLabel: LocaleKeys.valueTitleShopItemTaxIncluded.tr(),
                    ),
                  ],
                ),
                const Gap(6),
                _AmountInfoWidget(
                  amount: state?.salesAmount ?? 0,
                  label: LocaleKeys.strCommonShopFinalizedSales.tr(),
                  subLabel: LocaleKeys.valueTitleShopItemTaxIncluded.tr(),
                ),
              ],
            ),
          ),
          const Gap(16),
          SizedBox(
            height: 32,
            child: OutlinedButton.icon(
              onPressed: () {
                final sellerId = ref.read(userInfoProvider).value?.sellerId;
                context.pushNamed(
                  AppRoutes.sellerTopPage.name,
                  extra: sellerId,
                );
              },
              label: Text(LocaleKeys.strCommonShopGotoShopPage.tr()),
              icon: const Icon(Icons.storefront_outlined),
            ),
          ),
        ],
      ),
    );
  }
}

class _SellerActionWidget extends StatelessWidget {
  const _SellerActionWidget({
    required this.icon,
    required this.label,
    required this.onTap,
    this.width = 64,
  });
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final double width;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return GestureDetector(
      onTap: onTap,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: width,
          maxWidth: width,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: colorScheme.primary,
              child: Icon(
                icon,
                size: 24,
                color: colorScheme.onPrimary,
              ),
            ),
            const Gap(4),
            Text(
              label,
              style: LabelTypography.sm.bold,
            ),
          ],
        ),
      ),
    );
  }
}

class _AmountInfoWidget extends StatelessWidget {
  const _AmountInfoWidget({
    required this.amount,
    required this.label,
    required this.subLabel,
  });
  final double amount;
  final String label;
  final String subLabel;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: LabelTypography.sm.bold.withColor(colorScheme.tertiary),
            ),
            const Gap(4),
            Text(
              subLabel,
              style: LabelTypography.sm,
            ),
          ],
        ),
        const Gap(2),
        Text(
          AppHelper.formatCurrency(amount),
          style: BodyTypography.md,
        ),
      ],
    );
  }
}

class _AccountGuestWidget extends StatelessWidget {
  const _AccountGuestWidget();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 24,
      ),
      decoration: BoxDecoration(
        color: Color(0xFFF2F2F2),
      ),
      child: Row(
        spacing: 11,
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => context.goNamed(AppRoutes.register.name),
              label: Text(LocaleKeys.strCommonRegistrationRegistration.tr()),
              icon: const Icon(Icons.edit),
            ),
          ),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => context.bottomSheet(
                isScrollControlled: true,
                isDismissible: true,
                builder: (_) => const LoginBottomSheet(),
              ),
              label: Text(LocaleKeys.strCommonButtonActionLogin.tr()),
              icon: const Icon(Icons.login),
            ),
          ),
        ],
      ),
    );
  }
}
