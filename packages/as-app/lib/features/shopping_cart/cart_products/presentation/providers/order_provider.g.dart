// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderHash() => r'da61871ae01600fc3e138225f24d42f43295f1f8';

/// See also [Order].
@ProviderFor(Order)
final orderProvider = AutoDisposeNotifierProvider<Order, OrderState>.internal(
  Order.new,
  name: r'orderProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$orderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Order = AutoDisposeNotifier<OrderState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
