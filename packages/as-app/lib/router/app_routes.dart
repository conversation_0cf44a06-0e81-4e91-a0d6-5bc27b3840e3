part of 'app_router.dart';

enum AppRoutes {
  splash,
  passwordResetConfirm,
  register,
  servicesOverviewBuyer,
  servicesOverviewSeller,
  termsAndConditionBuyer,
  termsAndConditionSeller,
  registerUser,
  confirmRegistrationBuyer,
  confirmRegistrationSeller,
  completedRegistrationBuyer,
  completedRegistrationSeller,
  displayLanguage,
  selectCountry,
  registerEmail,
  sendRegisterEmailSuccess,
  informationRegisterFormBuyer,
  informationRegisterFormSeller,
  subscriptionPurchaseBuyer,
  subscriptionPurchaseSeller,
  verificationIdentityBuyer,
  verificationSeller,
  verificationIdentitySeller,
  verificationBusinessSeller,
  accountRegistrationSeller,
  accountRegistrationErrorSeller,
  paymentMethodBuyer,
  paymentMethodSeller,
  dashboard,
  home,
  searchByKeyword,
  searchByCategory,
  searchBySubCategory,
  searchByBrand,
  searchByCountry,
  notification,
  notificationManagementDetail,
  notificationMaintenanceDetail,
  help,
  legalNotice,
  helpCenter,
  functionalityOverview,
  functionalityOverviewDetail,
  buyingGuidelines,
  buyingGuidelineDetail,
  salesGuidelines,
  salesGuidelineDetail,
  termsAndPolices,
  communityGuidelines,
  privacyPolicy,
  termsOfService,
  paymentTerms,
  buyerProtectionPolicy,
  sctDisclosure,
  inquiry,
  inquirySelectType,
  inquiryServiceForm,
  inquiryServiceReview,
  inquiryAreaRequest,
  messages,
  searchMessage,
  profileSettings,
  profileGeneral,
  membershipStatus,
  basicInformation,
  basicInformationUpdate,
  changeEmailAddress,
  paymentMethod,
  enableSeller,
  subscriptionManagement,
  subscriptionPurchase,
  subscriptionPurchaseError,
  sellerProfile,
  selectFavoriteBrand,
  sellerBasicInformation,
  identityAndBusinessInfo,
  businessVerificationError,
  identityVerificationError,
  bankInfoUpdate,
  bankInfoUpdateError,
  pinCode,
  currentPinForm,
  currentPinError,
  newPinForm,
  confirmNewPinForm,
  changePassword,
  changePasswordError,
  favorite,
  favoriteSellerList,
  favoriteProductList,
  sellerTopPage,
  sellerDashboard,
  productListingForm,
  updateProduct,
  productDetail,
  productSearch,
  productSearchResult,
  shareProduct,
  deleteProduct,
  salesHistoryList,
  salesSummary,
  salesSummaryTab,
  paymentTransfer,
  paymentErrorHandler,
  pinUnlockForm,
  pinAuthError,
  paymentDetailForm,
  confirmPaymentDetail,
  paymentSuccess,
  insights,
  followerInsights,
  country,
  gender,
  age,
  followerList,
  buyerProfile,
  userBlacklist,
  unblockUser,
  storeManagement,
  storePendingTransaction,
  confirmStorePublish,
  storePublishSuccess,
  shoppingCart,
  cartProducts,
  cartProductsReview,
  buyerContact,
  purchaseConfirmation,
  purchaseSubscription,
  paymentProcess,
  paymentProcessFlow,
  paymentProcessError,
  purchaseComplete,
  citesApplicationProcess,
  citesPaymentProcess,
  provisionalPurchaseCompleted,
  purchaseHistory,
  purchaseHistoryDetail,
  categories,
  me,
  accountDetails,
  profileDetail,
  mailAddress,
  languageSetting,
  notificationSettings,
  registerReview,
  premiumMembership,
  updatedTermsOfService,
  addEditProductCategory,
  addEditProductSubCategory,
  addProductBrand,
  productListingReview,
  onSaleProductDetail,
  transactionDetail,
  followerDetail,
  blocklist,
  faqArticle,
  publishShop,
  noticesDetail,
  withdrawEarnings,
  cancelAccount,
  cancelAccountComplete;

  /// Get route name in param case starting with '/'
  String get initLocation => switch (this) {
        splash => '/',
        _ => '/${name.toParamCase()}',
      };

  /// Get route name in param case
  String get location => name.toParamCase();
}
