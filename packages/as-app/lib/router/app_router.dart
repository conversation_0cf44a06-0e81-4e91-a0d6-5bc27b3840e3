import 'package:change_case/change_case.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:openapi/openapi.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../core/core.dart';
import '../features/app/presentation/dashboard/dashboard_screen.dart';
import '../features/authentication/password_reset/presentation/password_reset_confirm/password_reset_confirm_screen.dart';
import '../features/authentication/register/presentation/buyer/completed_registration_buyer_screen.dart';
import '../features/authentication/register/presentation/buyer/confirm_registration_buyer_screen.dart';
import '../features/authentication/register/presentation/buyer/information_register_form_buyer_screen.dart';
import '../features/authentication/register/presentation/buyer/subscription_purchase_buyer_screen.dart';
import '../features/authentication/register/presentation/buyer/verification_identity_buyer_screen.dart';
import '../features/authentication/register/presentation/language/display_language_screen.dart';
import '../features/authentication/register/presentation/seller/completed_registration_seller_screen.dart';
import '../features/authentication/register/presentation/seller/confirm_registration_seller_screen.dart';
import '../features/authentication/register/presentation/seller/information_register_form_seller_screen.dart';
import '../features/authentication/register/presentation/seller/register_account_error_seller_screen.dart';
import '../features/authentication/register/presentation/seller/register_account_seller_screen.dart';
import '../features/authentication/register/presentation/seller/subscription_purchase_seller_screen.dart';
import '../features/authentication/register/presentation/seller/verification_business_seller_screen.dart';
import '../features/authentication/register/presentation/seller/verification_identity_seller_screen.dart';
import '../features/authentication/register/presentation/seller/verification_seller_screen.dart';
import '../features/authentication/register/register_dashboard/presentation/register_dashboard_screen.dart';
import '../features/authentication/register/register_email/domain/entities/jwt_token_register_email_entity.dart';
import '../features/authentication/register/register_email/presentation/register_email_screen.dart';
import '../features/authentication/register/register_email/presentation/send_confirmation_email_success_screen.dart';
import '../features/authentication/register/register_user/domain/entities/register_user_entity.dart';
import '../features/authentication/register/register_user/presentation/register_user_review/presentation/register_user_review_screen.dart';
import '../features/authentication/register/register_user/presentation/register_user_screen/register_user_screen.dart';
import '../features/authentication/register/select_country/presentation/select_country_screen.dart';
import '../features/authentication/register/service_overview/presentation/buyer/service_overview_buyer_screen.dart';
import '../features/authentication/register/service_overview/presentation/seller/service_overview_seller_screen.dart';
import '../features/authentication/register/terms_and_conditions/presentation/buyer/terms_and_conditions_buyer_screen.dart';
import '../features/authentication/register/terms_and_conditions/presentation/seller/terms_and_conditions_seller_screen.dart';
import '../features/boarding/presentation/splash_screen.dart';
import '../features/favorite/presentation/favorite_screen.dart';
import '../features/favorite/presentation/widgets/favorite_product_list_screen.dart';
import '../features/favorite/presentation/widgets/favorite_seller_list_screen.dart';
import '../features/help/domain/entities/faq_entity.dart';
import '../features/help/presentation/fag_article_screen.dart';
import '../features/help/presentation/help_screen.dart';
import '../features/home/<USER>/home_screen.dart';
import '../features/legal_notice/domain/enums/legal_notice_type_enum.dart';
import '../features/legal_notice/presentation/legal_notice_screen.dart';
import '../features/message/conversation_list/presentation/conversation_list_screen.dart';
import '../features/message/search_conversation/presentation/search_conversation_screen.dart';
import '../features/notices/presentation/notices_detail_screen.dart';
import '../features/notifications/presentation/notifications_screen.dart';
import '../features/product/product_detail/presentation/product_detail_screen.dart';
import '../features/product_search/product_search_results/presentation/product_search_results_screen.dart';
import '../features/product_search/search/presentation/product_search_screen.dart';
import '../features/product_search/search_by_brand/presentation/search_by_brand_screen.dart';
import '../features/product_search/search_by_category/domain/entities/product_category_entity.dart';
import '../features/product_search/search_by_category/presentation/search_by_category_screen.dart';
import '../features/product_search/search_by_category/presentation/widgets/search_by_sub_category_list.dart';
import '../features/product_search/search_by_country/presentation/search_by_country_screen.dart';
import '../features/product_search/search_by_keyword/presentation/search_by_keyword_screen.dart';
import '../features/profile/cancel_account/presentation/cancel_account_screen.dart';
import '../features/profile/cancel_account/presentation/cancel_complete/cancel_account_complete_screen.dart';
import '../features/profile/change_password/presentation/change_password_screen.dart';
import '../features/profile/inquiry/presentation/inquiry_form/inquiry_service_form_screen.dart';
import '../features/profile/inquiry/presentation/inquiry_request_area/inquiry_request_area_screen.dart';
import '../features/profile/inquiry/presentation/inquiry_review/inquiry_review_screen.dart';
import '../features/profile/inquiry/presentation/inquiry_select_type/inquiry_select_type_screen.dart';
import '../features/profile/language/presentation/language_setting_screen.dart';
import '../features/profile/mail_address/presentation/mail_address_screen.dart';
import '../features/profile/notification_settings/presentation/notification_settings_screen.dart';
import '../features/profile/payment_method/presentation/payment_method_screen.dart';
import '../features/profile/pin_code/presentation/pin_code_screen.dart';
import '../features/profile/premium_membership/presentation/premium_membership_screen.dart';
import '../features/profile/profile/domain/entities/extra_profile_entity.dart';
import '../features/profile/profile/presentation/account_details/account_details_screen.dart';
import '../features/profile/profile/presentation/profile_screen.dart';
import '../features/profile/profile_detail/presentation/profile_detail_screen.dart';
import '../features/profile/seller_profile/presentation/select_favorite_brand/select_favorite_brand_screen.dart';
import '../features/profile/seller_profile/presentation/seller_profile_screen.dart';
import '../features/profile/updated_terms_of_service/presentation/updated_terms_of_service_screen.dart';
import '../features/purchase_history/detail/presentation/purchase_history_detail_screen.dart';
import '../features/purchase_history/history/presentation/purchase_history_screen.dart';
import '../features/seller_dashboard/business_insights/presentation/business_insights_screen.dart';
import '../features/seller_dashboard/business_insights/presentation/follower_detail/follower_detail_screen.dart';
import '../features/seller_dashboard/business_insights/presentation/follower_list/follower_list_screen.dart';
import '../features/seller_dashboard/dashboard/presentation/seller_dashboard_screen.dart';
import '../features/seller_dashboard/product_listing/presentation/delete/delete_product_screen.dart';
import '../features/seller_dashboard/product_listing/presentation/detail/on_sale_product_detail_screen.dart';
import '../features/seller_dashboard/product_listing/presentation/form/presentation/add_edit_product_brand/add_edit_product_brand_screen.dart';
import '../features/seller_dashboard/product_listing/presentation/form/presentation/add_edit_product_category/add_edit_product_category_screen.dart';
import '../features/seller_dashboard/product_listing/presentation/form/presentation/add_edit_product_category/add_edit_product_sub_category_list.dart';
import '../features/seller_dashboard/product_listing/presentation/form/presentation/product_listing_form_screen.dart';
import '../features/seller_dashboard/product_listing/presentation/review/product_listing_review_screen.dart';
import '../features/seller_dashboard/sales_history_list/presentation/sales_history_list_screen.dart';
import '../features/seller_dashboard/sales_summary/presentation/sales_summary_screen.dart';
import '../features/seller_dashboard/sales_summary/presentation/sales_summary_tab_screen.dart';
import '../features/seller_dashboard/store_management/presentation/store_management_screen.dart';
import '../features/seller_dashboard/transactions/presentation/blocklist/presentation/blocklist_screen.dart';
import '../features/seller_dashboard/transactions/presentation/publish_shop/presentation/publish_shop_screen.dart';
import '../features/seller_dashboard/transactions/presentation/transaction_detail/presentation/transaction_detail_screen.dart';
import '../features/seller_top_page/presentation/seller_top_page_screen.dart';
import '../features/shopping_cart/cart_products/presentation/buyer_contact_form_screen.dart';
import '../features/shopping_cart/cart_products/presentation/cart_products_review_screen.dart';
import '../features/shopping_cart/cart_products/presentation/cart_products_screen.dart';
import '../features/shopping_cart/cites_application_process/presentation/cites_application_process_screen.dart';
import '../features/shopping_cart/cites_application_process/presentation/cites_payment_process_screen.dart';
import '../features/shopping_cart/cites_application_process/presentation/provisional_purchase_completed_screen.dart';
import '../features/shopping_cart/purchase_confirmation/presentation/payment_process/payment_process_error_screen.dart';
import '../features/shopping_cart/purchase_confirmation/presentation/payment_process/payment_process_flow_screen.dart';
import '../features/shopping_cart/purchase_confirmation/presentation/payment_process/payment_process_screen.dart';
import '../features/shopping_cart/purchase_confirmation/presentation/purchase_complete/purchase_complete_screen.dart';
import '../features/shopping_cart/purchase_confirmation/presentation/purchase_confirmation_screen.dart';
import '../features/shopping_cart/purchase_confirmation/presentation/subscription/purchase_subscription_screen.dart';
import 'not_found_screen.dart';
import 'router_constants.dart';

part 'app_router.g.dart';
part 'app_routes.dart';

@riverpod
BuildContext? appRouterContext(Ref ref) {
  return ref.read(goRouterProvider).routerDelegate.navigatorKey.currentContext;
}

@Riverpod(keepAlive: true)
GoRouter goRouter(Ref ref) {
  final talker = Talker();
  final defaultLocation = AppRoutes.splash.initLocation;

  const restorationScopeId = 'app_router';
  final rootNavigatorKey = GlobalKey<NavigatorState>();

  // Shell Navigator Keys
  final shellNavigatorHomeKey =
      GlobalKey<NavigatorState>(debugLabel: 'shell_home');
  final shellNavigatorSearchKey =
      GlobalKey<NavigatorState>(debugLabel: 'shell_search');
  final shellNavigatorFavoriteKey =
      GlobalKey<NavigatorState>(debugLabel: 'shell_favorite');
  final shellNavigatorShoppingCartKey =
      GlobalKey<NavigatorState>(debugLabel: 'shell_shopping_cart');
  final shellNavigatorMeKey = GlobalKey<NavigatorState>(debugLabel: 'shell_me');

  return GoRouter(
    initialLocation: defaultLocation,
    restorationScopeId: restorationScopeId,
    debugLogDiagnostics: true,
    navigatorKey: rootNavigatorKey,
    observers: [TalkerRouteObserver(talker)],
    routes: [
      GoRoute(
        path: AppRoutes.splash.initLocation,
        name: AppRoutes.splash.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const SplashScreen(),
        ),
      ),
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) =>
            DashboardScreen(navigationShell: navigationShell),
        redirect: (context, state) {
          return null;
        },
        branches: [
          StatefulShellBranch(
            navigatorKey: shellNavigatorHomeKey,
            initialLocation: AppRoutes.home.initLocation,
            routes: [
              GoRoute(
                path: AppRoutes.home.initLocation,
                name: AppRoutes.home.name,
                pageBuilder: (context, state) => MaterialPage(
                  key: state.pageKey,
                  child: const HomeScreen(),
                ),
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: shellNavigatorSearchKey,
            routes: [
              GoRoute(
                path: AppRoutes.productSearch.initLocation,
                name: AppRoutes.productSearch.name,
                pageBuilder: (context, state) => MaterialPage(
                  key: state.pageKey,
                  child: const ProductSearchScreen(),
                ),
                routes: [
                  GoRoute(
                    path: AppRoutes.searchByKeyword.initLocation,
                    name: AppRoutes.searchByKeyword.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const SearchByKeywordScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.searchByCategory.initLocation,
                    name: AppRoutes.searchByCategory.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const SearchByCategoryScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.searchByBrand.initLocation,
                    name: AppRoutes.searchByBrand.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const SearchByBrandScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.searchByCountry.initLocation,
                    name: AppRoutes.searchByCountry.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const SearchByCountryScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.searchBySubCategory.initLocation,
                    name: AppRoutes.searchBySubCategory.name,
                    pageBuilder: (context, state) {
                      ProductCategoryEntity? extraEntity;
                      final extra = state.extra;
                      if (extra is ProductCategoryEntity) {
                        extraEntity = extra;
                      }
                      return MaterialPage(
                        key: state.pageKey,
                        child: SearchBySubCategoryList(
                          category: extraEntity,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: AppRoutes.productSearchResult.initLocation,
                    name: AppRoutes.productSearchResult.name,
                    pageBuilder: (context, state) {
                      SearchProductsRequest? searchProductsRequest;
                      String previousLocation = '';
                      final queryParams = state.uri.queryParameters;
                      if (queryParams
                          .containsKey(RouterConstants.previousLocation)) {
                        previousLocation =
                            queryParams[RouterConstants.previousLocation] ?? '';
                      }
                      final extra = state.extra;
                      if (extra is SearchProductsRequest) {
                        searchProductsRequest = extra;
                      }
                      if (searchProductsRequest == null) {
                        return MaterialPage(
                          key: state.pageKey,
                          child: NotFoundScreen(),
                        );
                      }
                      return MaterialPage(
                        key: state.pageKey,
                        child: ProductSearchResultsScreen(
                          initRequest: searchProductsRequest,
                          previousLocation: previousLocation,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: shellNavigatorFavoriteKey,
            routes: [
              GoRoute(
                path: AppRoutes.favorite.initLocation,
                name: AppRoutes.favorite.name,
                pageBuilder: (context, state) => MaterialPage(
                  key: state.pageKey,
                  child: const FavoriteScreen(),
                ),
                routes: [
                  GoRoute(
                    path: AppRoutes.favoriteProductList.initLocation,
                    name: AppRoutes.favoriteProductList.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const FavoriteProductListScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.favoriteSellerList.initLocation,
                    name: AppRoutes.favoriteSellerList.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const FavoriteSellerListScreen(),
                    ),
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: shellNavigatorShoppingCartKey,
            routes: [
              GoRoute(
                path: AppRoutes.shoppingCart.initLocation,
                name: AppRoutes.shoppingCart.name,
                pageBuilder: (context, state) => MaterialPage(
                  key: state.pageKey,
                  child: const CartProductsScreen(),
                ),
                routes: [
                  GoRoute(
                    path: AppRoutes.cartProductsReview.initLocation,
                    name: AppRoutes.cartProductsReview.name,
                    pageBuilder: (context, state) {
                      Cart? cart;

                      final extra = state.extra;
                      if (extra is Map<String, dynamic>) {
                        cart = extra['cart'];
                      }
                      return MaterialPage(
                        key: state.pageKey,
                        child: CartProductsReviewScreen(
                          cart: cart,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: shellNavigatorMeKey,
            preload: true,
            routes: [
              GoRoute(
                path: AppRoutes.me.initLocation,
                name: AppRoutes.me.name,
                pageBuilder: (context, state) {
                  ExtraProfileEntity? extraEntity;
                  final extra = state.extra;
                  if (extra is ExtraProfileEntity) {
                    extraEntity = extra;
                  }
                  return MaterialPage(
                    key: state.pageKey,
                    child: ProfileScreen(
                      initialTab: extraEntity,
                    ),
                  );
                },
                routes: [
                  GoRoute(
                    path: AppRoutes.accountDetails.initLocation,
                    name: AppRoutes.accountDetails.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const AccountDetailsScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.profileDetail.initLocation,
                    name: AppRoutes.profileDetail.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const ProfileDetailScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.premiumMembership.initLocation,
                    name: AppRoutes.premiumMembership.name,
                    pageBuilder: (context, state) => MaterialPage(
                      key: state.pageKey,
                      child: const PremiumMembershipScreen(),
                    ),
                  ),
                  GoRoute(
                    path: AppRoutes.purchaseHistory.initLocation,
                    name: AppRoutes.purchaseHistory.name,
                    pageBuilder: (context, state) {
                      return MaterialPage(
                        key: state.pageKey,
                        child: PurchaseHistoryScreen(),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.paymentMethod.initLocation,
        name: AppRoutes.paymentMethod.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const PaymentMethodScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.mailAddress.initLocation,
        name: AppRoutes.mailAddress.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const MailAddressScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.languageSetting.initLocation,
        name: AppRoutes.languageSetting.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const LanguageSettingScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.notificationSettings.initLocation,
        name: AppRoutes.notificationSettings.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const NotificationSettingsScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.changePassword.initLocation,
        name: AppRoutes.changePassword.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const ChangePasswordScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.displayLanguage.initLocation,
        name: AppRoutes.displayLanguage.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const DisplayLanguageScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.pinCode.initLocation,
        name: AppRoutes.pinCode.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const PinCodeScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.passwordResetConfirm.initLocation,
        name: AppRoutes.passwordResetConfirm.name,
        pageBuilder: (context, state) {
          String? token;
          final extra = state.extra;
          if (extra is String) {
            token = extra;
          }

          return MaterialPage(
            key: state.pageKey,
            child: PasswordResetConfirmScreen(
              token: token,
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.registerEmail.initLocation,
        name: AppRoutes.registerEmail.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: RegisterEmailScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.sendRegisterEmailSuccess.initLocation,
        name: AppRoutes.sendRegisterEmailSuccess.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: SendRegisterEmailSuccessScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.registerUser.initLocation,
        name: AppRoutes.registerUser.name,
        pageBuilder: (context, state) {
          JwtTokenRegisterEmailEntity? extraEntity;
          final extra = state.extra;

          if (extra is JwtTokenRegisterEmailEntity) {
            extraEntity = extra;
          }
          return MaterialPage(
            key: state.pageKey,
            child: RegisterUserScreen(extraEntity: extraEntity),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.registerReview.initLocation,
        name: AppRoutes.registerReview.name,
        pageBuilder: (context, state) {
          RegisterUserEntity? extraEntity;
          final extra = state.extra;

          if (extra is RegisterUserEntity) {
            extraEntity = extra;
          } else {
            return MaterialPage(
              key: state.pageKey,
              child: NotFoundScreen(),
            );
          }
          return MaterialPage(
            key: state.pageKey,
            child: RegisterReviewScreen(extraEntity),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.productDetail.initLocation,
        name: AppRoutes.productDetail.name,
        pageBuilder: (context, state) {
          String productId = '';
          final extra = state.extra;
          if (extra is String) {
            productId = extra;
          }
          return MaterialPage(
            key: state.pageKey,
            child: ProductDetailScreen(productId: productId),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.register.initLocation,
        name: AppRoutes.register.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const RegisterDashboardScreen(),
        ),
        routes: [
          GoRoute(
            path: AppRoutes.servicesOverviewBuyer.initLocation,
            name: AppRoutes.servicesOverviewBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: ServiceOverviewBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.servicesOverviewSeller.initLocation,
            name: AppRoutes.servicesOverviewSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: ServiceOverviewSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.termsAndConditionBuyer.initLocation,
            name: AppRoutes.termsAndConditionBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: TermsAndConditionsBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.termsAndConditionSeller.initLocation,
            name: AppRoutes.termsAndConditionSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: TermsAndConditionsSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.selectCountry.initLocation,
            name: AppRoutes.selectCountry.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: SelectCountryScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.subscriptionPurchaseBuyer.initLocation,
            name: AppRoutes.subscriptionPurchaseBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: SubscriptionPurchaseBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.subscriptionPurchaseSeller.initLocation,
            name: AppRoutes.subscriptionPurchaseSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: SubscriptionPurchaseSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.informationRegisterFormBuyer.initLocation,
            name: AppRoutes.informationRegisterFormBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: InformationRegisterFormBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.informationRegisterFormSeller.initLocation,
            name: AppRoutes.informationRegisterFormSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: InformationRegisterFormSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.verificationIdentityBuyer.initLocation,
            name: AppRoutes.verificationIdentityBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: VerificationIdentityBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.verificationIdentitySeller.initLocation,
            name: AppRoutes.verificationIdentitySeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: VerificationIdentitySellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.accountRegistrationSeller.initLocation,
            name: AppRoutes.accountRegistrationSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: RegisterAccountSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.accountRegistrationErrorSeller.initLocation,
            name: AppRoutes.accountRegistrationErrorSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: RegisterAccountErrorSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.verificationSeller.initLocation,
            name: AppRoutes.verificationSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: VerificationSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.verificationBusinessSeller.initLocation,
            name: AppRoutes.verificationBusinessSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: VerificationBusinessSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.confirmRegistrationBuyer.initLocation,
            name: AppRoutes.confirmRegistrationBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: ConfirmRegistrationBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.confirmRegistrationSeller.initLocation,
            name: AppRoutes.confirmRegistrationSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: ConfirmRegistrationSellerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.completedRegistrationBuyer.initLocation,
            name: AppRoutes.completedRegistrationBuyer.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: CompletedRegistrationBuyerScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.completedRegistrationSeller.initLocation,
            name: AppRoutes.completedRegistrationSeller.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: CompletedRegistrationSellerScreen(),
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.messages.initLocation,
        name: AppRoutes.messages.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: ConversationListScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.searchMessage.initLocation,
        name: AppRoutes.searchMessage.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: SearchConversationScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.notification.initLocation,
        name: AppRoutes.notification.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const ManagementNotificationsScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.noticesDetail.initLocation,
        name: AppRoutes.noticesDetail.name,
        pageBuilder: (context, state) {
          String? noticeId;
          if (state.extra is String) {
            noticeId = state.extra as String;
          }
          return MaterialPage(
            key: state.pageKey,
            child: NoticesDetailScreen(noticeId: noticeId),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.help.initLocation,
        name: AppRoutes.help.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const HelpScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.faqArticle.initLocation,
        name: AppRoutes.faqArticle.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: FaqArticleScreen(faq: state.extra as FaqEntity?),
        ),
      ),
      GoRoute(
        path: AppRoutes.legalNotice.initLocation,
        name: AppRoutes.legalNotice.name,
        pageBuilder: (context, state) {
          LegalNoticeTypeEnum legalNotice =
              LegalNoticeTypeEnum.all(false).first;
          if (state.extra is LegalNoticeTypeEnum) {
            legalNotice = state.extra as LegalNoticeTypeEnum;
          }

          return MaterialPage(
            key: state.pageKey,
            child: LegalNoticeScreen(
              legalNoticeType: legalNotice,
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.sellerTopPage.initLocation,
        name: AppRoutes.sellerTopPage.name,
        pageBuilder: (context, state) {
          String? sellerId;
          if (state.extra is String) {
            sellerId = state.extra as String;
          }

          return MaterialPage(
            key: state.pageKey,
            child: SellerTopPageScreen(sellerId: sellerId),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.sellerDashboard.initLocation,
        name: AppRoutes.sellerDashboard.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: SellerDashboardScreen(),
          );
        },
        routes: [
          GoRoute(
            path: AppRoutes.salesHistoryList.initLocation,
            name: AppRoutes.salesHistoryList.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: SalesHistoryListScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.salesSummary.initLocation,
            name: AppRoutes.salesSummary.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: SalesSummaryScreen(),
              );
            },
            routes: [
              GoRoute(
                path: AppRoutes.salesSummaryTab.initLocation,
                name: AppRoutes.salesSummaryTab.name,
                pageBuilder: (context, state) {
                  return MaterialPage(
                    key: state.pageKey,
                    child: SalesSummaryTabScreen(),
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: AppRoutes.insights.initLocation,
            name: AppRoutes.insights.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: BusinessInsightsScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.storeManagement.initLocation,
            name: AppRoutes.storeManagement.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: StoreManagementScreen(),
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.cartProducts.initLocation,
        name: AppRoutes.cartProducts.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: CartProductsScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.buyerContact.initLocation,
        name: AppRoutes.buyerContact.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: BuyerContactFormScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.purchaseConfirmation.initLocation,
        name: AppRoutes.purchaseConfirmation.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: PurchaseConfirmationScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.purchaseSubscription.initLocation,
        name: AppRoutes.purchaseSubscription.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: PurchaseSubscriptionScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.paymentProcess.initLocation,
        name: AppRoutes.paymentProcess.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: PaymentProcessScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.paymentProcessFlow.initLocation,
        name: AppRoutes.paymentProcessFlow.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: PaymentProcessFlowScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.paymentProcessError.initLocation,
        name: AppRoutes.paymentProcessError.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: PaymentProcessErrorScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.purchaseComplete.initLocation,
        name: AppRoutes.purchaseComplete.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: PurchaseCompleteScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.citesApplicationProcess.initLocation,
        name: AppRoutes.citesApplicationProcess.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: CitesApplicationProcessScreen(),
          );
        },
        routes: [
          GoRoute(
            path: AppRoutes.citesPaymentProcess.initLocation,
            name: AppRoutes.citesPaymentProcess.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: CitesPaymentProcessScreen(),
              );
            },
          ),
          GoRoute(
            path: AppRoutes.provisionalPurchaseCompleted.initLocation,
            name: AppRoutes.provisionalPurchaseCompleted.name,
            pageBuilder: (context, state) {
              return MaterialPage(
                key: state.pageKey,
                child: ProvisionalPurchaseCompletedScreen(),
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.purchaseHistoryDetail.initLocation,
        name: AppRoutes.purchaseHistoryDetail.name,
        pageBuilder: (context, state) {
          String? purchaseId;
          if (state.extra is String) {
            purchaseId = state.extra as String;
          }
          return MaterialPage(
            key: state.pageKey,
            child: PurchaseHistoryDetailScreen(
              purchaseId: purchaseId ?? '',
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.productListingForm.initLocation,
        name: AppRoutes.productListingForm.name,
        pageBuilder: (context, state) {
          final product = state.extra as ProductEntity?;
          return MaterialPage(
            key: state.pageKey,
            child: ProductListingFormScreen(
              product: product,
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.addEditProductCategory.initLocation,
        name: AppRoutes.addEditProductCategory.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: AddEditProductCategoryScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.addEditProductSubCategory.initLocation,
        name: AppRoutes.addEditProductSubCategory.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: AddEditProductSubCategoryListScreen(
              category: state.extra as ProductCategoryEntity,
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.addProductBrand.initLocation,
        name: AppRoutes.addProductBrand.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: AddEditProductBrandScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.productListingReview.initLocation,
        name: AppRoutes.productListingReview.name,
        pageBuilder: (context, state) {
          return MaterialPage(
            key: state.pageKey,
            child: ProductListingReviewScreen(),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.onSaleProductDetail.initLocation,
        name: AppRoutes.onSaleProductDetail.name,
        pageBuilder: (context, state) {
          final product = state.extra as ProductEntity?;
          return MaterialPage(
            key: state.pageKey,
            child: OnSaleProductDetailScreen(product: product),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.deleteProduct.initLocation,
        name: AppRoutes.deleteProduct.name,
        pageBuilder: (context, state) {
          final product = state.extra as ProductEntity?;
          return MaterialPage(
            key: state.pageKey,
            child: DeleteProductScreen(product: product),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.inquirySelectType.initLocation,
        name: AppRoutes.inquirySelectType.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const InquirySelectTypeScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.inquiryServiceForm.initLocation,
        name: AppRoutes.inquiryServiceForm.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const InquiryServiceFormScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.inquiryServiceReview.initLocation,
        name: AppRoutes.inquiryServiceReview.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const InquiryReviewScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.inquiryAreaRequest.initLocation,
        name: AppRoutes.inquiryAreaRequest.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const InquiryRequestAreaScreen(),
        ),
      ),
      GoRoute(
          path: AppRoutes.updatedTermsOfService.initLocation,
          name: AppRoutes.updatedTermsOfService.name,
          pageBuilder: (context, state) {
            final extra = state.extra as Map<String, dynamic>? ?? {};
            return MaterialPage(
              key: state.pageKey,
              child: UpdatedTermsOfServiceScreen(
                isChangeCountry: extra['isChangeCountry'] ?? false,
                countryCode: extra['countryCode'] as String?,
              ),
            );
          }),
      GoRoute(
        path: AppRoutes.transactionDetail.initLocation,
        name: AppRoutes.transactionDetail.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const TransactionDetailScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.followerList.initLocation,
        name: AppRoutes.followerList.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const FollowerListScreen(),
        ),
        routes: [
          GoRoute(
            path: AppRoutes.followerDetail.initLocation,
            name: AppRoutes.followerDetail.name,
            pageBuilder: (context, state) => MaterialPage(
              key: state.pageKey,
              child: const FollowerDetailScreen(),
            ),
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.blocklist.initLocation,
        name: AppRoutes.blocklist.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const BlocklistScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.publishShop.initLocation,
        name: AppRoutes.publishShop.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const PublishShopScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.sellerProfile.initLocation,
        name: AppRoutes.sellerProfile.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const SellerProfileScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.selectFavoriteBrand.initLocation,
        name: AppRoutes.selectFavoriteBrand.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const SelectFavoriteBrandScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.cancelAccount.initLocation,
        name: AppRoutes.cancelAccount.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const CancelAccountScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.cancelAccountComplete.initLocation,
        name: AppRoutes.cancelAccountComplete.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          child: const CancelAccountCompleteScreen(),
        ),
      ),
      GoRoute(
        path: AppRoutes.withdrawEarnings.initLocation,
        name: AppRoutes.withdrawEarnings.name,
        pageBuilder: (context, state) => MaterialPage(
          key: state.pageKey,
          // TODO: Add withdraw earnings screen
          child: Scaffold(
            appBar: AppBar(
              leading: IconButton(
                onPressed: () => context.pop(),
                icon: Icon(Icons.arrow_back),
              ),
              title: Text(LocaleKeys.strCommonShopWithdrawSales.tr()),
            ),
          ),
        ),
      ),
    ],
    errorBuilder: (context, state) => const NotFoundScreen(),
  );
}

CustomTransitionPage buildPageWithDefaultTransition<T>({
  required BuildContext context,
  required GoRouterState state,
  required Widget child,
}) {
  return CustomTransitionPage<T>(
    key: state.pageKey,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      return SizeTransition(sizeFactor: animation, child: child);
    },
  );
}

extension GoRouterExtension on GoRouter {
  // Navigate back to a specific route
  void popUntilPath(String ancestorPath) {
    while (routerDelegate.currentConfiguration.matches.last.matchedLocation !=
        ancestorPath) {
      if (!canPop()) {
        return;
      }
      pop();
    }
  }
}
