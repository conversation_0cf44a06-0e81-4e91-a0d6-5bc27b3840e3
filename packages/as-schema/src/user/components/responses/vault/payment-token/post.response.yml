type: object
properties:
  id:
    type: string
    description: The payment token ID
    example: "8EC8D4G67VAS2"
  status:
    type: string
    description: The status of the payment token
    example: "CREATED"
  customer:
    type: object
    properties:
      id:
        type: string
        description: The customer ID
        example: "customer_4029352050"
  payment_source:
    type: object
    properties:
      card:
        type: object
        properties:
          last_digits:
            type: string
            description: Last 4 digits of the card
            example: "1234"
          brand:
            type: string
            description: Card brand
            example: "VISA"
          type:
            type: string
            description: Card type
            example: "CREDIT"
  links:
    type: array
    items:
      type: object
      properties:
        href:
          type: string
          description: The complete target URL
        rel:
          type: string
          description: The link relation type
        method:
          type: string
          description: The HTTP method required to make the related call
required:
  - id
  - status
