type: object
properties:
  id:
    type: string
    description: The setup token ID
    example: "7GH53639GA425732B"
  status:
    type: string
    description: The status of the setup token
    example: "CREATED"
  payment_source:
    type: object
    properties:
      card:
        type: object
        description: Card information for the setup token
        properties: {}
  links:
    type: array
    items:
      type: object
      properties:
        href:
          type: string
          description: The complete target URL
        rel:
          type: string
          description: The link relation type
        method:
          type: string
          description: The HTTP method required to make the related call
required:
  - id
  - status
