openapi: 3.1.0
info:
  title: AS API User
  version: "1.0"
  description: This is API specification for AS project.
servers:
  - url: "localhost:3000/user/v1"
paths:
  /auth:
    post:
      $ref: ./paths/auth/post.yml
  /auth/signup:
    post:
      $ref: ./paths/auth/signup/post.yml
  /auth/signup/verify:
    post:
      $ref: ./paths/auth/signup/verify.yml
  /auth/reset-password/request:
    post:
      $ref: ./paths/auth/reset-password/request.yml
  /auth/reset-password/verify:
    post:
      $ref: ./paths/auth/reset-password/verify.yml
  /auth/reset-password/confirm:
    post:
      $ref: ./paths/auth/reset-password/confirm.yml
  /users/register:
    post:
      $ref: ./paths/users/register/post.yml
  /system/status:
    get:
      $ref: ./paths/system/status/get.yml
  /home-feeds:
    get:
      $ref: ./paths/home-feeds/get.yml
  /seller/dashboard:
    get:
      $ref: ./paths/sellers/dashboard/get.yml
  /brands:
    get:
      $ref: ./paths/brands/get.yml
  /product-categories:
    get:
      $ref: ./paths/product-categories/get.yml
  /users/{id}/notifications:
    get:
      $ref: ./paths/users/notifications/get.yml
  /users/{id}/notifications/unread-count:
    get:
      $ref: ./paths/users/notifications/unread-count/get.yml
  /users/{id}/notifications/read-all:
    post:
      $ref: ./paths/users/notifications/read-all/post.yml
  /notifications/{id}/read:
    post:
      $ref: ./paths/users/notifications/read/post.yml
  /notifications/{id}:
    get:
      $ref: ./paths/users/notifications/id/get.yml
  /credit-cards:
    post:
      $ref: ./paths/credit-cards/post.yml
  /vault/setup-token:
    post:
      $ref: ./paths/vault/setup-token/post.yml
  /vault/payment-token:
    post:
      $ref: ./paths/vault/payment-token/post.yml
  /users/{id}/payment-methods:
    put:
      $ref: ./paths/users/payment-methods/put.yml
  /users/{id}/seller:
    get:
      $ref: ./paths/users/seller/get.yml
    # post:
    #   $ref: ./paths/users/seller/post.yml
    put:
      $ref: ./paths/users/seller/put.yml
  /users/{id}/profile:
    get:
      $ref: ./paths/users/profile/get.yml
    put:
      $ref: ./paths/users/profile/put.yml
  /users/{id}/seller/bank-account:
    put:
      $ref: ./paths/users/seller/bank-account/put.yml
  /users/{id}/pin:
    put:
      $ref: ./paths/users/pin/put.yml
    post:
      $ref: ./paths/users/pin/post.yml
  /users/{id}/pin/check:
    post:
      $ref: ./paths/users/pin/check/post.yml
  /users/{id}/password:
    put:
      $ref: ./paths/users/password/put.yml
  /users/{id}/email:
    put:
      $ref: ./paths/users/email/put.yml
  /users/{id}/email/verify:
    post:
      $ref: ./paths/users/email/verify/post.yml
  /users/{id}/notification-settings:
    get:
      $ref: ./paths/users/notification-settings/get.yml
    put:
      $ref: ./paths/users/notification-settings/put.yml
  /users/{id}/fcm-token:
    put:
      $ref: ./paths/users/fcm-token/put.yml
    delete:
      $ref: ./paths/users/fcm-token/delete.yml
  /users/{id}/cancel:
    post:
      $ref: ./paths/users/cancel/post.yml
  /users/{id}/check-cancel:
    post:
      $ref: ./paths/users/check-cancel/post.yml
#   /users/{id}/purchases:
#     get:
#       $ref: ./paths/users/purchases/get.yml
  /users/{id}/favorite-products:
    get:
      $ref: ./paths/users/favorites/products/get.yml
    post:
      $ref: ./paths/users/favorites/products/post.yml
    delete:
      $ref: ./paths/users/favorites/products/delete.yml
  /users/{id}/favorite-sellers:
    get:
      $ref: ./paths/users/favorites/sellers/get.yml
    post:
      $ref: ./paths/users/favorites/sellers/post.yml
    delete:
      $ref: ./paths/users/favorites/sellers/delete.yml
#   /cart/items:
#     get:
#       $ref: ./paths/cart/items/get.yml
  /countries:
    get:
      $ref: ./paths/countries/get.yml
  /postal-codes:
    get:
      $ref: ./paths/postal-codes/get.yml
  /faqs:
    get:
      $ref: ./paths/faqs/get.yml
  /terms:
    get:
      $ref: ./paths/terms/get.yml
  /products/search:
    post:
      $ref: ./paths/products/search/post.yml
  /products/{id}:
    get:
      $ref: ./paths/products/id/get.yml
  /sellers/{id}/products:
    get:
      $ref: ./paths/sellers/products/get.yml
  #   post:
  #     $ref: ./paths/sellers/products/post.yml
  # /sellers/{id}/products/{productId}:
  #   put:
  #     $ref: ./paths/sellers/products/put.yml
  #   delete:
  #     $ref: ./paths/sellers/products/delete.yml
  /threads:
    get:
      $ref: ./paths/threads/get.yml
    post:
      $ref: ./paths/threads/post.yml
  /users/{id}/threads/unread-count:
    get:
      $ref: ./paths/users/threads/unread-count/get.yml
  /threads/{id}:
    get:
      $ref: ./paths/threads/id/get.yml
  /threads/{id}/messages:
    get:
      $ref: ./paths/threads/messages/get.yml
  /messages:
    post:
      $ref: ./paths/messages/post.yml
  /sellers/{id}:
    get:
      $ref: ./paths/sellers/id/get.yml
  /inquiries:
    post:
      $ref: ./paths/inquiries/post.yml
  /user/subscription-membership:
    post:
      $ref: ./paths/users/subscription/post.yml
    get:
      $ref: ./paths/users/subscription/get.yml
  /users/{id}/terms:
    put:
      $ref: ./paths/users/terms/put.yml
  /sellers/{id}/rating:
    get:
      $ref: ./paths/sellers/id/rating/get.yml

  /seller/products/{id}:
    put:
      $ref: ./paths/sellers/products/id/put.yml
    delete:
      $ref: ./paths/sellers/products/id/delete.yml
  /uploads:
    put:
      $ref: ./paths/uploads/put.yml
  /seller/block-users:
    post:
      $ref: ./paths/sellers/block-users/post.yml
    get:
      $ref: ./paths/sellers/block-users/get.yml
  /banners:
    get:
      $ref: ./paths/banners/get.yml
  /seller/block-users/{id}:
    delete:
      $ref: ./paths/sellers/block-users/delete.yml
  /seller/products:
    post:
      $ref: ./paths/sellers/products/post.yml
  /followed-sellers/products:
    get:
      $ref: ./paths/sellers/products/followed-sellers/get.yml
  /reports:
    post:
      $ref: ./paths/reports/post.yml
  /user/purchased-items:
    get:
      $ref: ./paths/purchased-items/get.yml
  /user/purchased-items/{id}:
    get:
      $ref: ./paths/purchased-items/id/get.yml
    delete:
      $ref: ./paths/purchased-items/id/delete.yml
  /user/purchased-items/{id}/reviews:
    post:
      $ref: ./paths/purchased-items/id/reviews/post.yml
  /cart:
    get:
      $ref: ./paths/cart/get.yml
    post:
      $ref: ./paths/cart-items/post.yml
  /cart-items/{id}:
    put:
      $ref: ./paths/cart-items/id/put.yml
    delete:
      $ref: ./paths/cart-items/id/delete.yml
  /seller/publish-status:
    put:
      $ref: ./paths/sellers/publish-status/put.yml
  /seller/sold-items:
    get:
      $ref:  ./paths/sellers/orders/get.yml
  /seller/sold-items/{id}:
    get:
      $ref: ./paths/sellers/sold-items/id/get.yml
  /seller/sold-items/{id}/status:
    put:
      $ref: ./paths/sellers/sold-items/id/status.yml
  /orders:
    post:
      $ref: ./paths/orders/post.yml
  /orders/{id}:
    get:
      $ref: ./paths/orders/id/get.yml
  /subscriptions/cancel/android:
    post:
      $ref: ./paths/users/subscription/cancel/android.yml
  /subscriptions/cancel/ios:
    post:
      $ref: ./paths/users/subscription/cancel/ios.yml

  /orders/{id}/purchase:
    post:
      $ref: ./paths/orders/id/purchase.yml
  /seller/orders-detail/status:
    get:
      $ref: ./paths/sellers/orders/status/get.yml

  /seller/following-users:
    get:
      $ref: ./paths/sellers/following-users/get.yml
  /sales-summary/daily/{date}:
    get:
      $ref: ./paths/sales-summary/daily/get.yml
  /sales-summary/monthly/{year}/{month}:
    get:
      $ref: ./paths/sales-summary/monthly/get.yml
  /sales-summary/yearly/{year}:
    get:
      $ref: ./paths/sales-summary/yearly/get.yml
  /sales-summary/overall:
    get:
      $ref: ./paths/sales-summary/overall/get.yml
  /sales-insights:
    get:
      $ref: ./paths/sales-insights/get.yml
tags:
  - name: auth
  - name: home
  - name: seller
  - name: sales-summary
  - name: sales-insights
  - name: followed-sellers
  - name: notification
  - name: profile
  - name: business
  - name: bank-account
  - name: membership
  - name: password
  - name: payment
  - name: pin
  - name: cart
  - name: favorites
  - name: system
  - name: user
  - name: faq
  - name: product
  - name: message
  - name: inquiry
  - name: subscription
  - name: upload
  - name: banner
  - name: report
  - name: order
components:
  schemas:
    # Model
    Pagination:
      $ref: ./components/models/pagination.yml
    Address:
      $ref: ./components/models/address.yml
    Phone:
      $ref: ./components/models/phone.yml
    User:
      $ref: ./components/models/user.yml
    MaintenanceDescription:
      $ref: ./components/models/maintenance-schedule/maintenance-description.yml
    BankAccount:
      $ref: ./components/models/bank-account.yml
    CompanyInfo:
      $ref: ./components/models/company-info.yml
    Kyc:
      $ref: ./components/models/kyc.yml
    Seller:
      $ref: ./components/models/seller.yml
    Pin:
      $ref: ./components/models/user/pin.yml
    PushNotificationSettings:
      $ref: ./components/models/notification-settings/push-notification-settings.yml
    EmailNotificationSettings:
      $ref: ./components/models/notification-settings/email-notification-settings.yml
    Country:
      $ref: ./components/models/country/country.yml
    Region:
      $ref: ./components/models/country/region.yml
    Faq:
      $ref: ./components/models/faq/faq.yml
    FaqContent:
      $ref: ./components/models/faq/faq-content.yml
    Term:
      $ref: ./components/models/term/term.yml
    TermContent:
      $ref: ./components/models/term/term-content.yml
    Product:
      $ref: ./components/models/product/product.yml
    ProductTarget:
      $ref: ./components/models/product/product-target.yml
    ProductCondition:
      $ref: ./components/models/product/product-condition.yml
    ProductSalesStatus:
      $ref: ./components/models/product/sales-status.yml
    ProductTransactionStatus:
      $ref: ./components/models/product/transaction-status.yml
    ProductCategory:
      $ref: ./components/models/product/product-category.yml
    ProductBrand:
      $ref: ./components/models/product/product-brand.yml
    Thread:
      $ref: ./components/models/thread/thread.yml
    Message:
      $ref: ./components/models/thread/message.yml
    Notification:
      $ref: ./components/models/notification.yml
    InquiryCategory:
      $ref: ./components/models/inquiry/inquiry-category.yml
    InquiryStatus:
      $ref: ./components/models/inquiry/inquiry-status.yml
    InquiryContentCountry:
      $ref: ./components/models/inquiry/inquiry-content-country.yml
    InquiryContentMessage:
      $ref: ./components/models/inquiry/inquiry-content-message.yml
    InquiryContent:
      $ref: ./components/models/inquiry/inquiry-content.yml
    Inquiry:
      $ref: ./components/models/inquiry/inquiry.yml
    BannerActionType:
      $ref: ./components/models/banner/banner-action-type.yml
    BannerAction:
      $ref: ./components/models/banner/banner-action.yml
    BannerQuery:
      $ref: ./components/models/banner/banner-query.yml
    BannerTranslation:
      $ref: ./components/models/banner/banner-translation.yml
    BannerStatus:
      $ref: ./components/models/banner/banner-status.yml
    Banner:
      $ref: ./components/models/banner/banner.yml
    Admin:
      $ref: ./components/models/admin.yml
    BlockUser:
      $ref: ./components/models/block-user.yml
    Report:
      $ref: ./components/models/report/report.yml
    ReportStatus:
      $ref: ./components/models/report/report-status.yml
    PurchasedItem:
      $ref: ./components/models/order/purchased-item.yml
    CartItem:
      $ref: ./components/models/order/cart-item.yml
    Attachment:
      $ref: ./components/models/attachment.yml
    CreditCard:
      $ref: ./components/models/credit-card.yml
    Order:
      $ref: ./components/models/order/order.yml
    TaxDetail:
      $ref: ./components/models/order/tax-detail.yml
    Subscription:
      $ref: ./components/models/subscription.yml
    FollowingUser:
      $ref: ./components/models/following-user.yml
    TaxInfo:
      $ref: ./components/models/order/tax-info.yml
    TaxItemInfo:
      $ref: ./components/models/order/tax-item-info.yml
    TaxName:
      $ref: ./components/models/order/tax-name.yml
    SalesFeeInfo:
      $ref: ./components/models/order/sales-fee-info.yml
    Cart:
      $ref: ./components/models/order/cart.yml
    BankAccountType:
      $ref: ./components/models/country/bank-account-type.yml
    Currency:
      $ref: ./components/models/country/currency.yml
    BannerProducts:
      $ref: ./components/models/home/<USER>
    KeywordProducts:
      $ref: ./components/models/home/<USER>
    ShopResult:
      $ref: ./components/models/seller/shop-result.yml
    ProductSize:
      $ref: ./components/models/product/product-size.yml
    SizeTable:
      $ref: ./components/models/product/size-table.yml
    SalesSummaryOrderDetail:
      $ref: ./components/models/sales-summary/order-detail.yml
    SalesSummaryOrderCounts:
      $ref: ./components/models/sales-summary/order-counts.yml
    SalesSummaryOrderAmounts:
      $ref: ./components/models/sales-summary/order-amounts.yml
    SalesSummarySimpleOrderTotals:
      $ref: ./components/models/sales-summary/simple-order-totals.yml
    SalesSummaryQuarterlySummary:
      $ref: ./components/models/sales-summary/quarterly-summary.yml
    CountryFollowerStats:
      $ref: ./components/models/sales-insights/country-follower-stats.yml
    GenderBreakdown:
      $ref: ./components/models/sales-insights/gender-breakdown.yml
    AgeGroupStats:
      $ref: ./components/models/sales-insights/age-group-stats.yml
    FollowerTrends:
      $ref: ./components/models/sales-insights/follower-trends.yml

    # Request
    AuthRequest:
      $ref: ./components/requests/auth/post.request.yml
    RegisterUserRequest:
      $ref: ./components/requests/users/register/post.request.yml
    RegisterSellerRequest:
      $ref: ./components/requests/users/seller/post.request.yml
    SignupRequest:
      $ref: ./components/requests/auth/signup/post.request.yml
    SignupVerifyRequest:
      $ref: ./components/requests/auth/signup/verify.request.yml
    ResetPasswordRequestRequest:
      $ref: ./components/requests/auth/reset-password/request.request.yml
    ResetPasswordVerifyRequest:
      $ref: ./components/requests/auth/reset-password/verify.request.yml
    ResetPasswordConfirmRequest:
      $ref: ./components/requests/auth/reset-password/confirm.request.yml
    # FollowedSellersProductsRequest:
    #   $ref: ./components/requests/followed-sellers/products/get.request.yml"
    UpdateUserProfileRequest:
      $ref: ./components/requests/users/profile/put.request.yml
    VerifyCreditCardRequest:
      $ref: ./components/requests/credit-cards/post.request.yml
    CreateVaultSetupTokenRequest:
      $ref: ./components/requests/vault/setup-token/post.request.yml
    CreateVaultPaymentTokenRequest:
      $ref: ./components/requests/vault/payment-token/post.request.yml
    UpdatePaymentMethodsRequest:
      $ref: ./components/requests/users/payment-methods/put.request.yml
    UpdateSellerRequest:
      $ref: ./components/requests/users/seller/put.request.yml
    # UpdateBasicProfileRequest:
    #   $ref: ./components/requests/users/profile/put.request.yml
    # UpdateBusinessProfileRequest:
    #   $ref: ./components/requests/users/business/put.request.yml
    UpdateSellerBankAccountRequest:
      $ref: ./components/requests/users/seller/bank-account/put.request.yml
    SettingPinRequest:
      $ref: ./components/requests/users/pin/post.request.yml
    UpdatePinRequest:
      $ref: ./components/requests/users/pin/put.request.yml
    CheckPinRequest:
      $ref: ./components/requests/users/pin/check/post.request.yml
    UpdatePasswordRequest:
      $ref: ./components/requests/users/password/put.request.yml
    UpdateEmailRequest:
      $ref: ./components/requests/users/email/put.request.yml
    VerifyEmailRequest:
      $ref: ./components/requests/users/email/verify/post.request.yml
    NotificationSettingsRequest:
      $ref: ./components/requests/users/notification-settings/put.request.yml
    CreateOrderRequest:
      $ref: ./components/requests/orders/post.request.yml
    PurchaseOrderRequest:
      $ref: ./components/requests/orders/id/purchase.request.yml
    AddFavoriteProductRequest:
      $ref: ./components/requests/users/favorites/products/post.request.yml
    DeleteFavoriteProductsRequest:
      $ref: ./components/requests/users/favorites/products/delete.request.yml
    AddFavoriteSellersRequest:
      $ref: ./components/requests/users/favorites/sellers/post.request.yml
    DeleteFavoriteSellersRequest:
      $ref: ./components/requests/users/favorites/sellers/delete.request.yml
    SearchProductsRequest:
      $ref: ./components/requests/products/search/post.request.yml
    CreateThreadRequest:
      $ref: ./components/requests/threads/post.request.yml
    SendMessageRequest:
      $ref: ./components/requests/messages/post.request.yml
    InquiryCreateRequest:
      $ref: ./components/requests/inquiry/post.request.yml
    SubscriptionMembershipRequest:
      $ref: ./components/requests/users/subscription-membership/post.request.yml
    UpdateUserTermsRequest:
      $ref: ./components/requests/users/terms/put.request.yml
    ProductUpdateRequest:
      $ref: ./components/requests/sellers/products/put.request.yml
    UploadFileRequest:
      $ref: ./components/requests/uploads/put.request.yml
    BlockUserRequest:
      $ref: ./components/requests/sellers/block-users/post.request.yml
    ProductRequest:
      $ref: ./components/requests/sellers/products/post.request.yml
    CreateReviewRequest:
      $ref: ./components/requests/orders/review/post.request.yml
    UpdatePublishSellerRequest:
      $ref: ./components/requests/sellers/publish-status/put.request.yml
    CancelSubscriptionAndroidRequest:
      $ref: ./components/requests/users/subscription-membership/cancel/cancel.request.android.yml
    CancelSubscriptionIOSRequest:
      $ref: ./components/requests/users/subscription-membership/cancel/cancel.request.ios.yml
    CancelAccountRequest:
      $ref: ./components/requests/users/cancel/post.request.yml
    UpdateSoldItemStatusRequest:
      $ref: ./components/requests/seller/update-sold-item-status-request.yml
    UpsertFCMTokenRequest:
      $ref: ./components/requests/users/fcm-token/put.request.yml
    DeleteFCMTokenRequest:
      $ref: ./components/requests/users/fcm-token/delete.request.yml
    # Response
    ErrorResponse:
      $ref: ./components/responses/error.yml
    AuthResponse:
      $ref: ./components/responses/auth/post.response.yml
    GetThreadsResponse:
      $ref: ./components/responses/threads/get.response.yml
    GetThreadByIdResponse:
      $ref: ./components/responses/threads/id/get.response.yml
    CreateThreadResponse:
      $ref: ./components/responses/threads/post.response.yml
    GetMessagesResponse:
      $ref: ./components/responses/threads/messages/get.response.yml
    StatusResponse:
      $ref: ./components/responses/system/status/get.response.yml
    HomeFeedsResponse:
      $ref: ./components/responses/home-feeds/post.response.yml
    SellerDashboardResponse:
      $ref: ./components/responses/sellers/dashboard/get.response.yml
    # FollowedSellersProductsResponse:
    #   $ref: ./components/responses/followed-sellers/products/get.response.yml
    GetBrandsResponse:
      $ref: ./components/responses/brands/get.response.yml
    GetProductCategoriesResponse:
      $ref: ./components/responses/product-categories/get.response.yml
    GetNotificationsResponse:
      $ref: ./components/responses/users/notifications/get.response.yml
    CountUnreadNotificationsResponse:
      $ref: ./components/responses/users/notifications/unread-count/get.response.yml
    CountUnreadThreadsResponse:
      $ref: ./components/responses/users/threads/unread-count/get.response.yml
    UserProfileResponse:
      $ref: ./components/responses/users/profile/get.response.yml
    SellerProfileResponse:
      $ref: ./components/responses/users/seller/get.response.yml
    NotificationSettingsResponse:
      $ref: ./components/responses/users/notification-settings/get.response.yml
    VerifyCreditCardResponse:
      $ref: ./components/responses/credit-cards/post.response.yml
    CreateVaultSetupTokenResponse:
      $ref: ./components/responses/vault/setup-token/post.response.yml
    CreateVaultPaymentTokenResponse:
      $ref: ./components/responses/vault/payment-token/post.response.yml
    UpdatePaymentMethodsResponse:
      $ref: ./components/responses/users/payment-methods/put.response.yml
    # BusinessProfileResponse:
    #   $ref: ./components/responses/users/business/put.response.yml
    # PasswordResponse:
    #   $ref: ./components/responses/users/password/put.response.yml
    # CancelMembershipResponse:
    #   $ref: ./components/responses/users/cancel/put.response.yml
    # PurchasesResponse:
    #   $ref: ./components/responses/users/purchases/get.response.yml
    GetFavoriteProductsResponse:
      $ref: ./components/responses/users/favorites/products/get.response.yml
    GetFavoriteSellersResponse:
      $ref: ./components/responses/users/favorites/sellers/get.response.yml
    CartItemsResponse:
      $ref: ./components/responses/cart/items/get.response.yml
    CountriesResponse:
      $ref: ./components/responses/countries/get.response.yml
    FaqsResponse:
      $ref: ./components/responses/faqs/get.response.yml
    TermsResponse:
      $ref: ./components/responses/terms/get.response.yml
    SearchProductsResponse:
      $ref: ./components/responses/products/search/post.response.yml
    SellerProductsResponse:
      $ref: ./components/responses/sellers/products/get.response.yml
    RatingSellerResponse:
      $ref: ./components/responses/sellers/rating/get.response.yml
    UploadFileResponse:
      $ref: ./components/responses/uploads/put.response.yml
    GetBannersResponse:
      $ref: ./components/responses/banners/get.response.yml
    BlockUsersResponse:
      $ref: ./components/responses/sellers/block-users/get.response.yml
    PurchasedItemsResponse:
      $ref: ./components/responses/purchased-items/get.response.yml
    CreateOrderResponse:
      $ref: ./components/responses/orders/post.response.yml
    GetStatusOrderDetailResponse:
      $ref: ./components/responses/sellers/order/status/get.response.yml
    FollowingUserResponse:
      $ref: ./components/responses/sellers/following-users/get.response.yml
    DailySalesSummaryResponse:
      $ref: ./components/responses/sales-summary/daily/get.response.yml
    MonthlySalesSummaryResponse:
      $ref: ./components/responses/sales-summary/monthly/get.response.yml
    YearlySalesSummaryResponse:
      $ref: ./components/responses/sales-summary/yearly/get.response.yml
    OverallSalesSummaryResponse:
      $ref: ./components/responses/sales-summary/overall/get.response.yml
    SalesInsightsResponse:
      $ref: ./components/responses/sales-insights/get.response.yml
    PostalCodesResponse:
      $ref: ./components/responses/postal-codes/get.response.yml
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
