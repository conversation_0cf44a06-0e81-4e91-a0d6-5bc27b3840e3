operationId: create-vault-payment-token
summary: Create Vault Payment Token
description: Create a PayPal vault payment token from a setup token for future payments
tags:
  - payment
security:
  - bearerAuth: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../../../index.yml#/components/schemas/CreateVaultPaymentTokenRequest'
responses:
  '200':
    description: Successfully created vault payment token
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/CreateVaultPaymentTokenResponse'
  '400':
    description: Bad Request (Validation Error)
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '500':
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
