operationId: create-vault-setup-token
summary: Create Vault Setup Token
description: Create a PayPal vault setup token for secure payment method storage
tags:
  - payment
security:
  - bearerAuth: []
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../../../index.yml#/components/schemas/CreateVaultSetupTokenRequest'
responses:
  '200':
    description: Successfully created vault setup token
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/CreateVaultSetupTokenResponse'
  '400':
    description: Bad Request (Validation Error)
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '500':
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
