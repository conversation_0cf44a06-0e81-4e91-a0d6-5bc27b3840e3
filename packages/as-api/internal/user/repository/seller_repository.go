package repository

import (
	"context"
	"log"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func init() {
	if err := di.RegisterProviders(NewSellerRepository); err != nil {
		log.Fatal("register constructor seller repository failed:", err)
	}
}

type SellerRepository interface {
	Create(ctx context.Context, seller *entities.Seller) (*entities.Seller, error)
	Update(ctx context.Context, id string, seller *entities.Seller) (*entities.Seller, error)
	First(ctx context.Context, id string) (*entities.Seller, error)
	GetSellersByIDs(ctx context.Context, ids ...string) ([]*entities.Seller, error)
	GetAllSellers(ctx context.Context) ([]*entities.Seller, error)
	BlockUser(ctx context.Context, blockUser *entities.SellerBlockUser) error
	DeleteBlockUser(ctx context.Context, blockUserId string) error
	GetListBlockUsers(ctx context.Context, filter map[string]any, page, limit int) ([]*entities.SellerBlockUser, error)
	IsUserBlockedBySeller(ctx context.Context, sellerID, userID string) (*bool, error)
}

type sellerRepository struct {
	db *factory.DO
}

func NewSellerRepository(db *factory.DO) SellerRepository {
	return &sellerRepository{
		db: db,
	}
}

func (r *sellerRepository) Create(ctx context.Context, seller *entities.Seller) (*entities.Seller, error) {
	db := r.db.Model(nil)

	err := db.Query.Seller.WithContext(ctx).Create(seller)
	if err != nil {
		return nil, errors.Wrap(err, "Create")
	}

	return seller, nil
}

func (r *sellerRepository) Update(ctx context.Context, id string, seller *entities.Seller) (*entities.Seller, error) {
	db := r.db.Model(nil)
	s := db.Query.Seller

	// Update seller in database
	_, err := s.WithContext(ctx).Where(s.ID.Eq(id)).Updates(seller)
	if err != nil {
		return nil, errors.Wrap(err, "Update")
	}

	return seller, nil
}

func (r *sellerRepository) First(ctx context.Context, id string) (*entities.Seller, error) {
	db := r.db.Model(nil)
	s := db.Query.Seller

	// Find seller by ID
	seller, err := s.WithContext(ctx).Where(s.ID.Eq(id)).Preload(field.Associations).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apiutil.ErrResourceNotFound
		}
		return nil, errors.Wrap(err, "First")
	}

	return seller, nil
}

func (r *sellerRepository) GetSellersByIDs(ctx context.Context, ids ...string) ([]*entities.Seller, error) {
	db := r.db.Model(nil)
	s := db.Query.Seller

	sellers, err := s.WithContext(ctx).Where(s.ID.In(ids...)).Find()
	if err != nil {
		return nil, errors.Wrap(err, "GetSellersByIDs")
	}

	return sellers, nil
}

func (r *sellerRepository) GetAllSellers(ctx context.Context) ([]*entities.Seller, error) {
	db := r.db.Model(nil)
	s := db.Query.Seller

	sellers, err := s.WithContext(ctx).Find()
	if err != nil {
		return nil, errors.Wrap(err, "GetAllSellers")
	}

	return sellers, nil
}

func (r *sellerRepository) BlockUser(ctx context.Context, blockUser *entities.SellerBlockUser) error {
	db := r.db.Model(nil)
	s := db.Query.SellerBlockUser

	err := s.WithContext(ctx).Create(blockUser)
	if err != nil {
		return errors.Wrap(err, "BlockUser")
	}

	return nil
}

func (r *sellerRepository) DeleteBlockUser(ctx context.Context, blockUserId string) error {
	db := r.db.Model(nil)
	s := db.Query.SellerBlockUser

	if _, err := s.WithContext(ctx).Where(s.ID.Eq(blockUserId)).Delete(); err != nil {
		return errors.Wrap(err, "delete block user id")
	}

	return nil
}

func (r *sellerRepository) GetListBlockUsers(ctx context.Context, filter map[string]any, page, limit int) ([]*entities.SellerBlockUser, error) {
	db := r.db.Model(nil)
	s := db.Query.SellerBlockUser
	q := s.WithContext(ctx)

	for k, v := range filter {
		switch k {
		case "seller_id":
			sellerID, ok := v.(string)
			if !ok || sellerID == "" {
				continue
			}
			q = q.Where(s.SellerID.Eq(sellerID))
		}
	}

	if limit > 0 {
		q = q.Limit(limit)
	}

	if page > 0 {
		q = q.Offset((page - 1) * limit)
	}

	data, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "GetListBlockUsers")
	}

	return data, nil
}

func (r *sellerRepository) IsUserBlockedBySeller(ctx context.Context, sellerID, userID string) (*bool, error) {
	db := r.db.Model(nil)
	s := db.Query.SellerBlockUser

	count, err := s.WithContext(ctx).Where(s.SellerID.Eq(sellerID), s.UserID.Eq(userID)).Count()
	if err != nil {
		return nil, errors.Wrap(err, "query count blocked user")
	}

	return pointer.Ptr(count > 0), nil
}
