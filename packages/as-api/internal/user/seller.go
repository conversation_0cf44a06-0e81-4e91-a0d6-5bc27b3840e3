package user

import (
	"context"

	"as-api/as/foundations/db/entities"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pagination"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

type PublishStatus string

const (
	PublishStatusPublish PublishStatus = "publish"
	PublishStatusDraft   PublishStatus = "draft"
)

func (s *PublishStatus) String() string {
	return string(pointer.Safe(s))
}

type Seller struct {
	ID             *string
	About          *map[string]string
	AccountType    string
	AvatarUrl      *string
	FavoriteBrands *[]string
	HeaderImgUrl   *string
	ShopName       string
	Specialty      *map[string]string
	StockLocation  *string
	Kyc            *Kyc
	Address        *Address
	BankAccount    *BankAccount
	CompanyInfo    *CompanyInfo
	PublicStatus   *PublishStatus
	CountryCode    *string
	RegionID       *string
	AccountID      *string
	RegionId       *string
}

type Kyc struct {
	KycType   string `json:"kycType"`
	KycImgUrl string `json:"kycImgUrl"`
}

type BankAccount struct {
	AccountHolderKatakana *string `json:"accountHolderKatakana,omitempty"`
	AccountHolderName     string  `json:"accountHolderName"`
	AccountNumber         string  `json:"accountNumber"`
	AccountType           string  `json:"accountType"`
	BankCode              *string `json:"bankCode,omitempty"`
	BankName              string  `json:"bankName"`
	BranchCode            *string `json:"branchCode,omitempty"`
	BranchName            *string `json:"branchName,omitempty"`
	Country               *string `json:"country,omitempty"`
	Iban                  *string `json:"iban,omitempty"`
	Pin                   *string `json:"pin,omitempty"`
	RoutingNumber         *string `json:"routingNumber,omitempty"`
	SwiftCode             *string `json:"swiftCode,omitempty"`
}

type CompanyInfo struct {
	CompanyName *string `json:"companyName,omitempty"`
	ContactName *string `json:"contactName,omitempty"`
	DunsNumber  *string `json:"dunsNumber,omitempty"`
	Email       *string `json:"email,omitempty"`
	PhoneNumber *string `json:"phoneNumber,omitempty"`
	WebsiteURL  *string `json:"websiteURL,omitempty"`
}

type SellerBlockUser struct {
	Id       string
	SellerId string
	UserId   string
}

func (u *user) RegisterSeller(ctx context.Context, seller *Seller) (*Seller, error) {
	// Convert from domain model Seller to entity Seller
	sellerEntity, err := ParseSellerToEntity(seller)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller to entity")
	}

	// Create seller
	createdSeller, err := u.sr.Create(ctx, sellerEntity)
	if err != nil {
		return nil, errors.Wrap(err, "create seller")
	}

	// Convert entity seller back to domain model
	result, err := ParseSellerFromEntity(createdSeller)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller from entity")
	}

	return result, nil
}

func (u *user) CreateSeller(ctx context.Context, uid string, seller *Seller) (*Seller, error) {
	// First find user by ID
	user, err := u.r.First(ctx, uid)
	if err != nil {
		return nil, errors.Wrap(err, "get user by id")
	}

	if user.SellerID != nil {
		return nil, errors.New("user already has a seller")
	}

	// Set account_id from user
	seller.AccountID = user.AccountID

	// Convert from domain model Seller to entity Seller
	sellerEntity, err := ParseSellerToEntity(seller)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller to entity")
	}

	// Create seller
	createdSeller, err := u.sr.Create(ctx, sellerEntity)
	if err != nil {
		return nil, errors.Wrap(err, "create seller")
	}

	// Update User's SellerID
	user.SellerID = createdSeller.ID
	_, err = u.r.Update(ctx, uid, user)
	if err != nil {
		return nil, errors.Wrap(err, "update user seller_id")
	}

	// Convert entity seller back to domain model
	result, err := ParseSellerFromEntity(createdSeller)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller from entity")
	}

	return result, nil
}

func (u *user) ReadSellerByID(ctx context.Context, id string) (*Seller, error) {
	// Get seller from database
	sellerEntity, err := u.sr.First(ctx, id)
	if err != nil {
		return nil, errors.Wrap(err, "First")
	}

	if sellerEntity == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "seller not found")
	}

	// Convert to domain model
	seller, err := ParseSellerFromEntity(sellerEntity)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller from entity")
	}

	return seller, nil
}

func (u *user) GetSellersByIDs(ctx context.Context, ids ...string) ([]*Seller, error) {
	sellerEntities, err := u.sr.GetSellersByIDs(ctx, ids...)
	if err != nil {
		return nil, errors.Wrap(err, "get sellers by ids")
	}

	sellerMap := make(map[string]*Seller, len(sellerEntities))
	for _, sellerEntity := range sellerEntities {
		seller, err := ParseSellerFromEntity(sellerEntity)
		if err != nil {
			return nil, errors.Wrap(err, "parse seller from entity")
		}

		sellerMap[*seller.ID] = seller
	}

	sellers := make([]*Seller, 0, len(sellerMap))
	for _, id := range ids {
		seller, ok := sellerMap[id]
		if !ok {
			return nil, errors.Wrap(apiutil.ErrResourceNotFound, "seller not found")
		}
		sellers = append(sellers, seller)
	}

	return sellers, nil
}

func (u *user) UpdateSeller(ctx context.Context, uid string, seller *Seller) (*Seller, error) {
	// First find user by ID to check if user has a seller
	user, err := u.r.First(ctx, uid)
	if err != nil {
		return nil, errors.Wrap(err, "get user by id")
	}

	// Check if user has a seller
	if user.SellerID == nil {
		return nil, errors.New("user does not have a seller account")
	}

	// Get seller ID from user
	sellerID := *user.SellerID

	// Check if provided seller ID matches with user's seller ID
	if seller.ID != nil && *seller.ID != sellerID {
		return nil, errors.New("provided seller ID does not match with user's seller ID")
	}

	// Convert from domain model Seller to entity Seller
	sellerEntity, err := ParseSellerToEntity(seller)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller to entity")
	}

	// Set the ID explicitly
	sellerEntity.ID = &sellerID

	// Update seller
	updatedSeller, err := u.sr.Update(ctx, sellerID, sellerEntity)
	if err != nil {
		return nil, errors.Wrap(err, "update seller")
	}

	// Convert entity seller back to domain model
	result, err := ParseSellerFromEntity(updatedSeller)
	if err != nil {
		return nil, errors.Wrap(err, "parse seller from entity")
	}

	return result, nil
}

func (a *user) BlockUser(ctx context.Context, blockUser SellerBlockUser) error {
	if err := a.sr.BlockUser(ctx, &entities.SellerBlockUser{
		UserID:   blockUser.UserId,
		SellerID: blockUser.SellerId,
	}); err != nil {
		return errors.Wrap(err, "sr.BlockUser")
	}

	return nil
}

func (a *user) DeleteBlockUser(ctx context.Context, blockUserId string) error {
	if err := a.sr.DeleteBlockUser(ctx, blockUserId); err != nil {
		return errors.Wrap(err, "sr.DeleteBlockUser")
	}

	return nil
}

func (a *user) GetListBlockUsers(ctx context.Context, req *GetListBlockUsersRequest) ([]*SellerBlockUser, error) {
	page, limit := pagination.Paginate(pointer.Safe(req.Page), pointer.Safe(req.Limit))
	filter := map[string]any{
		"seller_id": req.SellerId,
	}

	data, err := a.sr.GetListBlockUsers(ctx, filter, page, limit)
	if err != nil {
		return nil, errors.Wrap(err, "sr.GetListBlockUsers")
	}

	sellerBlockUsers := make([]*SellerBlockUser, 0, len(data))
	for _, sellerBlockUser := range data {
		sellerBlockUsers = append(sellerBlockUsers, ParseEntityToSellerBlockUser(sellerBlockUser))
	}

	return sellerBlockUsers, nil
}

func (a *user) IsUserBlockedBySeller(ctx context.Context, sellerID, userID string) (*bool, error) {
	result, err := a.sr.IsUserBlockedBySeller(ctx, sellerID, userID)
	if err != nil {
		return nil, errors.Wrap(err, "query is user blocked by seller")
	}

	return result, nil
}

func (u *user) GetAllSellers(ctx context.Context) ([]*Seller, error) {
	sellerEntities, err := u.sr.GetAllSellers(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get all sellers")
	}

	sellers := make([]*Seller, 0, len(sellerEntities))
	for _, sellerEntity := range sellerEntities {
		seller, err := ParseSellerFromEntity(sellerEntity)
		if err != nil {
			return nil, errors.Wrap(err, "parse seller from entity")
		}

		sellers = append(sellers, seller)
	}

	return sellers, nil
}
