package user

import (
	"context"
	"time"

	"as-api/as/foundations/db/entities"
	"as-api/as/internal/user/repository"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pagination"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
	"golang.org/x/crypto/bcrypt"
)

type AccountStatus = string

const (
	AccountStatusActive     AccountStatus = "active"
	AccountStatusInreview   AccountStatus = "inreview"
	AccountStatusRestricted AccountStatus = "restricted"
	AccountStatusSuspended  AccountStatus = "suspended"
	AccountStatusViewonly   AccountStatus = "viewonly"
)

type Phone struct {
	CountryCode string `json:"countryCode,omitempty"`
	Number      string `json:"number,omitempty"`
}

// Address struct represents a user address
type Address struct {
	IsDifferentFromResidence bool    `json:"isDifferentFromResidence,omitempty"`
	Address1                 *string `json:"address1,omitempty"`
	Address2                 *string `json:"address2,omitempty"`
	City                     *string `json:"city,omitempty"`
	State                    *string `json:"state,omitempty"`
	Country                  *string `json:"country,omitempty"`
	PostalCode               *string `json:"postalCode,omitempty"`
	FirstName                *string `json:"firstName,omitempty"`
	LastName                 *string `json:"lastName,omitempty"`
	FullName                 *string `json:"fullName,omitempty"`
	Phone                    *Phone  `json:"phone,omitempty"`
	Mobile                   *Phone  `json:"mobile,omitempty"`
	Type                     *string `json:"type,omitempty"` // "home", "shipping", "billing"
	RegionId                 *string `json:"regionId,omitempty"`
}

type CreditCard struct {
	CardHolderName *string `json:"card_holder_name,omitempty"`
	CardNumber     *string `json:"card_number,omitempty"`
	ExpiryMonth    *string `json:"expiry_month,omitempty"`
	ExpiryYear     *string `json:"expiry_year,omitempty"`
	Brand          *string `json:"brand,omitempty"`
	CardID         *string `json:"card_id,omitempty"`
}

type CancelReason struct {
	PricesTooHigh       *bool   `json:"pricesTooHigh,omitempty"`
	DeliveryTooLong     *bool   `json:"deliveryTooLong,omitempty"`
	ProductsUnavailable *bool   `json:"productsUnavailable,omitempty"`
	ItemProblem         *bool   `json:"itemProblem,omitempty"`
	SellerUntrustworthy *bool   `json:"sellerUntrustworthy,omitempty"`
	AppDifficultToUse   *bool   `json:"appDifficultToUse,omitempty"`
	TooManyEmails       *bool   `json:"tooManyEmails,omitempty"`
	SituationChanged    *bool   `json:"situationChanged,omitempty"`
	DontKnowHowToList   *bool   `json:"dontKnowHowToList,omitempty"`
	ItemsDidNotSell     *bool   `json:"itemsDidNotSell,omitempty"`
	OtherReason         *string `json:"otherReason,omitempty"`
}

type User struct {
	ID                 *string
	SellerID           *string
	Name               *string
	Email              string
	PhoneNumber        *string
	CreatedAt          *time.Time
	UpdatedAt          *time.Time
	DeletedAt          *time.Time
	FirstName          *string
	LastName           *string
	Nickname           *string
	Gender             *string
	DateOfBirth        *time.Time
	HomeAddress        *Address
	ShippingAddress    *Address
	BillingAddress     *Address
	CreditCard         *CreditCard
	PinSetting         bool
	CountryCode        *string
	RegionID           *string
	ReceiveNewsletter  *bool
	AccountId          *string
	Status             *string
	Membership         *MembershipType
	ConfirmTermDate    *time.Time
	IsTermsRequired    *bool
	Language           *string
	EmailNotifications *EmailNotificationSettings
	PushNotification   *PushNotificationSettings
	CancelReason       *CancelReason
}

type UserSearchRequest struct {
	AccountId   *string
	Nickname    *string
	Email       *string
	AccountType *string // *SearchUsersParamsAccountType
	Status      *string //*SearchUsersParamsStatus
	CountryCode *string //*SearchUsersParamsCountryCode
	RegionId    *string //*SearchUsersParamsRegionId
	PostalCode  *string //*SearchUsersParamsPostalCode
	Unscope     *bool   //*SearchUsersParamsUnscope
	Page        *int
	Limit       *int
}

type UserSearchResponse struct {
	Data       []User
	TotalCount int
}

type GetListEmailsParams struct {
	NotificationSettings NotificationSettingsType
	RegionIds            []string
	CountryCodes         []string
	Language             []string
}

type UserFavoriteSeller struct {
	UserId   string
	SellerId string
}

type GetListBlockUsersRequest struct {
	SellerId string
	Page     *int
	Limit    *int
}

type NotificationSettingsType string

const (
	All       NotificationSettingsType = "all"
	Campaigns NotificationSettingsType = "campaigns"
	Important NotificationSettingsType = "important"
)

type MembershipType = int

const (
	// Membership indicates a user has a membership.
	Membership MembershipType = 1
	// NoMembership indicates a user does not have a membership.
	NoMembership MembershipType = 0
)

type UserDomain interface {
	ReadOne(ctx context.Context, id string) (*User, error)
	ReadOneBySellerID(ctx context.Context, sellerID string) (*User, error)
	FindByEmail(ctx context.Context, email string) (*User, error)
	Create(ctx context.Context, user *User) (*User, error)
	Update(ctx context.Context, id string, user *User) (*User, error)
	SettingPin(ctx context.Context, id string, pin string) error
	VerifyPin(ctx context.Context, id string, pin *string) error
	RegisterSeller(ctx context.Context, seller *Seller) (*Seller, error)
	CreateSeller(ctx context.Context, uid string, seller *Seller) (*Seller, error)
	UpdateSeller(ctx context.Context, uid string, seller *Seller) (*Seller, error)
	ReadSellerByID(ctx context.Context, id string) (*Seller, error)
	GetSellersByIDs(ctx context.Context, ids ...string) ([]*Seller, error)
	GetAllSellers(ctx context.Context) ([]*Seller, error)
	UpdateNotificationSettings(ctx context.Context, id string, settings *NotificationSettings) error
	GetNotificationSettings(ctx context.Context, id string) (*NotificationSettings, error)
	SearchUsers(ctx context.Context, req *UserSearchRequest) (*UserSearchResponse, error)
	GetListEmails(ctx context.Context, param GetListEmailsParams) ([]string, error)
	BlockUser(ctx context.Context, blockUser SellerBlockUser) error
	UpdateMembership(ctx context.Context, id string, membership int32) error
	DeleteBlockUser(ctx context.Context, blockUserId string) error
	GetListBlockUsers(ctx context.Context, req *GetListBlockUsersRequest) ([]*SellerBlockUser, error)
	CancelAccount(ctx context.Context, id string, user *User) error
	GetUserLanguage(ctx context.Context, userID string) (string, error)
	IsUserBlockedBySeller(ctx context.Context, sellerID, userID string) (*bool, error)
}

type user struct {
	r  repository.Repository
	sr repository.SellerRepository
}

func New(r repository.Repository, sr repository.SellerRepository) UserDomain {
	return &user{r: r, sr: sr}
}

func (u *user) FindByEmail(ctx context.Context, email string) (*User, error) {
	entity, err := u.r.FindByEmail(ctx, email)
	if err != nil {
		return nil, errors.Wrap(err, "FindByEmail")
	}

	return ParseUserFromEntities(entity), nil
}

func (u *user) ReadOneBySellerID(ctx context.Context, sellerID string) (*User, error) {
	entity, err := u.r.FindBySellerID(ctx, sellerID)
	if err != nil {
		return nil, errors.Wrap(err, "ReadOneBySellerID")
	}

	return ParseUserFromEntities(entity), nil
}

func (u *user) Create(ctx context.Context, userData *User) (*User, error) {
	// Convert from domain model to entity using mapping function
	userEntity, err := ParseUserToEntity(userData)
	if err != nil {
		return nil, errors.Wrap(err, "parse user to entity")
	}

	// Save to database
	createdUser, err := u.r.Create(ctx, userEntity)
	if err != nil {
		return nil, errors.Wrap(err, "create user")
	}

	// Convert back to domain model
	return ParseUserFromEntities(createdUser), nil
}

func (u *user) Update(ctx context.Context, id string, userData *User) (*User, error) {
	// Convert from domain model to entity, preserving created_at and JSON fields if not provided
	userEntity, err := ParseUserToEntity(userData)
	if err != nil {
		return nil, errors.Wrap(err, "parse user to entity")
	}

	// Update user in database
	updatedEntity, err := u.r.Update(ctx, id, userEntity)
	if err != nil {
		return nil, errors.Wrap(err, "update user")
	}

	// Convert back to domain model
	return ParseUserFromEntities(updatedEntity), nil
}

func (u *user) VerifyPin(ctx context.Context, id string, pin *string) error {
	user, err := u.r.First(ctx, id)
	if err != nil {
		return errors.Wrap(err, "find user")
	}

	if user.Pin == nil && pin == nil {
		return nil
	}

	if user.Pin == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "pin not found")
	}

	if pin == nil {
		return errors.Wrap(apiutil.ErrInvalidRequest, "pin is required")
	}

	return bcrypt.CompareHashAndPassword([]byte(pointer.Safe(user.Pin)), []byte(pointer.Safe(pin)))
}

func (u *user) SettingPin(ctx context.Context, id string, pin string) error {
	hashedPin, err := bcrypt.GenerateFromPassword([]byte(pin), bcrypt.DefaultCost)
	if err != nil {
		return errors.Wrap(err, "hash pin")
	}

	_, err = u.r.Update(ctx, id, &entities.User{Pin: pointer.Ptr(string(hashedPin))})
	if err != nil {
		return errors.Wrap(err, "update pin")
	}

	return nil
}

func (u *user) SearchUsers(ctx context.Context, req *UserSearchRequest) (*UserSearchResponse, error) {
	page, limit := pagination.Paginate(pointer.Safe(req.Page), pointer.Safe(req.Limit))
	filter := map[string]any{
		"account_id":   req.AccountId,
		"nickname":     req.Nickname,
		"email":        req.Email,
		"account_type": req.AccountType,
		"status":       req.Status,
		"country_code": req.CountryCode,
		"region_id":    req.RegionId,
		"postal_code":  req.PostalCode,
		"unscope":      req.Unscope,
	}

	users, count, err := u.r.SearchUsers(ctx, filter, page, limit)
	if err != nil {
		return nil, errors.Wrap(err, "search users")
	}

	if len(users) == 0 {
		return nil, nil
	}

	domainUsers := make([]User, 0, len(users))
	for _, userRecord := range users {
		userEntity := ParseUserFromEntities(userRecord)
		domainUsers = append(domainUsers, pointer.Safe(userEntity))
	}

	return &UserSearchResponse{
		Data:       domainUsers,
		TotalCount: count,
	}, nil
}

func (u *user) GetListEmails(ctx context.Context, param GetListEmailsParams) ([]string, error) {
	userEntities, _, err := u.r.SearchUsers(ctx, map[string]any{
		"region_ids":    param.RegionIds,
		"country_codes": param.CountryCodes,
		"languages":     param.Language,
	}, 0, 0)
	if err != nil {
		return nil, errors.Wrap(err, "repo.SearchUsers")
	}

	emailsResult := make([]string, 0, len(userEntities))
	for _, entity := range userEntities {
		domainUser := ParseUserFromEntities(entity)
		email := domainUser.Email
		settings := domainUser.EmailNotifications

		switch param.NotificationSettings {
		case Campaigns:
			if pointer.Safe(settings.CampaignsAndPromotions) {
				emailsResult = append(emailsResult, email)
			}
		case Important:
			if pointer.Safe(settings.ImportantNotices) {
				emailsResult = append(emailsResult, email)
			}
		default:
			emailsResult = append(emailsResult, email)
		}
	}

	return emailsResult, nil
}

func (u *user) UpdateMembership(ctx context.Context, id string, membership int32) error {
	userEntity, err := u.r.First(ctx, id)
	if err != nil {
		return errors.Wrap(err, "update membership")
	}

	userEntity.Membership = &membership
	_, err = u.r.Update(ctx, id, userEntity)
	if err != nil {
		return errors.Wrap(err, "update membership")
	}

	return nil
}

func (u *user) CancelAccount(ctx context.Context, id string, user *User) error {
	userEntity, err := ParseUserToEntity(user)
	if err != nil {
		return errors.Wrap(err, "parse user to entity")
	}

	err = u.r.CancelAccount(ctx, id, userEntity)
	if err != nil {
		return errors.Wrap(err, "cancel account")
	}

	return nil
}

// GetUserLanguage retrieves the language preference for a user
func (u *user) GetUserLanguage(ctx context.Context, userID string) (string, error) {
	if userID == "" {
		return "en", errors.New("user ID is required")
	}

	// Get user entity from repository
	userEntity, err := u.r.First(ctx, userID)
	if err != nil {
		return "en", errors.Wrap(err, "failed to get user")
	}

	// Return user's language preference or default to English
	if userEntity.Language != nil && *userEntity.Language != "" {
		return *userEntity.Language, nil
	}

	// Default to English if no language preference is set
	return "en", nil
}
