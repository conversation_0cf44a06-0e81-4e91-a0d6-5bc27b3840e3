package service

import (
	"errors"
	"strings"
	"testing"
	"time"

	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/env"
	"as-api/as/foundations/gmoapi"
	"as-api/as/internal/auth"
	"as-api/as/internal/payment"
	"as-api/as/internal/user"
	authmock "as-api/as/mocks/domains/auth"
	"as-api/as/mocks/domains/email"
	ordermock "as-api/as/mocks/domains/order"
	paymentmock "as-api/as/mocks/domains/payment"
	usermock "as-api/as/mocks/domains/user"
	mockvalidator "as-api/as/mocks/validator"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/jwt"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test for RegisterUser function
func TestRegisterUser(t *testing.T) {
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	email := "<EMAIL>"
	firstName := "Test"
	lastName := "User"
	authID := "auth123"

	// Create test cases
	tests := []struct {
		name    string
		request dtos.RegisterUserRequest
		mock    func(ctx context.Context)
		assert  func(t *testing.T, err error)
	}{
		{
			name: "Successful_Registration",
			request: dtos.RegisterUserRequest{
				FirstName: &firstName,
				LastName:  &lastName,
				Password:  "password123",
			},
			mock: func(ctx context.Context) {
				// Set up validator
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				// Setup auth service
				authData := &auth.Auth{
					ID:       authID,
					Username: email,
					UserID:   nil, // No user attached yet
				}
				mockAuthDomain.On("FindOneByID", ctx, authID).Return(authData, nil).Once()

				// Check email doesn't exist
				mockUserDomain.On("FindByEmail", ctx, email).Return(nil, apiutil.ErrResourceNotFound).Once()

				// Create user success
				createdUser := &user.User{
					Email:     email,
					FirstName: &firstName,
					LastName:  &lastName,
				}
				mockUserDomain.On("Create", ctx, mock.Anything).Return(createdUser, nil).Once()

				// Attach user ID in auth
				mockAuthDomain.On("AttachUserID", ctx, authID, mock.Anything, "password123").Return(nil).Once()

				// Generate JWT
				mockAuthDomain.On("GenJWT", mock.Anything, mock.Anything).Return(&auth.AuthToken{AccessToken: "accessToken", RefreshToken: "refreshToken"}, nil).Once()
			},
			assert: func(t *testing.T, err error) {
				assert.NoError(t, err)
			},
		},
		{
			name: "Validation_Error",
			request: dtos.RegisterUserRequest{
				Password: "123", // Password too short
			},
			mock: func(ctx context.Context) {
				// Set up validator to return error
				mockValidator.On("Validate", mock.Anything).Return(errors.New("password too short")).Once()
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "validate: password too short")
			},
		},
		{
			name: "Auth_Not_Found",
			request: dtos.RegisterUserRequest{
				FirstName: &firstName,
				LastName:  &lastName,
				Password:  "password123",
			},
			mock: func(ctx context.Context) {
				// Set up validator
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				// Auth not found
				mockAuthDomain.On("FindOneByID", ctx, authID).Return(nil, apiutil.ErrResourceNotFound).Once()
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "find auth by ID: EC404")
			},
		},
		{
			name: "User_Already_Registered",
			request: dtos.RegisterUserRequest{
				FirstName: &firstName,
				LastName:  &lastName,
				Password:  "password123",
			},
			mock: func(ctx context.Context) {
				// Set up validator
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				// Auth has user already
				userID := "user123"
				authData := &auth.Auth{
					ID:       authID,
					Username: email,
					UserID:   &userID,
				}
				mockAuthDomain.On("FindOneByID", ctx, authID).Return(authData, nil).Once()
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "user already registered")
			},
		},
		{
			name: "Email_Already_Exists",
			request: dtos.RegisterUserRequest{
				FirstName: &firstName,
				LastName:  &lastName,
				Password:  "password123",
			},
			mock: func(ctx context.Context) {
				// Set up validator
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				// Auth found
				auth := &auth.Auth{
					ID:       authID,
					Username: "<EMAIL>",
					UserID:   nil,
				}
				mockAuthDomain.On("FindOneByID", ctx, authID).Return(auth, nil).Once()

				// Set expectation but don't need to be called due to early return
				// after FindByEmail returns a user
				mockUserDomain.On("FindByEmail", ctx, auth.Username).Return(&user.User{}, nil).Once()
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "user with this email already exists")
			},
		},
		{
			name: "Database_Error",
			request: dtos.RegisterUserRequest{
				FirstName: &firstName,
				LastName:  &lastName,
				Password:  "password123",
			},
			mock: func(ctx context.Context) {
				// Set up validator
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				// Setup auth service
				authData := &auth.Auth{
					ID:       authID,
					Username: email,
					UserID:   nil, // No user attached yet
				}
				mockAuthDomain.On("FindOneByID", ctx, authID).Return(authData, nil).Once()

				// Email check passes
				mockUserDomain.On("FindByEmail", ctx, email).Return(nil, apiutil.ErrResourceNotFound).Once()

				// Database error when creating user
				mockUserDomain.On("Create", ctx, mock.Anything).Return(nil, errors.New("database error")).Once()
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "create user: database error")
			},
		},
	}

	// Run the tests
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create context with JWT claims
			claims := jwt.CustomClaims{
				AID:     authID,
				UID:     "user123",
				IsAdmin: false,
				Type:    "accessToken",
			}
			ctx := context.WithValue(context.Background(), context.ClaimsCtx, claims)

			// Set up mock behavior
			tt.mock(ctx)

			// Call function being tested
			_, err := s.RegisterUser(ctx, tt.request)

			// Check results
			tt.assert(t, err)

			// Verify all mocks
			mockUserDomain.AssertExpectations(t)
			mockAuthDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

// Test for GetUserProfile function
func TestGetUserProfile(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	email := "<EMAIL>"
	firstName := "Test"
	lastName := "User"
	nickname := "Tester"
	gender := "male"
	dobStr := "1990-01-01"
	// Parse date of birth for testing
	dob, _ := time.Parse(time.DateOnly, dobStr)

	// Create test context
	ctx := context.Background()

	// Set up JWT claims with test.jwt package
	jwtClaims := jwt.CustomClaims{
		UID: userID,
	}
	ctx = context.WithValue(ctx, context.ClaimsCtx, jwtClaims)

	tests := []struct {
		name    string
		id      string
		mock    func(ctx context.Context)
		want    *dtos.UserProfileResponse
		wantErr error
	}{
		{
			name: "Successful_Profile_Retrieval",
			id:   userID,
			mock: func(ctx context.Context) {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:          &userID,
					Email:       email,
					FirstName:   &firstName,
					LastName:    &lastName,
					Nickname:    &nickname,
					Gender:      &gender,
					DateOfBirth: &dob,
					HomeAddress: &user.Address{
						IsDifferentFromResidence: false,
						Address1:                 pointer.Ptr("123 Main St"),
						City:                     pointer.Ptr("Test City"),
						Country:                  pointer.Ptr("Test Country"),
					},
					SellerID:        pointer.Ptr("seller123"),
					PinSetting:      true,
					IsTermsRequired: pointer.Ptr(false),
				}, nil).Once()

				// Mock ReadSellerByID
				mockUserDomain.On("ReadSellerByID", ctx, "seller123").Return(&user.Seller{
					ID:          pointer.Ptr("seller123"),
					AccountType: "individual",
					ShopName:    "Test Shop",
					About:       &map[string]string{"en": "Test shop description"},
					AvatarUrl:   pointer.Ptr("https://example.com/avatar.jpg"),
					CompanyInfo: &user.CompanyInfo{
						CompanyName: pointer.Ptr("Test Company"),
						ContactName: pointer.Ptr("John Doe"),
						Email:       pointer.Ptr("<EMAIL>"),
						PhoneNumber: pointer.Ptr("+**********"),
					},
				}, nil).Once()
			},
			want: &dtos.UserProfileResponse{
				User: dtos.User{
					Id:              &userID,
					Email:           &email,
					FirstName:       &firstName,
					LastName:        &lastName,
					Nickname:        &nickname,
					Gender:          &gender,
					DateOfBirth:     &dobStr,
					SellerId:        pointer.Ptr("seller123"),
					PinSetting:      pointer.Ptr(true),
					IsTermsRequired: pointer.Ptr(false),
				},
				HomeAddress: &dtos.Address{
					IsDifferentFromResidence: pointer.Ptr(false),
					Address1:                 pointer.Ptr("123 Main St"),
					City:                     pointer.Ptr("Test City"),
					Country:                  pointer.Ptr("Test Country"),
				},
				Seller: &dtos.Seller{
					Id:          pointer.Ptr("seller123"),
					AccountType: dtos.SellerAccountType("individual"),
					ShopName:    "Test Shop",
					About:       &map[string]string{"en": "Test shop description"},
					AvatarUrl:   pointer.Ptr("https://example.com/avatar.jpg"),
				},
				CompanyInfo: &dtos.CompanyInfo{
					CompanyName: pointer.Ptr("Test Company"),
					ContactName: pointer.Ptr("John Doe"),
					Email:       pointer.Ptr("<EMAIL>"),
					PhoneNumber: pointer.Ptr("+**********"),
				},
			},
			wantErr: nil,
		},
		{
			name: "Successful_Profile_Retrieval_Without_Seller",
			id:   userID,
			mock: func(ctx context.Context) {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Mock user domain ReadOne - user without seller
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:          &userID,
					Email:       email,
					FirstName:   &firstName,
					LastName:    &lastName,
					Nickname:    &nickname,
					Gender:      &gender,
					DateOfBirth: &dob,
					HomeAddress: &user.Address{
						IsDifferentFromResidence: false,
						Address1:                 pointer.Ptr("123 Main St"),
						City:                     pointer.Ptr("Test City"),
						Country:                  pointer.Ptr("Test Country"),
					},
					SellerID:        nil, // No seller
					PinSetting:      true,
					IsTermsRequired: pointer.Ptr(false),
				}, nil).Once()
				// No ReadSellerByID mock needed since SellerID is nil
			},
			want: &dtos.UserProfileResponse{
				User: dtos.User{
					Id:              &userID,
					Email:           &email,
					FirstName:       &firstName,
					LastName:        &lastName,
					Nickname:        &nickname,
					Gender:          &gender,
					DateOfBirth:     &dobStr,
					SellerId:        nil, // No seller
					PinSetting:      pointer.Ptr(true),
					IsTermsRequired: pointer.Ptr(false),
				},
				HomeAddress: &dtos.Address{
					IsDifferentFromResidence: pointer.Ptr(false),
					Address1:                 pointer.Ptr("123 Main St"),
					City:                     pointer.Ptr("Test City"),
					Country:                  pointer.Ptr("Test Country"),
				},
				// No Seller or CompanyInfo fields
			},
			wantErr: nil,
		},
		{
			name: "Invalid_UUID",
			id:   "invalid-uuid",
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").
					Return(errors.New("invalid UUID format")).Once()
			},
			want:    nil,
			wantErr: apiutil.ErrInvalidRequest,
		},
		{
			name: "User_Not_Found",
			id:   userID,
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctx, userID).
					Return(nil, apiutil.ErrResourceNotFound).Once()
			},
			want:    nil,
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name: "Permission_Denied_Wrong_User",
			id:   "different-user-id",
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", "different-user-id", "required,uuid").Return(nil).Once()
				// No need to mock ReadOne as the function will return error before that
			},
			want:    nil,
			wantErr: apiutil.ErrPermissionDenied,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock(ctx)
			got, err := s.GetUserProfile(ctx, tt.id)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, tt.wantErr) || strings.Contains(err.Error(), tt.wantErr.Error()),
					"expected error containing %v, got %v", tt.wantErr, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

// Test for UpdateUserProfile function
func TestUpdateUserProfile(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	firstName := "Updated"
	lastName := "User"
	nickname := "NewNick"
	gender := "female"
	dobStr := "1992-02-02"
	// Parse date of birth for testing
	dob, _ := time.Parse(time.DateOnly, dobStr)

	// Create test context
	ctx := context.Background()

	// Set up JWT claims
	jwtClaims := jwt.CustomClaims{
		UID: userID,
	}
	ctx = context.WithValue(ctx, context.ClaimsCtx, jwtClaims)

	tests := []struct {
		name    string
		id      string
		request dtos.UpdateUserProfileRequest
		mock    func(ctx context.Context)
		wantErr error
	}{
		{
			name: "Successful_Update",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					FirstName:   &firstName,
					LastName:    &lastName,
					Nickname:    &nickname,
					Gender:      &gender,
					DateOfBirth: &dobStr,
				},
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("456 New St"),
					City:       pointer.Ptr("New City"),
					Country:    pointer.Ptr("New Country"),
					PostalCode: pointer.Ptr("54321"),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Old"),
					LastName:  pointer.Ptr("Name"),
				}, nil).Once()

				// Mock user domain Update - we need to capture the updated user
				mockUserDomain.On("Update", ctx, userID, mock.MatchedBy(func(u *user.User) bool {
					// Verify the user was updated correctly
					return u.FirstName != nil && *u.FirstName == firstName &&
						u.LastName != nil && *u.LastName == lastName &&
						u.Nickname != nil && *u.Nickname == nickname &&
						u.Gender != nil && *u.Gender == gender &&
						u.DateOfBirth != nil && u.DateOfBirth.Equal(dob) &&
						u.HomeAddress != nil &&
						u.HomeAddress.Address1 != nil && *u.HomeAddress.Address1 == "456 New St"
				})).Return(&user.User{}, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "Validation_Error",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					Gender: pointer.Ptr("invalid_gender"),
				},
			},
			mock: func(ctx context.Context) {
				// Set up validator to return error
				mockValidator.On("Validate", mock.Anything).Return(errors.New("invalid gender")).Once()
			},
			wantErr: apiutil.ErrInvalidRequest,
		},
		{
			name: "Invalid_UUID",
			id:   "invalid-uuid",
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					FirstName: &firstName,
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID fails
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").
					Return(errors.New("invalid UUID format")).Once()
			},
			wantErr: apiutil.ErrInvalidRequest,
		},
		{
			name: "User_Not_Found",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					FirstName: &firstName,
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// User not found
				mockUserDomain.On("ReadOne", ctx, userID).Return(nil, apiutil.ErrResourceNotFound).Once()
			},
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name: "Wrong_User",
			id:   "different-user-id",
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					FirstName: &firstName,
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", "different-user-id", "required,uuid").Return(nil).Once()
				// No need to mock ReadOne as the function will return error before that
			},
			wantErr: apiutil.ErrPermissionDenied,
		},
		{
			name: "Invalid_Date_Format",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					DateOfBirth: pointer.Ptr("invalid-date"),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Old"),
					LastName:  pointer.Ptr("Name"),
				}, nil).Once()
				// No need to mock Update as the function will return error before that due to invalid date
			},
			wantErr: errors.New("parse date of birth"),
		},
		{
			name: "Database_Error",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					FirstName: &firstName,
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Old"),
					LastName:  pointer.Ptr("Name"),
				}, nil).Once()
				// Database error
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(nil, errors.New("database error")).Once()
			},
			wantErr: errors.New("update user"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock(ctx)
			err := s.UpdateUserProfile(ctx, tt.id, tt.request)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, tt.wantErr) || strings.Contains(err.Error(), tt.wantErr.Error()),
					"expected error containing %v, got %v", tt.wantErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test for UpdateUserProfile US address validation
func TestUpdateUserProfile_USAddressValidation(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockOrderDomain := ordermock.NewOrderDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Order:   mockOrderDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	validUSZip := "10001"
	invalidUSZip := "99999"

	// Create test context
	ctx := context.Background()

	// Set up JWT claims
	jwtClaims := jwt.CustomClaims{
		UID: userID,
	}
	ctx = context.WithValue(ctx, context.ClaimsCtx, jwtClaims)

	tests := []struct {
		name    string
		id      string
		request dtos.UpdateUserProfileRequest
		mock    func(ctx context.Context)
		wantErr error
	}{
		{
			name: "Valid_US_Home_Address",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("New York"),
					State:      pointer.Ptr("NY"),
					Country:    pointer.Ptr("USA"),
					PostalCode: pointer.Ptr(validUSZip),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// Mock ZIP code validation - valid
				mockOrderDomain.On("ValidateUSZipCode", ctx, validUSZip).Return(true, nil).Once()
				// Mock user domain Update
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(&user.User{}, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "Valid_US_Shipping_Address_Different_From_Home",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("New York"),
					State:      pointer.Ptr("NY"),
					Country:    pointer.Ptr("Canada"),
					PostalCode: pointer.Ptr("K1A 0A6"),
				},
				ShippingAddress: &dtos.Address{
					Address1:                 pointer.Ptr("456 Ship Ave"),
					City:                     pointer.Ptr("Boston"),
					State:                    pointer.Ptr("MA"),
					Country:                  pointer.Ptr("USA"),
					PostalCode:               pointer.Ptr(validUSZip),
					IsDifferentFromResidence: pointer.Ptr(true),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// Mock ZIP code validation for shipping address - valid
				mockOrderDomain.On("ValidateUSZipCode", ctx, validUSZip).Return(true, nil).Once()
				// Mock user domain Update
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(&user.User{}, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "Invalid_US_Home_Address_Postal_Code",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("New York"),
					State:      pointer.Ptr("NY"),
					Country:    pointer.Ptr("USA"),
					PostalCode: pointer.Ptr(invalidUSZip),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// Mock ZIP code validation - invalid
				mockOrderDomain.On("ValidateUSZipCode", ctx, invalidUSZip).Return(false, nil).Once()
			},
			wantErr: ErrInvalidUSPostalCode,
		},
		{
			name: "Invalid_US_Shipping_Address_Postal_Code",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("New York"),
					State:      pointer.Ptr("NY"),
					Country:    pointer.Ptr("Canada"),
					PostalCode: pointer.Ptr("K1A 0A6"),
				},
				ShippingAddress: &dtos.Address{
					Address1:                 pointer.Ptr("456 Ship Ave"),
					City:                     pointer.Ptr("Boston"),
					State:                    pointer.Ptr("MA"),
					Country:                  pointer.Ptr("USA"),
					PostalCode:               pointer.Ptr(invalidUSZip),
					IsDifferentFromResidence: pointer.Ptr(true),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// Mock ZIP code validation for shipping address - invalid
				mockOrderDomain.On("ValidateUSZipCode", ctx, invalidUSZip).Return(false, nil).Once()
			},
			wantErr: ErrInvalidUSPostalCode,
		},
		{
			name: "Non_US_Address_Skip_Validation",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("Toronto"),
					State:      pointer.Ptr("ON"),
					Country:    pointer.Ptr("Canada"),
					PostalCode: pointer.Ptr("K1A 0A6"),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// No ZIP code validation for non-US addresses
				// Mock user domain Update
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(&user.User{}, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "Shipping_Address_Same_As_Home_Validate_Home",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("New York"),
					State:      pointer.Ptr("NY"),
					Country:    pointer.Ptr("USA"),
					PostalCode: pointer.Ptr(validUSZip),
				},
				ShippingAddress: &dtos.Address{
					Address1:                 pointer.Ptr("123 Main St"),
					City:                     pointer.Ptr("New York"),
					State:                    pointer.Ptr("NY"),
					Country:                  pointer.Ptr("USA"),
					PostalCode:               pointer.Ptr(validUSZip),
					IsDifferentFromResidence: pointer.Ptr(false), // Same as home
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// Mock ZIP code validation for home address (since shipping is same as home)
				mockOrderDomain.On("ValidateUSZipCode", ctx, validUSZip).Return(true, nil).Once()
				// Mock user domain Update
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(&user.User{}, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "Nil_Addresses_No_Validation",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				User: &dtos.User{
					FirstName: pointer.Ptr("Updated"),
					LastName:  pointer.Ptr("User"),
				},
				// No addresses provided
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// No ZIP code validation for nil addresses
				// Mock user domain Update
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(&user.User{}, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "US_Address_Missing_Postal_Code",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1: pointer.Ptr("123 Main St"),
					City:     pointer.Ptr("New York"),
					State:    pointer.Ptr("NY"),
					Country:  pointer.Ptr("USA"),
					// PostalCode is nil
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// No ZIP code validation mock needed - will fail before that
			},
			wantErr: ErrInvalidUSPostalCode,
		},
		{
			name: "ZIP_Code_Validation_Service_Error",
			id:   userID,
			request: dtos.UpdateUserProfileRequest{
				HomeAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("New York"),
					State:      pointer.Ptr("NY"),
					Country:    pointer.Ptr("USA"),
					PostalCode: pointer.Ptr(validUSZip),
				},
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:        &userID,
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("Test"),
					LastName:  pointer.Ptr("User"),
				}, nil).Once()
				// Mock ZIP code validation service error
				mockOrderDomain.On("ValidateUSZipCode", ctx, validUSZip).Return(false, errors.New("service error")).Once()
			},
			wantErr: errors.New("failed to validate postal code"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock(ctx)
			err := s.UpdateUserProfile(ctx, tt.id, tt.request)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, tt.wantErr) || strings.Contains(err.Error(), tt.wantErr.Error()),
					"expected error containing %v, got %v", tt.wantErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test for UpdatePassword function
func TestUpdatePassword(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	authID := "auth123"
	email := "<EMAIL>"
	oldPassword := "oldPassword123"
	newPassword := "newPassword456"

	// Create test context
	ctx := context.Background()

	// Set up JWT claims
	jwtClaims := jwt.CustomClaims{
		UID: userID,
	}
	ctx = context.WithValue(ctx, context.ClaimsCtx, jwtClaims)

	tests := []struct {
		name    string
		id      string
		request dtos.UpdatePasswordRequest
		mock    func(ctx context.Context)
		wantErr error
	}{
		{
			name: "Successful_Password_Update",
			id:   userID,
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: oldPassword,
				NewPassword:     newPassword,
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Mock auth domain FindAuthByUserID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).Return(&auth.Auth{
					ID:       authID,
					Username: email,
					UserID:   &userID,
				}, nil).Once()

				// Mock password authentication
				mockAuthDomain.On("PasswordAuthen", ctx, email, oldPassword).Return(&auth.Auth{}, nil).Once()

				// Mock password update
				mockAuthDomain.On("SetPassword", ctx, authID, newPassword).Return(nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "Validation_Error",
			id:   userID,
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: "",  // Validation will fail on empty password
				NewPassword:     "1", // Too short
			},
			mock: func(ctx context.Context) {
				// Set up validator to return error
				mockValidator.On("Validate", mock.Anything).Return(errors.New("validation failed")).Once()
			},
			wantErr: apiutil.ErrInvalidRequest,
		},
		{
			name: "Invalid_UUID",
			id:   "invalid-uuid",
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: oldPassword,
				NewPassword:     newPassword,
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID fails
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").
					Return(errors.New("invalid UUID format")).Once()
			},
			wantErr: apiutil.ErrInvalidRequest,
		},
		{
			name: "Wrong_User",
			id:   "different-user-id",
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: oldPassword,
				NewPassword:     newPassword,
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", "different-user-id", "required,uuid").Return(nil).Once()
				// Permission denied - no need to mock further calls
			},
			wantErr: apiutil.ErrPermissionDenied,
		},
		{
			name: "Auth_Not_Found",
			id:   userID,
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: oldPassword,
				NewPassword:     newPassword,
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Auth not found
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).Return(nil, apiutil.ErrResourceNotFound).Once()
			},
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name: "Invalid_Current_Password",
			id:   userID,
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: "wrongPassword",
				NewPassword:     newPassword,
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Auth found
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).Return(&auth.Auth{
					ID:       authID,
					Username: email,
					UserID:   &userID,
				}, nil).Once()
				// Password authentication fails
				mockAuthDomain.On("PasswordAuthen", ctx, email, "wrongPassword").Return(nil, errors.New("invalid credentials")).Once()
			},
			wantErr: errors.New("invalid current password"),
		},
		{
			name: "Password_Update_Error",
			id:   userID,
			request: dtos.UpdatePasswordRequest{
				CurrentPassword: oldPassword,
				NewPassword:     newPassword,
			},
			mock: func(ctx context.Context) {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				// Auth found
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).Return(&auth.Auth{
					ID:       authID,
					Username: email,
					UserID:   &userID,
				}, nil).Once()
				// Password authentication succeeds
				mockAuthDomain.On("PasswordAuthen", ctx, email, oldPassword).Return(&auth.Auth{}, nil).Once()
				// Password update fails
				mockAuthDomain.On("SetPassword", ctx, authID, newPassword).Return(errors.New("database error")).Once()
			},
			wantErr: errors.New("update password"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock(ctx)
			err := s.UpdatePassword(ctx, tt.id, tt.request)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, tt.wantErr) || strings.Contains(err.Error(), tt.wantErr.Error()),
					"expected error containing %v, got %v", tt.wantErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test for UpdateEmail function
func TestUpdateEmail(t *testing.T) {
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{
		// We can setup ApplinkHost in actual test
	}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmailService := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmailService,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	authID := "auth123"
	currentEmail := "<EMAIL>"
	newEmail := "<EMAIL>"
	userName := "Test User"
	mockToken := "mock-jwt-token"

	tests := []struct {
		name    string
		id      string
		request dtos.UpdateEmailRequest
		mock    func(ctx context.Context) context.Context
		assert  func(t *testing.T, err error)
	}{
		{
			name: "Successful_Update_Email_Request",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Check if email already exists - it doesn't
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID,
						Username: currentEmail,
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				// Get user for email template
				mockUserDomain.On("ReadOne", ctx, userID).
					Return(&user.User{
						ID:    pointer.Ptr(userID),
						Name:  pointer.Ptr(userName),
						Email: currentEmail,
					}, nil).Once()

				// Generate JWT token for email verification
				mockAuthDomain.On("GenVerifyEmailJWT", ctx, authID, mock.Anything).
					Return(mockToken, nil).Once()

				// Send email
				mockEmailService.On("Send", ctx, mock.Anything).Return(nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.NoError(t, err)
			},
		},
		{
			name: "Validation_Error",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: "invalid-email", // Invalid email format
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request - returns error
				mockValidator.On("Validate", mock.Anything).
					Return(errors.New("invalid email format")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "validate: invalid email format")
			},
		},
		{
			name: "Permission_Denied_Different_User",
			id:   "different-user-id",
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", "different-user-id", "required,uuid").Return(nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "wrong login user")
			},
		},
		{
			name: "Email_Already_Exists",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Email already exists for another user
				otherUserID := "other-user-123"
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(&auth.Auth{
						Username: newEmail,
						UserID:   pointer.Ptr(otherUserID),
					}, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, apiutil.ErrEmailAlreadyExists))
			},
		},
		{
			name: "Email_Already_Updated_Same_User",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Email already exists but for same user
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(&auth.Auth{
						Username: newEmail,
						UserID:   pointer.Ptr(userID), // Same userID
					}, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, apiutil.ErrEmailAlreadyUpdated))
			},
		},
		{
			name: "Auth_Not_Found",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Check if email already exists - it doesn't
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				// Auth not found
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "find auth by user ID")
			},
		},
		{
			name: "User_Not_Found",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Check if email already exists - it doesn't
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID,
						Username: currentEmail,
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				// User not found
				mockUserDomain.On("ReadOne", ctx, userID).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "read user by ID")
			},
		},
		{
			name: "Token_Generation_Error",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Check if email already exists - it doesn't
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID,
						Username: currentEmail,
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				// Get user for email template
				mockUserDomain.On("ReadOne", ctx, userID).
					Return(&user.User{
						ID:    pointer.Ptr(userID),
						Name:  pointer.Ptr(userName),
						Email: currentEmail,
					}, nil).Once()

				// Error generating JWT token
				mockAuthDomain.On("GenVerifyEmailJWT", ctx, authID, mock.Anything).
					Return("", errors.New("token generation error")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "generate verify email token")
			},
		},
		{
			name: "Email_Send_Error",
			id:   userID,
			request: dtos.UpdateEmailRequest{
				Email: newEmail,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Check if email already exists - it doesn't
				mockAuthDomain.On("FindAuthByUsername", ctx, newEmail).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID,
						Username: currentEmail,
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				// Get user for email template
				mockUserDomain.On("ReadOne", ctx, userID).
					Return(&user.User{
						ID:    pointer.Ptr(userID),
						Name:  pointer.Ptr(userName),
						Email: currentEmail,
					}, nil).Once()

				// Generate JWT token for email verification
				mockAuthDomain.On("GenVerifyEmailJWT", ctx, authID, mock.Anything).
					Return(mockToken, nil).Once()

				// Error sending email
				mockEmailService.On("Send", ctx, mock.Anything).
					Return(errors.New("email send error")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "send email")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create context with user ID
			ctx := context.Background()
			claims := jwt.CustomClaims{
				UID: userID,
			}
			ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

			// Setup mocks based on test case
			ctx = tt.mock(ctx)

			// Call the service method
			err := s.UpdateEmail(ctx, tt.id, tt.request)

			// Assert expectations
			tt.assert(t, err)
		})
	}
}

// Test for VerifyEmail function
func TestVerifyEmail(t *testing.T) {
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmailService := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmailService,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	authID := "auth123"
	newEmail := "<EMAIL>"
	validToken := "valid-token"

	tests := []struct {
		name    string
		id      string
		request dtos.VerifyEmailRequest
		mock    func(ctx context.Context) context.Context
		assert  func(t *testing.T, err error)
	}{
		{
			name: "Successful_Verify_Email",
			id:   userID,
			request: dtos.VerifyEmailRequest{
				Token: validToken,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Verify token
				mockAuthDomain.On("VerifyEmailToken", ctx, validToken).
					Return(&auth.Auth{
						ID:       authID,
						Username: newEmail,
					}, nil).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID,
						Username: "<EMAIL>",
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				// Update username
				mockAuthDomain.On("UpdateUsername", ctx, authID, newEmail).
					Return(nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.NoError(t, err)
			},
		},
		{
			name: "Validation_Error",
			id:   userID,
			request: dtos.VerifyEmailRequest{
				Token: "", // Empty token, should fail validation
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request - returns error
				mockValidator.On("Validate", mock.Anything).
					Return(errors.New("token is required")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "validate: token is required")
			},
		},
		{
			name: "Permission_Denied_Different_User",
			id:   "different-user-id",
			request: dtos.VerifyEmailRequest{
				Token: validToken,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", "different-user-id", "required,uuid").Return(nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "wrong login user")
			},
		},
		{
			name: "Invalid_Token",
			id:   userID,
			request: dtos.VerifyEmailRequest{
				Token: "invalid-token",
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Token verification fails
				mockAuthDomain.On("VerifyEmailToken", ctx, "invalid-token").
					Return(nil, errors.New("invalid token")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, apiutil.ErrInvalidToken))
			},
		},
		{
			name: "Auth_Not_Found",
			id:   userID,
			request: dtos.VerifyEmailRequest{
				Token: validToken,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Verify token successfully
				mockAuthDomain.On("VerifyEmailToken", ctx, validToken).
					Return(&auth.Auth{
						ID:       authID,
						Username: newEmail,
					}, nil).Once()

				// Auth not found
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(nil, apiutil.ErrResourceNotFound).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "find auth by user ID")
			},
		},
		{
			name: "Token_Not_For_This_User",
			id:   userID,
			request: dtos.VerifyEmailRequest{
				Token: validToken,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Verify token successfully but for different auth ID
				mockAuthDomain.On("VerifyEmailToken", ctx, validToken).
					Return(&auth.Auth{
						ID:       "different-auth-id",
						Username: newEmail,
					}, nil).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID, // Different from token's auth ID
						Username: "<EMAIL>",
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "token not for this user")
			},
		},
		{
			name: "Update_Username_Error",
			id:   userID,
			request: dtos.VerifyEmailRequest{
				Token: validToken,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate request
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Verify token
				mockAuthDomain.On("VerifyEmailToken", ctx, validToken).
					Return(&auth.Auth{
						ID:       authID,
						Username: newEmail,
					}, nil).Once()

				// Find auth by user ID
				mockAuthDomain.On("FindAuthByUserID", ctx, userID).
					Return(&auth.Auth{
						ID:       authID,
						Username: "<EMAIL>",
						UserID:   pointer.Ptr(userID),
					}, nil).Once()

				// Update username fails
				mockAuthDomain.On("UpdateUsername", ctx, authID, newEmail).
					Return(errors.New("database error")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "update auth")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create context with user ID
			ctx := context.Background()
			claims := jwt.CustomClaims{
				UID: userID,
			}
			ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

			// Setup mocks based on test case
			ctx = tt.mock(ctx)

			// Call the service method
			err := s.VerifyEmail(ctx, tt.id, tt.request)

			// Assert expectations
			tt.assert(t, err)
		})
	}
}

func TestUpdateUserNotificationSettings(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	id := "user-id-123"
	request := dtos.NotificationSettingsRequest{
		EmailNotifications: &dtos.EmailNotificationSettings{
			CampaignsAndPromotions: pointer.Ptr(true),
			Messages:               pointer.Ptr(false),
		},
		PushNotifications: &dtos.PushNotificationSettings{
			StopAll:     pointer.Ptr(false),
			Withdrawals: pointer.Ptr(true),
		},
	}

	// Expected domain model
	expectedSettings := &user.NotificationSettings{
		EmailNotifications: &user.EmailNotificationSettings{
			CampaignsAndPromotions: pointer.Ptr(true),
			Messages:               pointer.Ptr(false),
		},
		PushNotifications: &user.PushNotificationSettings{
			StopAll:     pointer.Ptr(false),
			Withdrawals: pointer.Ptr(true),
		},
	}

	tests := []struct {
		name          string
		setupMocks    func(ctx context.Context)
		expectedError error
	}{
		{
			name: "successful update",
			setupMocks: func(ctx context.Context) {
				mockValidator.On("Validate", request).Return(nil).Once()
				mockValidator.On("ValidateVar", id, "required,uuid").Return(nil).Once()
				mockUserDomain.On("UpdateNotificationSettings", ctx, id, mock.MatchedBy(func(ns *user.NotificationSettings) bool {
					// Deep equality check for the notification settings
					return ns.EmailNotifications.CampaignsAndPromotions != nil &&
						*ns.EmailNotifications.CampaignsAndPromotions == *expectedSettings.EmailNotifications.CampaignsAndPromotions &&
						ns.EmailNotifications.Messages != nil &&
						*ns.EmailNotifications.Messages == *expectedSettings.EmailNotifications.Messages &&
						ns.PushNotifications.StopAll != nil &&
						*ns.PushNotifications.StopAll == *expectedSettings.PushNotifications.StopAll &&
						ns.PushNotifications.Withdrawals != nil &&
						*ns.PushNotifications.Withdrawals == *expectedSettings.PushNotifications.Withdrawals
				})).Return(nil).Once()
			},
			expectedError: nil,
		},
		{
			name: "validation error",
			setupMocks: func(ctx context.Context) {
				mockValidator.On("Validate", request).Return(errors.New("validation error")).Once()
			},
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name: "update error",
			setupMocks: func(ctx context.Context) {
				mockValidator.On("Validate", request).Return(nil).Once()
				mockValidator.On("ValidateVar", id, "required,uuid").Return(nil).Once()
				mockUserDomain.On("UpdateNotificationSettings", ctx, id, mock.MatchedBy(func(ns *user.NotificationSettings) bool {
					return true // Just match any notification settings
				})).Return(errors.New("db error")).Once()
			},
			expectedError: errors.New("db error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create context with JWT claims
			claims := jwt.CustomClaims{
				UID: id,
			}
			ctx := context.WithValue(context.Background(), context.ClaimsCtx, claims)

			// Setup mocks based on test case
			tt.setupMocks(ctx)

			// Call method
			err := s.UpdateUserNotificationSettings(ctx, id, request)

			// Check expectations
			if tt.expectedError != nil {
				assert.Error(t, err)
				if errors.Is(err, apiutil.ErrInvalidRequest) ||
					errors.Is(err, apiutil.ErrNotFound) {
					assert.True(t, errors.Is(err, tt.expectedError))
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks
			mockUserDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestGetUserNotificationSettings(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	id := "user-id-123"
	ctx := context.Background()

	// Create test notification settings
	emailNotificationSettings := &user.EmailNotificationSettings{
		CampaignsAndPromotions: pointer.Ptr(true),
		Messages:               pointer.Ptr(false),
	}

	pushNotificationSettings := &user.PushNotificationSettings{
		StopAll:     pointer.Ptr(false),
		Withdrawals: pointer.Ptr(true),
	}

	notificationSettings := &user.NotificationSettings{
		EmailNotifications: emailNotificationSettings,
		PushNotifications:  pushNotificationSettings,
	}

	tests := []struct {
		name   string
		id     string
		mock   func(context.Context) context.Context
		assert func(*testing.T, *dtos.NotificationSettingsResponse, error)
	}{
		{
			name: "Success",
			id:   id,
			mock: func(ctx context.Context) context.Context {
				// Validate ID
				mockValidator.On("ValidateVar", id, "required,uuid").Return(nil).Once()

				// Set user ID in context with JWT claims
				claims := jwt.CustomClaims{
					UID: id,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock GetNotificationSettings call
				mockUserDomain.On("GetNotificationSettings", ctx, id).
					Return(notificationSettings, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, response *dtos.NotificationSettingsResponse, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotNil(t, response.EmailNotifications)
				assert.NotNil(t, response.PushNotifications)

				// Check email notification settings
				assert.Equal(t, *emailNotificationSettings.CampaignsAndPromotions, *response.EmailNotifications.CampaignsAndPromotions)
				assert.Equal(t, *emailNotificationSettings.Messages, *response.EmailNotifications.Messages)

				// Check push notification settings
				assert.Equal(t, *pushNotificationSettings.StopAll, *response.PushNotifications.StopAll)
				assert.Equal(t, *pushNotificationSettings.Withdrawals, *response.PushNotifications.Withdrawals)
			},
		},
		{
			name: "Invalid_ID",
			id:   id,
			mock: func(ctx context.Context) context.Context {
				// Validation fails
				mockValidator.On("ValidateVar", id, "required,uuid").
					Return(errors.New("invalid ID")).Once()

				return ctx
			},
			assert: func(t *testing.T, response *dtos.NotificationSettingsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "validate")
			},
		},
		{
			name: "No_User_ID_In_Context",
			id:   id,
			mock: func(ctx context.Context) context.Context {
				// Validate ID
				mockValidator.On("ValidateVar", id, "required,uuid").Return(nil).Once()

				// No user ID in context
				return ctx
			},
			assert: func(t *testing.T, response *dtos.NotificationSettingsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "get user ID: EC403")
			},
		},
		{
			name: "Wrong_User_ID",
			id:   id,
			mock: func(ctx context.Context) context.Context {
				// Validate ID
				mockValidator.On("ValidateVar", id, "required,uuid").Return(nil).Once()

				// Set different user ID in context with JWT claims
				claims := jwt.CustomClaims{
					UID: "different-user-id",
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				return ctx
			},
			assert: func(t *testing.T, response *dtos.NotificationSettingsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "wrong login user")
			},
		},
		{
			name: "Domain_Error",
			id:   id,
			mock: func(ctx context.Context) context.Context {
				// Validate ID
				mockValidator.On("ValidateVar", id, "required,uuid").Return(nil).Once()

				// Set user ID in context with JWT claims
				claims := jwt.CustomClaims{
					UID: id,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Domain returns error
				mockUserDomain.On("GetNotificationSettings", ctx, id).
					Return(nil, errors.New("domain error")).Once()

				return ctx
			},
			assert: func(t *testing.T, response *dtos.NotificationSettingsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "get notification settings")
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.mock(ctx)
			response, err := s.GetUserNotificationSettings(ctx, tt.id)
			tt.assert(t, response, err)
		})
	}
}

func TestUpdateUserTerms(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockValidator := mockvalidator.NewValidator(t)

	s := &svc{
		v:    mockValidator,
		user: mockUserDomain,
	}

	// Test data
	userID := "user123"
	validDate := "2024-08-01T17:05:21Z"
	invalidDate := "invalid-date"

	// Create test context
	ctx := context.Background()
	claims := jwt.CustomClaims{
		UID: userID,
	}

	tests := []struct {
		name    string
		id      string
		request dtos.UpdateUserTermsRequest
		mock    func(ctx context.Context) context.Context
		wantErr error
	}{
		{
			name: "Successful_Update",
			id:   userID,
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: validDate,
			},

			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID: &userID,
				}, nil).Once()

				// Mock user domain Update
				mockUserDomain.On("Update", ctx, userID, mock.MatchedBy(func(u *user.User) bool {
					return u.ConfirmTermDate != nil && u.ConfirmTermDate.Format(time.RFC3339) == validDate
				})).Return(&user.User{}, nil).Once()

				return ctx
			},
			wantErr: nil,
		},
		{
			name: "Invalid_UUID",
			id:   "invalid-uuid",
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: validDate,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID fails
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").
					Return(errors.New("invalid UUID format")).Once()

				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
				return ctx
			},
			wantErr: apiutil.ErrInvalidRequest,
		},
		{
			name: "User_Not_Found",
			id:   userID,
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: validDate,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
				// User not found
				mockUserDomain.On("ReadOne", ctx, userID).Return(nil, apiutil.ErrResourceNotFound).Once()
				return ctx
			},
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name: "Invalid_Date_Format",
			id:   userID,
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: invalidDate, // Invalid date
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{ID: &userID}, nil).Once()
				return ctx
			},
			wantErr: errors.New("parse date"),
		},
		{
			name: "Update_Error",
			id:   userID,
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: validDate,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID: &userID,
				}, nil).Once()

				// Mock user domain Update returns error
				mockUserDomain.On("Update", ctx, userID, mock.Anything).Return(nil, errors.New("update error")).Once()
				return ctx
			},
			wantErr: errors.New("update error"),
		},
		{
			name: "Ctx_Does not have user_id",
			id:   userID,
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: validDate,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				return ctx
			},
			wantErr: apiutil.ErrPermissionDenied,
		},
		{
			name: "Ctx_Does have user_id not match",
			id:   userID,
			request: dtos.UpdateUserTermsRequest{
				ConfirmTermDate: validDate,
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				claims := jwt.CustomClaims{
					UID: "123",
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
				return ctx
			},
			wantErr: apiutil.ErrPermissionDenied,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.mock(ctx)
			err := s.UpdateUserTerms(ctx, tt.id, tt.request)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, tt.wantErr) || strings.Contains(err.Error(), tt.wantErr.Error()),
					"expected error containing %v, got %v", tt.wantErr, err)
			} else {
				assert.NoError(t, err)
			}
			mockUserDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestUpdateUserPaymentMethod(t *testing.T) {
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockPaymentDomain := paymentmock.NewPaymentDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Payment: mockPaymentDomain,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	userID := "user123"
	authID := "auth123"
	accessID := "access123"
	cardID := "card123"

	tests := []struct {
		name    string
		userID  string
		request *dtos.UpdatePaymentMethodsRequest
		mock    func(ctx context.Context)
		assert  func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error)
	}{
		{
			name:   "Successful_Payment_Method_Update",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
				BillingAddress: &dtos.Address{
					Address1:   pointer.Ptr("123 Main St"),
					City:       pointer.Ptr("Test City"),
					Country:    pointer.Ptr("Test Country"),
					PostalCode: pointer.Ptr("12345"),
					FullName:   pointer.Ptr("John Doe"),
				},
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				userData := &user.User{
					ID:         pointer.Ptr(userID),
					Email:      "<EMAIL>",
					CreditCard: &user.CreditCard{CardID: pointer.Ptr("old-card-123")},
				}
				mockUserDomain.On("ReadOne", ctx, userID).Return(userData, nil).Once()

				storeCardReq := &payment.StoreCardRequest{
					AccessID: accessID,
					MemberCard: payment.MemberCard{
						MemberID: authID,
						CardID:   pointer.Ptr("old-card-123"),
					},
				}
				storeCardRes := &payment.StoreCardResponse{
					CardID: pointer.Ptr(cardID),
					CardResult: &payment.CardResult{
						CardholderName: pointer.Ptr("John Doe"),
						CardNumber:     pointer.Ptr("****1234"),
						ExpiryMonth:    pointer.Ptr("12"),
						ExpiryYear:     pointer.Ptr("2025"),
						Brand:          pointer.Ptr(gmoapi.CardResultBrandVISA),
					},
				}
				mockPaymentDomain.On("StoreCard", ctx, storeCardReq).Return(storeCardRes, nil).Once()

				updateUserReq := &user.User{
					CreditCard: &user.CreditCard{
						CardID:         pointer.Ptr(cardID),
						CardHolderName: pointer.Ptr("John Doe"),
						CardNumber:     pointer.Ptr("****1234"),
						ExpiryMonth:    pointer.Ptr("12"),
						ExpiryYear:     pointer.Ptr("2025"),
						Brand:          pointer.Ptr(string(gmoapi.CardResultBrandVISA)),
					},
					BillingAddress: &user.Address{
						Address1:   pointer.Ptr("123 Main St"),
						City:       pointer.Ptr("Test City"),
						Country:    pointer.Ptr("Test Country"),
						PostalCode: pointer.Ptr("12345"),
						FullName:   pointer.Ptr("John Doe"),
					},
				}
				mockUserDomain.On("Update", ctx, userID, updateUserReq).Return(userData, nil).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotNil(t, response.CardResult)
				assert.Equal(t, "John Doe", pointer.Safe(response.CardResult.CardHolderName))
				assert.Equal(t, "****1234", pointer.Safe(response.CardResult.CardNumber))
				assert.Equal(t, string(gmoapi.CardResultBrandVISA), pointer.Safe(response.CardResult.Brand))
			},
		},
		{
			name:   "Successful_Payment_Method_Update_Without_Existing_Card",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				userData := &user.User{
					ID:         pointer.Ptr(userID),
					Email:      "<EMAIL>",
					CreditCard: nil,
				}
				mockUserDomain.On("ReadOne", ctx, userID).Return(userData, nil).Once()

				storeCardReq := &payment.StoreCardRequest{
					AccessID: accessID,
					MemberCard: payment.MemberCard{
						MemberID: authID,
						CardID:   nil,
					},
				}
				storeCardRes := &payment.StoreCardResponse{
					CardID: pointer.Ptr(cardID),
					CardResult: &payment.CardResult{
						CardholderName: pointer.Ptr("Jane Smith"),
						CardNumber:     pointer.Ptr("****5678"),
						ExpiryMonth:    pointer.Ptr("06"),
						ExpiryYear:     pointer.Ptr("2026"),
						Brand:          pointer.Ptr(gmoapi.CardResultBrandMASTERCARD),
					},
				}
				mockPaymentDomain.On("StoreCard", ctx, storeCardReq).Return(storeCardRes, nil).Once()

				updateUserReq := &user.User{
					CreditCard: &user.CreditCard{
						CardID:         pointer.Ptr(cardID),
						CardHolderName: pointer.Ptr("Jane Smith"),
						CardNumber:     pointer.Ptr("****5678"),
						ExpiryMonth:    pointer.Ptr("06"),
						ExpiryYear:     pointer.Ptr("2026"),
						Brand:          pointer.Ptr(string(gmoapi.CardResultBrandMASTERCARD)),
					},
					BillingAddress: nil,
				}
				mockUserDomain.On("Update", ctx, userID, updateUserReq).Return(userData, nil).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotNil(t, response.CardResult)
				assert.Equal(t, "Jane Smith", pointer.Safe(response.CardResult.CardHolderName))
				assert.Equal(t, string(gmoapi.CardResultBrandMASTERCARD), pointer.Safe(response.CardResult.Brand))
			},
		},
		{
			name:   "Invalid_User_ID_Validation_Error",
			userID: "invalid-uuid",
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").Return(errors.New("invalid UUID")).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "validate: invalid UUID")
			},
		},
		{
			name:   "Request_Validation_Error",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: "",
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(errors.New("accessId is required")).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "validate: accessId is required")
			},
		},
		{
			name:   "Permission_Denied_Wrong_User",
			userID: "different-user-123",
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", "different-user-123", "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "wrong login user")
			},
		},
		{
			name:   "User_Not_Found",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				mockUserDomain.On("ReadOne", ctx, userID).Return(nil, apiutil.ErrResourceNotFound).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "get user ID")
			},
		},
		{
			name:   "User_Data_Nil",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				mockUserDomain.On("ReadOne", ctx, userID).Return(nil, nil).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "user not found")
			},
		},
		{
			name:   "Payment_Store_Card_Error",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				userData := &user.User{
					ID:         pointer.Ptr(userID),
					Email:      "<EMAIL>",
					CreditCard: nil,
				}
				mockUserDomain.On("ReadOne", ctx, userID).Return(userData, nil).Once()

				storeCardReq := &payment.StoreCardRequest{
					AccessID: accessID,
					MemberCard: payment.MemberCard{
						MemberID: authID,
						CardID:   nil,
					},
				}
				mockPaymentDomain.On("StoreCard", ctx, storeCardReq).Return(nil, errors.New("payment service error")).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "store card")
			},
		},
		{
			name:   "User_Update_Error",
			userID: userID,
			request: &dtos.UpdatePaymentMethodsRequest{
				AccessId: accessID,
			},
			mock: func(ctx context.Context) {
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()

				userData := &user.User{
					ID:         pointer.Ptr(userID),
					Email:      "<EMAIL>",
					CreditCard: nil,
				}
				mockUserDomain.On("ReadOne", ctx, userID).Return(userData, nil).Once()

				storeCardReq := &payment.StoreCardRequest{
					AccessID: accessID,
					MemberCard: payment.MemberCard{
						MemberID: authID,
						CardID:   nil,
					},
				}
				storeCardRes := &payment.StoreCardResponse{
					CardID: pointer.Ptr(cardID),
					CardResult: &payment.CardResult{
						CardholderName: pointer.Ptr("Test User"),
						CardNumber:     pointer.Ptr("****1111"),
						ExpiryMonth:    pointer.Ptr("01"),
						ExpiryYear:     pointer.Ptr("2027"),
						Brand:          pointer.Ptr(gmoapi.CardResultBrandAMEX),
					},
				}
				mockPaymentDomain.On("StoreCard", ctx, storeCardReq).Return(storeCardRes, nil).Once()

				updateUserReq := &user.User{
					CreditCard: &user.CreditCard{
						CardID:         pointer.Ptr(cardID),
						CardHolderName: pointer.Ptr("Test User"),
						CardNumber:     pointer.Ptr("****1111"),
						ExpiryMonth:    pointer.Ptr("01"),
						ExpiryYear:     pointer.Ptr("2027"),
						Brand:          pointer.Ptr(string(gmoapi.CardResultBrandAMEX)),
					},
					BillingAddress: nil,
				}
				mockUserDomain.On("Update", ctx, userID, updateUserReq).Return(nil, errors.New("database error")).Once()
			},
			assert: func(t *testing.T, response *dtos.UpdatePaymentMethodsResponse, err error) {
				assert.Error(t, err)
				assert.Nil(t, response)
				assert.Contains(t, err.Error(), "update user credit card")
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{
				AID: authID,
				UID: userID,
			})

			tc.mock(ctx)

			response, err := s.UpdateUserPaymentMethod(ctx, tc.userID, tc.request)

			tc.assert(t, response, err)

			mockValidator.AssertExpectations(t)
			mockUserDomain.AssertExpectations(t)
			mockPaymentDomain.AssertExpectations(t)
		})
	}
}

func TestCancelAccount(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockOrderDomain := ordermock.NewOrderDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Order:   mockOrderDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	cancelReq := &dtos.CancelAccountRequest{
		PricesTooHigh:       pointer.Ptr(true),
		OtherReason:         pointer.Ptr("User requested account deletion"),
		DeliveryTooLong:     pointer.Ptr(false),
		ProductsUnavailable: pointer.Ptr(false),
		ItemProblem:         pointer.Ptr(false),
		SellerUntrustworthy: pointer.Ptr(false),
		AppDifficultToUse:   pointer.Ptr(false),
		TooManyEmails:       pointer.Ptr(false),
		SituationChanged:    pointer.Ptr(false),
		DontKnowHowToList:   pointer.Ptr(false),
		ItemsDidNotSell:     pointer.Ptr(false),
	}

	tests := []struct {
		name    string
		id      string
		request *dtos.CancelAccountRequest
		mock    func(ctx context.Context) context.Context
		assert  func(t *testing.T, err error)
	}{
		{
			name:    "Successful_Cancel_Account",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context with JWT claims
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (NO incomplete purchases, can cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock user domain CancelAccount
				mockUserDomain.On("CancelAccount", ctx, userID, mock.MatchedBy(func(u *user.User) bool {
					return u.CancelReason != nil &&
						u.CancelReason.PricesTooHigh != nil &&
						*u.CancelReason.PricesTooHigh == true &&
						u.CancelReason.OtherReason != nil &&
						*u.CancelReason.OtherReason == "User requested account deletion"
				})).Return(nil).Once()

				// Mock auth domain DeleteAuthByUserID
				mockAuthDomain.On("DeleteAuthByUserID", ctx, userID).Return(nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.NoError(t, err)
			},
		},
		{
			name: "Successful_Cancel_Account_Different_Reasons",
			id:   userID,
			request: &dtos.CancelAccountRequest{
				DeliveryTooLong: pointer.Ptr(true),
				ItemProblem:     pointer.Ptr(true),
				OtherReason:     pointer.Ptr("Items take too long to deliver and have quality issues"),
			},
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context with JWT claims
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (NO incomplete purchases, can cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock user domain CancelAccount
				mockUserDomain.On("CancelAccount", ctx, userID, mock.MatchedBy(func(u *user.User) bool {
					return u.CancelReason != nil &&
						u.CancelReason.DeliveryTooLong != nil &&
						*u.CancelReason.DeliveryTooLong == true &&
						u.CancelReason.ItemProblem != nil &&
						*u.CancelReason.ItemProblem == true
				})).Return(nil).Once()

				// Mock auth domain DeleteAuthByUserID
				mockAuthDomain.On("DeleteAuthByUserID", ctx, userID).Return(nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.NoError(t, err)
			},
		},
		{
			name:    "Invalid_UUID",
			id:      "invalid-uuid",
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID fails
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").
					Return(errors.New("invalid UUID format")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, apiutil.ErrInvalidRequest))
				assert.Contains(t, err.Error(), "validate: invalid UUID format")
			},
		},
		{
			name:    "Permission_Denied_No_User_In_Context",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// No user ID in context (no JWT claims)
				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, apiutil.ErrPermissionDenied))
				assert.Contains(t, err.Error(), "get user ID")
			},
		},
		{
			name:    "Permission_Denied_Wrong_User",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set different user ID in context
				claims := jwt.CustomClaims{
					UID: "different-user-123",
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, apiutil.ErrPermissionDenied))
				assert.Contains(t, err.Error(), "wrong login user")
			},
		},
		{
			name:    "User_Not_Found",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// User not found
				mockUserDomain.On("ReadOne", ctx, userID).Return(nil, apiutil.ErrResourceNotFound).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "get user ID")
			},
		},
		{
			name:    "Domain_Cancel_Account_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (has incomplete purchases)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock user domain CancelAccount returns error
				mockUserDomain.On("CancelAccount", ctx, userID, mock.Anything).Return(errors.New("database error")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "update user confirm terms date")
			},
		},
		{
			name:    "Domain_ReadOne_Database_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne returns database error
				mockUserDomain.On("ReadOne", ctx, userID).Return(nil, errors.New("database connection failed")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "get user ID")
				assert.Contains(t, err.Error(), "database connection failed")
			},
		},
		{
			name:    "Auth_Delete_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (has incomplete purchases)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock user domain CancelAccount succeeds
				mockUserDomain.On("CancelAccount", ctx, userID, mock.Anything).Return(nil).Once()

				// Mock auth domain DeleteAuthByUserID returns error
				mockAuthDomain.On("DeleteAuthByUserID", ctx, userID).Return(errors.New("auth deletion failed")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "delete auth by user ID")
				assert.Contains(t, err.Error(), "auth deletion failed")
			},
		},
		{
			name:    "Auth_Delete_Not_Found_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (has incomplete purchases)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock user domain CancelAccount succeeds
				mockUserDomain.On("CancelAccount", ctx, userID, mock.Anything).Return(nil).Once()

				// Mock auth domain DeleteAuthByUserID returns not found error
				mockAuthDomain.On("DeleteAuthByUserID", ctx, userID).Return(apiutil.ErrResourceNotFound).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "delete auth by user ID")
			},
		},
		{
			name:    "Incomplete_Purchase_Orders_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns false (HAS incomplete purchases, cannot cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(false, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, ErrIncompletePurchaseOrders))
				assert.Contains(t, err.Error(), "E96002")
			},
		},
		{
			name:    "Incomplete_Sales_Orders_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne - user with seller ID
				sellerID := "seller123"
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					Email:    "<EMAIL>",
					Name:     pointer.Ptr("Test User"),
					SellerID: &sellerID,
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (NO incomplete purchases, can cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock order domain CheckOrderDetailStatusSeller - returns false (HAS incomplete sales, cannot cancel)
				mockOrderDomain.On("CheckOrderDetailStatusSeller", ctx, sellerID).Return(false, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, ErrIncompleteSalesOrders))
				assert.Contains(t, err.Error(), "E96003")
			},
		},
		{
			name:    "Order_Check_Buyer_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:    pointer.Ptr(userID),
					Email: "<EMAIL>",
					Name:  pointer.Ptr("Test User"),
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer returns error
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(false, errors.New("database error")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "check incomplete purchase orders")
				assert.Contains(t, err.Error(), "database error")
			},
		},
		{
			name:    "Incomplete_Purchase_And_Sales_Orders_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne - user with seller ID
				sellerID := "seller123"
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					Email:    "<EMAIL>",
					Name:     pointer.Ptr("Test User"),
					SellerID: &sellerID,
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns false (HAS incomplete purchases, cannot cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(false, nil).Once()

				// Mock order domain CheckOrderDetailStatusSeller - returns false (HAS incomplete sales, cannot cancel)
				mockOrderDomain.On("CheckOrderDetailStatusSeller", ctx, sellerID).Return(false, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, ErrIncompletePurchaseOrdersAndSalesOrders))
				assert.Contains(t, err.Error(), "E96003a")
			},
		},
		{
			name:    "Order_Check_Seller_Error",
			id:      userID,
			request: cancelReq,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne - user with seller ID
				sellerID := "seller123"
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					Email:    "<EMAIL>",
					Name:     pointer.Ptr("Test User"),
					SellerID: &sellerID,
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (NO incomplete purchases, can cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock order domain CheckOrderDetailStatusSeller returns error
				mockOrderDomain.On("CheckOrderDetailStatusSeller", ctx, sellerID).Return(false, errors.New("database error")).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "check incomplete sales orders")
				assert.Contains(t, err.Error(), "database error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create base context
			ctx := context.Background()

			// Setup mocks based on test case
			ctx = tt.mock(ctx)

			// Call the service method
			err := s.CancelAccount(ctx, tt.id, tt.request)

			// Assert expectations
			tt.assert(t, err)

			// Verify all mocks
			mockValidator.AssertExpectations(t)
			mockUserDomain.AssertExpectations(t)
			mockAuthDomain.AssertExpectations(t)
			mockOrderDomain.AssertExpectations(t)
		})
	}
}

func TestCheckCancelAccount(t *testing.T) {
	t.Parallel()

	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockOrderDomain := ordermock.NewOrderDomain(t)
	mockEnv := env.MapperData{}
	mockValidator := mockvalidator.NewValidator(t)
	mockEmail := email.NewSMTPService(t)

	di := ServiceDI{
		User:    mockUserDomain,
		Auth:    mockAuthDomain,
		Order:   mockOrderDomain,
		Payment: nil,
		Email:   mockEmail,
		Env:     mockEnv,
		Log:     nil,
		V:       mockValidator,
	}
	s := NewService(di)

	// Test data
	userID := "user123"
	sellerID := "seller123"

	tests := []struct {
		name   string
		id     string
		mock   func(ctx context.Context) context.Context
		assert func(t *testing.T, err error)
	}{
		{
			name: "Can_Cancel_Account_Non_Seller",
			id:   userID,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne - non-seller user
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					Email:    "<EMAIL>",
					Name:     pointer.Ptr("Test User"),
					SellerID: nil, // Not a seller
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (NO incomplete purchases, can cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.NoError(t, err)
			},
		},
		{
			name: "Cannot_Cancel_Account_Incomplete_Purchases",
			id:   userID,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					Email:    "<EMAIL>",
					Name:     pointer.Ptr("Test User"),
					SellerID: nil,
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns false (HAS incomplete purchases, cannot cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(false, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, ErrIncompletePurchaseOrders))
				assert.Contains(t, err.Error(), "E96002")
			},
		},
		{
			name: "Cannot_Cancel_Account_Seller_With_Incomplete_Sales",
			id:   userID,
			mock: func(ctx context.Context) context.Context {
				// Validate UUID
				mockValidator.On("ValidateVar", userID, "required,uuid").Return(nil).Once()

				// Set user ID in context
				claims := jwt.CustomClaims{
					UID: userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)

				// Mock user domain ReadOne - seller user
				mockUserDomain.On("ReadOne", ctx, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					Email:    "<EMAIL>",
					Name:     pointer.Ptr("Test User"),
					SellerID: &sellerID, // Is a seller
				}, nil).Once()

				// Mock order domain CheckOrderDetailStatusBuyer - returns true (NO incomplete purchases, can cancel)
				mockOrderDomain.On("CheckOrderDetailStatusBuyer", ctx, userID).Return(true, nil).Once()

				// Mock order domain CheckOrderDetailStatusSeller - returns false (HAS incomplete sales, cannot cancel)
				mockOrderDomain.On("CheckOrderDetailStatusSeller", ctx, sellerID).Return(false, nil).Once()

				return ctx
			},
			assert: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, ErrIncompleteSalesOrders))
				assert.Contains(t, err.Error(), "E96003")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			ctx = tt.mock(ctx)

			err := s.CheckCancelAccount(ctx, tt.id)

			tt.assert(t, err)

			// Assert all expectations
			mockUserDomain.AssertExpectations(t)
			mockAuthDomain.AssertExpectations(t)
			mockOrderDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
			mockEmail.AssertExpectations(t)
		})
	}
}

// Test for isUSAddress function (case-sensitive, only accepts "USA")
func TestIsUSAddress(t *testing.T) {
	tests := []struct {
		name     string
		country  *string
		expected bool
	}{
		{
			name:     "Nil country",
			country:  nil,
			expected: false,
		},
		{
			name:     "Empty country",
			country:  pointer.Ptr(""),
			expected: false,
		},
		{
			name:     "Whitespace only",
			country:  pointer.Ptr("   "),
			expected: false,
		},
		{
			name:     "USA uppercase",
			country:  pointer.Ptr("USA"),
			expected: true,
		},
		{
			name:     "usa lowercase",
			country:  pointer.Ptr("usa"),
			expected: true,
		},
		{
			name:     "US uppercase",
			country:  pointer.Ptr("US"),
			expected: false,
		},
		{
			name:     "us lowercase",
			country:  pointer.Ptr("us"),
			expected: false,
		},
		{
			name:     "United States",
			country:  pointer.Ptr("United States"),
			expected: false,
		},
		{
			name:     "united states lowercase",
			country:  pointer.Ptr("united states"),
			expected: false,
		},
		{
			name:     "Mixed case",
			country:  pointer.Ptr("UsA"),
			expected: true,
		},
		{
			name:     "With whitespace",
			country:  pointer.Ptr("  USA  "),
			expected: true,
		},
		{
			name:     "Canada",
			country:  pointer.Ptr("Canada"),
			expected: false,
		},
		{
			name:     "United Kingdom",
			country:  pointer.Ptr("United Kingdom"),
			expected: false,
		},
		{
			name:     "Japan",
			country:  pointer.Ptr("Japan"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isUSAddress(tt.country)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test for normalizeUSPostalCode function
func TestNormalizeUSPostalCode(t *testing.T) {
	tests := []struct {
		name       string
		postalCode string
		expected   string
	}{
		{
			name:       "Valid 5-digit ZIP",
			postalCode: "12345",
			expected:   "12345",
		},
		{
			name:       "Valid 9-digit ZIP",
			postalCode: "12345-6789",
			expected:   "12345",
		},
		{
			name:       "With whitespace",
			postalCode: "  12345  ",
			expected:   "12345",
		},
		{
			name:       "9-digit with whitespace",
			postalCode: "  12345-6789  ",
			expected:   "12345",
		},
		{
			name:       "With spaces in middle",
			postalCode: "123 45",
			expected:   "12345",
		},
		{
			name:       "Empty string",
			postalCode: "",
			expected:   "",
		},
		{
			name:       "Only whitespace",
			postalCode: "   ",
			expected:   "",
		},
		{
			name:       "Too short",
			postalCode: "1234",
			expected:   "",
		},
		{
			name:       "Too long",
			postalCode: "123456",
			expected:   "",
		},
		{
			name:       "Invalid characters",
			postalCode: "1234a",
			expected:   "",
		},
		{
			name:       "Invalid 9-digit format",
			postalCode: "12345-67890",
			expected:   "",
		},
		{
			name:       "Missing dash in 9-digit",
			postalCode: "123456789",
			expected:   "",
		},
		{
			name:       "Letters in ZIP",
			postalCode: "abcde",
			expected:   "",
		},
		{
			name:       "Mixed letters and numbers",
			postalCode: "12a45",
			expected:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := normalizeUSPostalCode(tt.postalCode)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test for validateUSPostalCode function
func TestValidateUSPostalCode(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		address       *user.Address
		mockSetup     func(mockOrderDomain *ordermock.OrderDomain)
		expectedError bool
		errorContains string
	}{
		{
			name:          "Nil address",
			address:       nil,
			mockSetup:     func(mockOrderDomain *ordermock.OrderDomain) {},
			expectedError: false,
		},
		{
			name: "Non-US address",
			address: &user.Address{
				Country:    pointer.Ptr("Canada"),
				PostalCode: pointer.Ptr("K1A 0A6"),
			},
			mockSetup:     func(mockOrderDomain *ordermock.OrderDomain) {},
			expectedError: false,
		},
		{
			name: "US address with nil postal code",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: nil,
			},
			mockSetup:     func(mockOrderDomain *ordermock.OrderDomain) {},
			expectedError: true,
			errorContains: "postal code is required",
		},
		{
			name: "US address with empty postal code",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr(""),
			},
			mockSetup:     func(mockOrderDomain *ordermock.OrderDomain) {},
			expectedError: true,
			errorContains: "postal code is required",
		},
		{
			name: "US address with whitespace postal code",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr("   "),
			},
			mockSetup:     func(mockOrderDomain *ordermock.OrderDomain) {},
			expectedError: true,
			errorContains: "postal code is required",
		},
		{
			name: "US address with invalid postal code format",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr("invalid"),
			},
			mockSetup:     func(mockOrderDomain *ordermock.OrderDomain) {},
			expectedError: true,
			errorContains: "invalid postal code format",
		},
		{
			name: "US address with valid postal code - found in database",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr("12345"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain) {
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "12345").Return(true, nil)
			},
			expectedError: false,
		},
		{
			name: "US address with valid postal code - not found in database",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr("99999"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain) {
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "99999").Return(false, nil)
			},
			expectedError: true,
			errorContains: "postal code not found",
		},
		{
			name: "US address with 9-digit postal code - found in database",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr("12345-6789"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain) {
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "12345").Return(true, nil)
			},
			expectedError: false,
		},
		{
			name: "US address case insensitive - lowercase usa",
			address: &user.Address{
				Country:    pointer.Ptr("usa"),
				PostalCode: pointer.Ptr("12345"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain) {
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "12345").Return(true, nil)
			},
			expectedError: false,
		},
		{
			name: "Non-US address with US country code",
			address: &user.Address{
				Country:    pointer.Ptr("US"),
				PostalCode: pointer.Ptr("12345"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain) {
				// No ZIP code validation should occur for non-USA addresses
			},
			expectedError: false,
		},
		{
			name: "Database error",
			address: &user.Address{
				Country:    pointer.Ptr("USA"),
				PostalCode: pointer.Ptr("12345"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain) {
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "12345").Return(false, errors.New("database error"))
			},
			expectedError: true,
			errorContains: "failed to validate postal code",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockOrderDomain := ordermock.NewOrderDomain(t)
			s := &svc{
				order: mockOrderDomain,
			}

			tt.mockSetup(mockOrderDomain)

			err := s.validateUSPostalCode(ctx, tt.address)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			mockOrderDomain.AssertExpectations(t)
		})
	}
}

// Test for areAddressesIdentical function
func TestAreAddressesIdentical(t *testing.T) {
	tests := []struct {
		name         string
		shippingAddr *user.Address
		expected     bool
	}{
		{
			name:         "Shipping address is nil",
			shippingAddr: nil,
			expected:     true,
		},
		{
			name: "IsDifferentFromResidence is false - addresses are identical",
			shippingAddr: &user.Address{
				IsDifferentFromResidence: false,
				Address1:                 pointer.Ptr("123 Main St"),
				City:                     pointer.Ptr("New York"),
				PostalCode:               pointer.Ptr("10001"),
				Country:                  pointer.Ptr("USA"),
			},
			expected: true,
		},
		{
			name: "IsDifferentFromResidence is true - addresses are different",
			shippingAddr: &user.Address{
				IsDifferentFromResidence: true,
				Address1:                 pointer.Ptr("456 Ship Ave"),
				City:                     pointer.Ptr("Boston"),
				PostalCode:               pointer.Ptr("02101"),
				Country:                  pointer.Ptr("USA"),
			},
			expected: false,
		},
		{
			name: "IsDifferentFromResidence not set (default false) - addresses are identical",
			shippingAddr: &user.Address{
				// IsDifferentFromResidence not set, defaults to false
				Address1:   pointer.Ptr("123 Main St"),
				City:       pointer.Ptr("New York"),
				PostalCode: pointer.Ptr("10001"),
				Country:    pointer.Ptr("USA"),
			},
			expected: true,
		},
		{
			name: "IsDifferentFromResidence is true even with same field values - addresses are different",
			shippingAddr: &user.Address{
				IsDifferentFromResidence: true,
				Address1:                 pointer.Ptr("123 Main St"),
				City:                     pointer.Ptr("New York"),
				PostalCode:               pointer.Ptr("10001"),
				Country:                  pointer.Ptr("USA"),
			},
			expected: false,
		},
		{
			name: "IsDifferentFromResidence is false even with different field values - addresses are identical",
			shippingAddr: &user.Address{
				IsDifferentFromResidence: false,
				Address1:                 pointer.Ptr("456 Ship Ave"),
				City:                     pointer.Ptr("Boston"),
				PostalCode:               pointer.Ptr("02101"),
				Country:                  pointer.Ptr("USA"),
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := areAddressesIdentical(tt.shippingAddr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper function to setup common mocks for RegisterUser tests
func setupCommonRegisterUserMocks(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain, zipCode string, zipValid bool) {
	// Setup ZIP code validation
	mockOrderDomain.On("ValidateUSZipCode", mock.Anything, zipCode).Return(zipValid, nil)

	// Setup other required mocks for successful registration
	mockAuthDomain.On("FindOneByID", mock.Anything, "auth123").Return(&auth.Auth{ID: "auth123", Username: "<EMAIL>"}, nil)
	mockUserDomain.On("FindByEmail", mock.Anything, "<EMAIL>").Return(nil, apiutil.ErrResourceNotFound)
	mockUserDomain.On("Create", mock.Anything, mock.Anything).Return(&user.User{ID: pointer.Ptr("user-id")}, nil)
	mockAuthDomain.On("AttachUserID", mock.Anything, "auth123", mock.Anything, "password123").Return(nil)
	mockAuthDomain.On("GenJWT", mock.Anything, mock.Anything).Return(&auth.AuthToken{AccessToken: "token", RefreshToken: "refresh"}, nil)
}

// Test for selective validation logic in RegisterUser
func TestRegisterUserSelectiveValidation(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name            string
		homeAddress     *user.Address
		shippingAddress *user.Address
		billingAddress  *user.Address
		mockSetup       func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain)
		expectedError   bool
		errorContains   string
	}{
		{
			name: "Validate shipping address when different from home",
			homeAddress: &user.Address{
				Address1:   pointer.Ptr("123 Home St"),
				City:       pointer.Ptr("New York"),
				State:      pointer.Ptr("NY"),
				PostalCode: pointer.Ptr("10001"),
				Country:    pointer.Ptr("USA"),
			},
			shippingAddress: &user.Address{
				IsDifferentFromResidence: true, // This makes it different from home
				Address1:                 pointer.Ptr("456 Ship Ave"),
				City:                     pointer.Ptr("Boston"),
				State:                    pointer.Ptr("MA"),
				PostalCode:               pointer.Ptr("02101"),
				Country:                  pointer.Ptr("USA"),
			},
			billingAddress: &user.Address{
				Address1:   pointer.Ptr("789 Bill Blvd"),
				City:       pointer.Ptr("Chicago"),
				State:      pointer.Ptr("IL"),
				PostalCode: pointer.Ptr("60601"),
				Country:    pointer.Ptr("USA"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain) {
				// Should validate shipping address postal code (02101), not home or billing
				setupCommonRegisterUserMocks(mockOrderDomain, mockUserDomain, mockAuthDomain, "02101", true)
			},
			expectedError: false,
		},
		{
			name: "Validate home address when shipping is identical",
			homeAddress: &user.Address{
				Address1:   pointer.Ptr("123 Main St"),
				City:       pointer.Ptr("New York"),
				State:      pointer.Ptr("NY"),
				PostalCode: pointer.Ptr("10001"),
				Country:    pointer.Ptr("USA"),
			},
			shippingAddress: &user.Address{
				IsDifferentFromResidence: false,                           // This makes it identical to home
				Address1:                 pointer.Ptr("456 Different St"), // Even with different fields
				City:                     pointer.Ptr("Boston"),
				State:                    pointer.Ptr("MA"),
				PostalCode:               pointer.Ptr("02101"),
				Country:                  pointer.Ptr("USA"),
			},
			billingAddress: &user.Address{
				Address1:   pointer.Ptr("789 Bill Blvd"),
				City:       pointer.Ptr("Chicago"),
				State:      pointer.Ptr("IL"),
				PostalCode: pointer.Ptr("60601"),
				Country:    pointer.Ptr("USA"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain) {
				// Should validate home address postal code (10001), not shipping or billing
				setupCommonRegisterUserMocks(mockOrderDomain, mockUserDomain, mockAuthDomain, "10001", true)
			},
			expectedError: false,
		},
		{
			name: "Validate home address when no shipping address",
			homeAddress: &user.Address{
				Address1:   pointer.Ptr("123 Main St"),
				City:       pointer.Ptr("New York"),
				State:      pointer.Ptr("NY"),
				PostalCode: pointer.Ptr("10001"),
				Country:    pointer.Ptr("USA"),
			},
			shippingAddress: nil,
			billingAddress: &user.Address{
				Address1:   pointer.Ptr("789 Bill Blvd"),
				City:       pointer.Ptr("Chicago"),
				State:      pointer.Ptr("IL"),
				PostalCode: pointer.Ptr("60601"),
				Country:    pointer.Ptr("USA"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain) {
				// Should validate home address postal code (10001), billing should be ignored
				setupCommonRegisterUserMocks(mockOrderDomain, mockUserDomain, mockAuthDomain, "10001", true)
			},
			expectedError: false,
		},
		{
			name: "Shipping address validation fails",
			homeAddress: &user.Address{
				Address1:   pointer.Ptr("123 Home St"),
				City:       pointer.Ptr("New York"),
				State:      pointer.Ptr("NY"),
				PostalCode: pointer.Ptr("10001"),
				Country:    pointer.Ptr("USA"),
			},
			shippingAddress: &user.Address{
				IsDifferentFromResidence: true, // This makes it different from home
				Address1:                 pointer.Ptr("456 Ship Ave"),
				City:                     pointer.Ptr("Boston"),
				State:                    pointer.Ptr("MA"),
				PostalCode:               pointer.Ptr("99999"),
				Country:                  pointer.Ptr("USA"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain) {
				// Should validate shipping address postal code (99999) and fail
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "99999").Return(false, nil)

				// Setup other required mocks (but won't be called due to validation failure)
				mockAuthDomain.On("FindOneByID", mock.Anything, "auth123").Return(&auth.Auth{ID: "auth123", Username: "<EMAIL>"}, nil)
				mockUserDomain.On("FindByEmail", mock.Anything, "<EMAIL>").Return(nil, apiutil.ErrResourceNotFound)
			},
			expectedError: true,
			errorContains: "validate shipping address postal code",
		},
		{
			name: "Home address validation fails when shipping is identical",
			homeAddress: &user.Address{
				Address1:   pointer.Ptr("123 Main St"),
				City:       pointer.Ptr("New York"),
				State:      pointer.Ptr("NY"),
				PostalCode: pointer.Ptr("99999"),
				Country:    pointer.Ptr("USA"),
			},
			shippingAddress: &user.Address{
				IsDifferentFromResidence: false,                           // This makes it identical to home
				Address1:                 pointer.Ptr("456 Different St"), // Even with different fields
				City:                     pointer.Ptr("Boston"),
				State:                    pointer.Ptr("MA"),
				PostalCode:               pointer.Ptr("02101"),
				Country:                  pointer.Ptr("USA"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain) {
				// Should validate home address postal code (99999) and fail
				mockOrderDomain.On("ValidateUSZipCode", mock.Anything, "99999").Return(false, nil)

				// Setup other required mocks (but won't be called due to validation failure)
				mockAuthDomain.On("FindOneByID", mock.Anything, "auth123").Return(&auth.Auth{ID: "auth123", Username: "<EMAIL>"}, nil)
				mockUserDomain.On("FindByEmail", mock.Anything, "<EMAIL>").Return(nil, apiutil.ErrResourceNotFound)
			},
			expectedError: true,
			errorContains: "validate home address postal code",
		},
		{
			name: "No validation for non-US addresses",
			homeAddress: &user.Address{
				Address1:   pointer.Ptr("123 Main St"),
				City:       pointer.Ptr("Toronto"),
				State:      pointer.Ptr("ON"),
				PostalCode: pointer.Ptr("M5V 3A8"),
				Country:    pointer.Ptr("Canada"),
			},
			shippingAddress: &user.Address{
				Address1:   pointer.Ptr("456 Ship Ave"),
				City:       pointer.Ptr("Vancouver"),
				State:      pointer.Ptr("BC"),
				PostalCode: pointer.Ptr("V6B 1A1"),
				Country:    pointer.Ptr("Canada"),
			},
			mockSetup: func(mockOrderDomain *ordermock.OrderDomain, mockUserDomain *usermock.UserDomain, mockAuthDomain *authmock.AuthDomain) {
				// No ZIP code validation should occur for Canadian addresses

				// Setup other required mocks
				mockAuthDomain.On("FindOneByID", mock.Anything, "auth123").Return(&auth.Auth{ID: "auth123", Username: "<EMAIL>"}, nil)
				mockUserDomain.On("FindByEmail", mock.Anything, "<EMAIL>").Return(nil, apiutil.ErrResourceNotFound)
				mockUserDomain.On("Create", mock.Anything, mock.Anything).Return(&user.User{ID: pointer.Ptr("user-id")}, nil)
				mockAuthDomain.On("AttachUserID", mock.Anything, "auth123", mock.Anything, "password123").Return(nil)
				mockAuthDomain.On("GenJWT", mock.Anything, mock.Anything).Return(&auth.AuthToken{AccessToken: "token", RefreshToken: "refresh"}, nil)
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockOrderDomain := ordermock.NewOrderDomain(t)
			mockUserDomain := usermock.NewUserDomain(t)
			mockAuthDomain := authmock.NewAuthDomain(t)
			mockValidator := mockvalidator.NewValidator(t)
			mockEmail := email.NewSMTPService(t)
			mockEnv := env.MapperData{}

			di := ServiceDI{
				User:    mockUserDomain,
				Auth:    mockAuthDomain,
				Order:   mockOrderDomain,
				Payment: nil,
				Email:   mockEmail,
				Env:     mockEnv,
				Log:     nil,
				V:       mockValidator,
			}
			s := NewService(di)

			// Setup validator mock to always pass
			mockValidator.On("Validate", mock.Anything).Return(nil)

			// Create context with JWT claims
			claims := jwt.CustomClaims{
				AID:     "auth123",
				UID:     "user123",
				IsAdmin: false,
				Type:    "accessToken",
			}
			ctxWithClaims := context.WithValue(ctx, context.ClaimsCtx, claims)

			tt.mockSetup(mockOrderDomain, mockUserDomain, mockAuthDomain)

			req := dtos.RegisterUserRequest{
				Email:           pointer.Ptr("<EMAIL>"),
				Password:        "password123",
				HomeAddress:     convertAddressToDTO(tt.homeAddress),
				ShippingAddress: convertAddressToDTO(tt.shippingAddress),
				BillingAddress:  convertAddressToDTO(tt.billingAddress),
			}

			_, err := s.RegisterUser(ctxWithClaims, req)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			mockOrderDomain.AssertExpectations(t)
			mockUserDomain.AssertExpectations(t)
			mockAuthDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

// Helper function to convert user.Address to DTO Address for testing
func convertAddressToDTO(addr *user.Address) *dtos.Address {
	if addr == nil {
		return nil
	}
	return &dtos.Address{
		IsDifferentFromResidence: &addr.IsDifferentFromResidence,
		Address1:                 addr.Address1,
		Address2:                 addr.Address2,
		City:                     addr.City,
		State:                    addr.State,
		PostalCode:               addr.PostalCode,
		Country:                  addr.Country,
	}
}
