package service

import (
	"as-api/as/pkg/helpers/apiutil"

	"github.com/pkg/errors"
)

var (
	ErrEmailAlreadyExists                     = apiutil.NewError("EU001", errors.Wrap(apiutil.ErrUniqueViolation, "email already signed up"))
	ErrEmailAlreadyBlocked                    = apiutil.NewError("EU002", errors.Wrap(apiutil.ErrUniqueViolation, "email already blocked"))
	ErrInvalidValue                           = apiutil.NewError("EU400", errors.Wrap(apiutil.ErrInvalidValue, "invalid value"))
	ErrInternalServer                         = apiutil.NewError("EU500", errors.Wrap(apiutil.ErrInternalServer, "internal server error"))
	ErrIncompletePurchaseOrders               = apiutil.NewError("E96002", errors.Wrap(apiutil.ErrResourcePreconditionFailed, "cannot cancel account with incomplete purchase orders"))
	ErrIncompleteSalesOrders                  = apiutil.NewError("E96003", errors.Wrap(apiutil.ErrResourcePreconditionFailed, "cannot cancel account with incomplete sales orders"))
	ErrIncompletePurchaseOrdersAndSalesOrders = apiutil.NewError("E96003a", errors.Wrap(apiutil.ErrResourcePreconditionFailed, "cannot cancel account with incomplete purchase orders and sales orders"))
	ErrInvalidUSPostalCode                    = apiutil.NewError("EU004", errors.Wrap(apiutil.ErrInvalidRequest, "invalid US postal code"))
)
