package service

import (
	"fmt"
	"regexp"
	"strings"
	timePkg "time"

	"as-api/as/api/user/user/domain"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/env"
	"as-api/as/foundations/logger"
	"as-api/as/internal/auth"
	"as-api/as/internal/email"
	fcmtoken "as-api/as/internal/fcm-token"
	"as-api/as/internal/order"
	"as-api/as/internal/payment"
	"as-api/as/internal/user"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/generator"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/jwt"
	"as-api/as/pkg/time"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
	"go.uber.org/dig"
)

type svc struct {
	user    user.UserDomain
	auth    auth.AuthDomain
	email   email.SMTPService
	order   order.OrderDomain
	payment payment.PaymentDomain
	fcm     fcmtoken.FCMTokenDomain

	env env.MapperData
	v   validator.Validator
	log logger.Logger
}

type ServiceDI struct {
	dig.In
	User    user.UserDomain
	Auth    auth.AuthDomain
	Order   order.OrderDomain
	Payment payment.PaymentDomain
	Email   email.SMTPService
	Fcm     fcmtoken.FCMTokenDomain
	Env     env.MapperData
	Log     logger.Logger
	V       validator.Validator
}

func NewService(di ServiceDI) domain.UserService {
	return &svc{
		user:    di.User,
		auth:    di.Auth,
		order:   di.Order,
		payment: di.Payment,
		email:   di.Email,
		fcm:     di.Fcm,
		env:     di.Env,
		v:       di.V,
		log:     di.Log,
	}
}

// RegisterUser implements domain.UserService
func (s *svc) RegisterUser(ctx context.Context, req dtos.RegisterUserRequest) (*dtos.AuthResponse, error) {
	// Validate request
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	claims, err := context.GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return nil, errors.Wrapf(apiutil.ErrNotFoundToken, "get claims: %v", err)
	}

	au, err := s.auth.FindOneByID(ctx, claims.AID)
	if err != nil {
		return nil, errors.Wrap(err, "find auth by ID")
	}

	if au == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "auth not found")
	}

	if au.UserID != nil {
		return nil, errors.Wrap(apiutil.ErrEmailAlreadyExists, "user already registered")
	}

	// Set email from auth
	req.Email = pointer.Ptr(au.Username)

	// Check if user with email already exists
	existingUser, err := s.user.FindByEmail(ctx, *req.Email)
	if err != nil && !errors.Is(err, apiutil.ErrResourceNotFound) {
		return nil, errors.Wrap(err, "find user by email")
	}

	if existingUser != nil {
		return nil, errors.Wrap(apiutil.ErrEmailAlreadyExists, "user with this email already exists")
	}

	// Create domain user from DTO
	domainUser, err := convertDTORegisterUserRequestToDomain(&req)
	if err != nil {
		return nil, errors.Wrap(err, "convert DTO user to domain user")
	}

	// Validate US postal codes selectively:
	// 1. Primary: validate shipping address if it exists and is provided
	// 2. Fallback: validate home address if shipping is not provided OR identical to home
	// 3. No validation for billing address
	var addressToValidate *user.Address
	var addressType string

	if domainUser.ShippingAddress != nil && !areAddressesIdentical(domainUser.ShippingAddress) {
		// Shipping address exists and is different from home address - validate shipping
		addressToValidate = domainUser.ShippingAddress
		addressType = "shipping address"
	} else {
		// Either no shipping address or shipping is identical to home - validate home
		addressToValidate = domainUser.HomeAddress
		addressType = "home address"
	}

	if err := s.validateUSPostalCode(ctx, addressToValidate); err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("validate %s postal code", addressType))
	}

	// Generate account ID
	domainUser.AccountId = pointer.Ptr(generator.GenerateID(10))

	if req.RegisterSellerRequest != nil {
		seller := convertRegistSellerRequestToDomain(req.RegisterSellerRequest)
		seller.CountryCode = domainUser.CountryCode
		seller.RegionID = domainUser.RegionID
		seller.AccountID = domainUser.AccountId

		// Create seller
		seller, err := s.user.RegisterSeller(ctx, seller)
		if err != nil {
			return nil, errors.Wrap(err, "register seller")
		}

		domainUser.SellerID = seller.ID
	}

	// Create user
	u, err := s.user.Create(ctx, domainUser)
	if err != nil {
		return nil, errors.Wrap(err, "create user")
	}

	uid := pointer.Safe(u.ID)

	// Attach user ID to auth
	if err := s.auth.AttachUserID(ctx, au.ID, uid, req.Password); err != nil {
		return nil, errors.Wrap(err, "attach user ID to auth")
	}

	claims = auth.Claims{
		AID:     au.ID,
		UID:     uid,
		SID:     u.SellerID,
		Name:    u.Name,
		IsAdmin: false,
	}

	// Set user payment method if credit card access ID is provided
	if req.CreditCardAccessId != nil {
		go func() {
			ctx := context.WithValue(context.Background(), context.ClaimsCtx, claims)
			if _, err := s.UpdateUserPaymentMethod(ctx, uid, &dtos.UpdatePaymentMethodsRequest{
				AccessId: *req.CreditCardAccessId,
			}); err != nil {
				s.log.Error("update user payment method", logger.FieldMap{
					"error": err,
				})
			}
		}()
	}

	token, err := s.auth.GenJWT(ctx, claims)
	if err != nil {
		return nil, errors.Wrap(err, "gen jwt")
	}

	return &dtos.AuthResponse{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
	}, nil
}

// UpdateUserProfile implements domain.UserService
func (s *svc) UpdateUserProfile(ctx context.Context, id string, req dtos.UpdateUserProfileRequest) error {
	// Validate request
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Find user by ID
	existingUser, err := s.user.ReadOne(ctx, uid)
	if err != nil {
		return errors.Wrap(err, "read user by ID")
	}

	if existingUser == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	// Update user from request data
	if err := updateUserFromRequest(existingUser, req); err != nil {
		return err
	}

	// Validate US postal codes selectively:
	// 1. Primary: validate shipping address if it exists and is provided
	// 2. Fallback: validate home address if shipping is not provided OR identical to home
	// 3. No validation for billing address
	var addressToValidate *user.Address
	var addressType string

	if existingUser.ShippingAddress != nil && !areAddressesIdentical(existingUser.ShippingAddress) {
		// Shipping address exists and is different from home address - validate shipping
		addressToValidate = existingUser.ShippingAddress
		addressType = "shipping address"
	} else {
		// Either no shipping address or shipping is identical to home - validate home
		addressToValidate = existingUser.HomeAddress
		addressType = "home address"
	}

	if err := s.validateUSPostalCode(ctx, addressToValidate); err != nil {
		return errors.Wrap(err, fmt.Sprintf("validate %s postal code", addressType))
	}

	// Update user in database, this will handle addresses internally
	// avoiding multiple database calls
	_, err = s.user.Update(ctx, uid, existingUser)
	if err != nil {
		return errors.Wrap(err, "update user")
	}

	return nil
}

// GetUserProfile implements domain.UserService
func (s *svc) GetUserProfile(ctx context.Context, id string) (*dtos.UserProfileResponse, error) {
	// Validate ID
	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	// Check if the requesting user is the same as the user being requested
	if uid != id {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Fetch user from database
	userDomain, err := s.user.ReadOne(ctx, id)
	if err != nil {
		return nil, errors.Wrap(err, "read user by ID")
	}

	if userDomain == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	// Convert domain user to UserProfileResponse
	response := &dtos.UserProfileResponse{
		User: pointer.Safe(convertDomainUserToDTO(userDomain)),
	}

	// Convert addresses
	if userDomain.HomeAddress != nil {
		response.HomeAddress = convertDomainAddressToDTO(userDomain.HomeAddress)
	}
	if userDomain.ShippingAddress != nil {
		response.ShippingAddress = convertDomainAddressToDTO(userDomain.ShippingAddress)
	}
	if userDomain.BillingAddress != nil {
		response.BillingAddress = convertDomainAddressToDTO(userDomain.BillingAddress)
	}
	if userDomain.CreditCard != nil {
		response.CreditCard = ConvertDomainCreditCardToDTO(userDomain.CreditCard)
	}

	// Fetch and include seller information if user has a seller profile
	if userDomain.SellerID != nil {
		seller, err := s.user.ReadSellerByID(ctx, *userDomain.SellerID)
		if err != nil {
			return nil, errors.Wrap(err, "read seller by ID")
		}

		if seller != nil {
			response.Seller = convertDomainSellerToDTO(seller)

			// Include CompanyInfo from seller if available
			if seller.CompanyInfo != nil {
				response.CompanyInfo = convertDomainCompanyInfoToDTO(seller.CompanyInfo)
			}
		}
	}

	return response, nil
}

// UpdatePassword implements domain.UserService
func (s *svc) UpdatePassword(ctx context.Context, id string, req dtos.UpdatePasswordRequest) error {
	// Validate request
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	// Check if the requesting user is the same as the user being requested
	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Get auth by user ID
	auth, err := s.auth.FindAuthByUserID(ctx, id)
	if err != nil {
		return errors.Wrap(err, "find auth by user ID")
	}

	if auth == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "auth not found")
	}

	// Verify current password
	_, err = s.auth.PasswordAuthen(ctx, auth.Username, req.CurrentPassword)
	if err != nil {
		return errors.Wrap(err, "invalid current password")
	}

	// Update password
	if err := s.auth.SetPassword(ctx, auth.ID, req.NewPassword); err != nil {
		return errors.Wrap(err, "update password")
	}

	return nil
}

// UpdateEmail implements domain.UserService
func (s *svc) UpdateEmail(ctx context.Context, id string, req dtos.UpdateEmailRequest) error {
	// Validate request
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	// Check if the requesting user is the same as the user being requested
	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Check if email already exists
	existingUser, err := s.auth.FindAuthByUsername(ctx, req.Email)
	if err != nil && !errors.Is(err, apiutil.ErrResourceNotFound) {
		return errors.Wrap(err, "find user by email")
	}

	if existingUser != nil {
		if pointer.Safe(existingUser.UserID) == id {
			return errors.Wrap(apiutil.ErrEmailAlreadyUpdated, "email already updated")
		}

		return errors.Wrap(apiutil.ErrEmailAlreadyExists, "email already exists")
	}

	// Find auth by user ID
	auth, err := s.auth.FindAuthByUserID(ctx, id)
	if err != nil {
		return errors.Wrap(err, "find auth by user ID")
	}

	if auth == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "auth not found")
	}

	// Get current user info (for name in email template)
	user, err := s.user.ReadOne(ctx, id)
	if err != nil {
		return errors.Wrap(err, "read user by ID")
	}

	if user == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	// Generate verify email token
	token, err := s.auth.GenVerifyEmailJWT(ctx, auth.ID, struct{ Email string }{Email: req.Email})
	if err != nil {
		return errors.Wrap(err, "generate verify email token")
	}

	// Create verify email link
	verifyLink := fmt.Sprintf("%s/mailAddress?uid=%s&token=%s", s.env.ApplinkHost, id, token)

	// Generate email content from template
	emailContent, err := TemplateMailUpdateEmail(map[string]interface{}{
		"Name":  pointer.Safe(user.Name),
		"Email": req.Email,
		"Link":  verifyLink,
	})
	if err != nil {
		return errors.Wrap(err, "generate email content")
	}

	// Send email
	if err := s.email.Send(ctx, email.SendMailRequest{
		To:      req.Email, // Send to new email
		Subject: _updateEmailSubject,
		Body:    emailContent,
	}); err != nil {
		return errors.Wrap(err, "send email")
	}

	return nil
}

// VerifyEmail implements domain.UserService
func (s *svc) VerifyEmail(ctx context.Context, id string, req dtos.VerifyEmailRequest) error {
	// Validate request
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Verify email token
	verifiedAuth, err := s.auth.VerifyEmailToken(ctx, req.Token)
	if err != nil {
		return errors.Wrapf(apiutil.ErrInvalidToken, "verify email token: %v", err)
	}

	// Check if the auth ID matches with the user's auth
	auth, err := s.auth.FindAuthByUserID(ctx, id)
	if err != nil {
		return errors.Wrap(err, "find auth by user ID")
	}

	if auth == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "auth not found")
	}

	if verifiedAuth.ID != auth.ID {
		return errors.Wrap(apiutil.ErrPermissionDenied, "token not for this user")
	}

	// Update auth in database
	if err := s.auth.UpdateUsername(ctx, auth.ID, verifiedAuth.Username); err != nil {
		return errors.Wrap(err, "update auth")
	}

	return nil
}

// UpdateUserNotificationSettings implements domain.UserService
func (s *svc) UpdateUserNotificationSettings(ctx context.Context, id string, req dtos.NotificationSettingsRequest) error {
	// Validate request
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context and check if it matches the requested ID
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Convert DTO to domain model
	notificationSettings := ConvertDTOToNotificationSettings(req)

	// Update notification settings
	if err := s.user.UpdateNotificationSettings(ctx, id, notificationSettings); err != nil {
		return errors.Wrap(err, "update notification settings")
	}

	return nil
}

func (s *svc) GetUserNotificationSettings(ctx context.Context, id string) (*dtos.NotificationSettingsResponse, error) {
	// Validate ID
	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context and check if it matches the requested ID
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	if uid != id {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	// Get notification settings from domain
	notificationSettings, err := s.user.GetNotificationSettings(ctx, id)
	if err != nil {
		return nil, errors.Wrap(err, "get notification settings")
	}

	// Convert domain model to DTO
	response := ConvertNotificationSettingsToDTO(notificationSettings)

	return response, nil
}

func (s *svc) UpdateUserTerms(ctx context.Context, id string, req dtos.UpdateUserTermsRequest) error {
	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context and check if it matches the requested ID
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrapf(apiutil.ErrPermissionDenied, "get user ID: %v", err)
	}

	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	userData, err := s.user.ReadOne(ctx, id)
	if err != nil {
		return errors.Wrap(err, "get user ID")
	}

	confirmTermDate, err := time.ParseTimeInDefaultTZ(timePkg.RFC3339, req.ConfirmTermDate)
	if err != nil {
		return errors.Wrap(err, "parse date")
	}

	userData.ConfirmTermDate = &confirmTermDate
	userData.IsTermsRequired = pointer.Ptr(false)

	_, err = s.user.Update(ctx, id, userData)
	if err != nil {
		return errors.Wrap(err, "update user confirm terms date")
	}

	return nil
}

func (s *svc) UpdateUserPaymentMethod(ctx context.Context, id string, req *dtos.UpdatePaymentMethodsRequest) (*dtos.UpdatePaymentMethodsResponse, error) {
	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	aid, err := context.GetAuthID(ctx)
	if err != nil {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "get AID")
	}

	uid, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "get user ID")
	}

	if uid != id {
		return nil, errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	userData, err := s.user.ReadOne(ctx, id)
	if err != nil {
		return nil, errors.Wrap(err, "get user ID")
	}

	if userData == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	memberCard := payment.MemberCard{
		MemberID: aid,
	}

	if userData.CreditCard != nil {
		memberCard.CardID = userData.CreditCard.CardID
	}

	storeCardReq := &payment.StoreCardRequest{
		AccessID:   req.AccessId,
		MemberCard: memberCard,
	}

	storeCardRes, err := s.payment.StoreCard(ctx, storeCardReq)
	if err != nil {
		return nil, errors.Wrap(err, "store card")
	}

	creditCard := ConvertDomainPaymentCreditCardToDomainUser(storeCardRes)
	billingAddress := convertDTOAddressToDomain(req.BillingAddress)
	_, err = s.user.Update(ctx, id, &user.User{
		CreditCard:     creditCard,
		BillingAddress: billingAddress,
	})
	if err != nil {
		return nil, errors.Wrap(err, "update user credit card")
	}

	return &dtos.UpdatePaymentMethodsResponse{
		CardResult: ConvertDomainCreditCardToDTO(creditCard),
	}, nil
}

// CancelAccount implements domain.UserService.
func (s *svc) CancelAccount(ctx context.Context, id string, req *dtos.CancelAccountRequest) error {
	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context and check if it matches the requested ID
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrapf(apiutil.ErrPermissionDenied, "get user ID: %v", err)
	}

	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	userData, err := s.user.ReadOne(ctx, id)
	if err != nil {
		return errors.Wrap(err, "get user ID")
	}

	if userData == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	// Validation checks before allowing account cancellation
	if err := s.checkCancelAccount(ctx, userData); err != nil {
		return errors.Wrap(err, "check cancel account")
	}

	userData.CancelReason = &user.CancelReason{
		PricesTooHigh:       req.PricesTooHigh,
		DeliveryTooLong:     req.DeliveryTooLong,
		ProductsUnavailable: req.ProductsUnavailable,
		ItemProblem:         req.ItemProblem,
		SellerUntrustworthy: req.SellerUntrustworthy,
		AppDifficultToUse:   req.AppDifficultToUse,
		TooManyEmails:       req.TooManyEmails,
		SituationChanged:    req.SituationChanged,
		DontKnowHowToList:   req.DontKnowHowToList,
		ItemsDidNotSell:     req.ItemsDidNotSell,
		OtherReason:         req.OtherReason,
	}

	// Update user status to suspended
	userData.Status = pointer.Ptr(user.AccountStatusSuspended)

	err = s.user.CancelAccount(ctx, id, userData)
	if err != nil {
		return errors.Wrap(err, "update user confirm terms date")
	}

	err = s.auth.DeleteAuthByUserID(ctx, id)
	if err != nil {
		return errors.Wrap(err, "delete auth by user ID")
	}

	return nil
}

// CheckCancelAccount implements domain.UserService.
func (s *svc) CheckCancelAccount(ctx context.Context, id string) error {
	if err := s.v.ValidateVar(id, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Get user ID from context and check if it matches the requested ID
	uid, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrapf(apiutil.ErrPermissionDenied, "get user ID: %v", err)
	}

	if uid != id {
		return errors.Wrap(apiutil.ErrPermissionDenied, "wrong login user")
	}

	userData, err := s.user.ReadOne(ctx, id)
	if err != nil {
		return errors.Wrap(err, "get user ID")
	}

	if userData == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	if err := s.checkCancelAccount(ctx, userData); err != nil {
		return errors.Wrap(err, "check cancel account")
	}

	return nil
}

func (s *svc) checkCancelAccount(ctx context.Context, userData *user.User) error {
	// Validation checks before allowing account cancellation
	var hasNoIncompletePurchases, hasNoIncompleteSales bool
	var err error

	// Check for incomplete purchase orders (user as buyer)
	// Returns true when there are NO incomplete purchases
	hasNoIncompletePurchases, err = s.order.CheckOrderDetailStatusBuyer(ctx, pointer.Safe(userData.ID))
	if err != nil {
		return errors.Wrap(err, "check incomplete purchase orders")
	}

	// Check for incomplete sales orders (user as seller)
	// Returns true when there are NO incomplete sales
	if userData.SellerID != nil {
		hasNoIncompleteSales, err = s.order.CheckOrderDetailStatusSeller(ctx, *userData.SellerID)
		if err != nil {
			return errors.Wrap(err, "check incomplete sales orders")
		}
	}

	// Convert to hasIncomplete flags for easier logic
	hasIncompletePurchases := !hasNoIncompletePurchases
	hasIncompleteSales := userData.SellerID != nil && !hasNoIncompleteSales

	switch {
	case hasIncompletePurchases && hasIncompleteSales:
		return ErrIncompletePurchaseOrdersAndSalesOrders
	case hasIncompletePurchases:
		return ErrIncompletePurchaseOrders
	case hasIncompleteSales:
		return ErrIncompleteSalesOrders
	}

	return nil
}

// validateUSPostalCode validates US postal codes against the district_taxes table
func (s *svc) validateUSPostalCode(ctx context.Context, address *user.Address) error {
	if address == nil {
		return nil
	}

	// Check if this is a US address (case-insensitive)
	if !isUSAddress(address.Country) {
		return nil // Skip validation for non-US addresses
	}

	// Check if postal code is provided
	if address.PostalCode == nil || strings.TrimSpace(*address.PostalCode) == "" {
		return errors.Wrap(ErrInvalidUSPostalCode, "postal code is required for US addresses")
	}

	// Normalize the postal code (handle 5-digit and 9-digit formats)
	normalizedPostalCode := normalizeUSPostalCode(*address.PostalCode)
	if normalizedPostalCode == "" {
		return errors.Wrap(ErrInvalidUSPostalCode, "invalid postal code format")
	}

	// Validate the postal code exists in the district_taxes table
	isValid, err := s.order.ValidateUSZipCode(ctx, normalizedPostalCode)
	if err != nil {
		return errors.Wrap(err, "failed to validate postal code")
	}

	if !isValid {
		return errors.Wrap(ErrInvalidUSPostalCode, "postal code not found in valid US postal codes")
	}

	return nil
}

// isUSAddress checks if the country represents the United States (case-sensitive)
func isUSAddress(country *string) bool {
	if country == nil {
		return false
	}

	return strings.ToUpper(strings.TrimSpace(*country)) == order.USA_COUNTRY_CODE
}

// normalizeUSPostalCode normalizes US postal codes to 5-digit format
// Handles both 5-digit (12345) and 9-digit (12345-6789) ZIP codes
func normalizeUSPostalCode(postalCode string) string {
	// Remove all whitespace
	cleaned := strings.ReplaceAll(strings.TrimSpace(postalCode), " ", "")

	// Regular expression to match valid US postal code formats
	// Matches: 12345 or 12345-6789
	zipRegex := regexp.MustCompile(`^(\d{5})(?:-\d{4})?$`)
	matches := zipRegex.FindStringSubmatch(cleaned)

	if len(matches) < 2 {
		return "" // Invalid format
	}

	// Return the 5-digit portion
	return matches[1]
}

// areAddressesIdentical determines if shipping address is identical to home address
// Uses the IsDifferentFromResidence field in the shipping address to make this determination
func areAddressesIdentical(shippingAddr *user.Address) bool {
	// If shipping address is nil, consider it identical to home (no separate shipping address)
	if shippingAddr == nil {
		return true
	}

	// Use the IsDifferentFromResidence field to determine if addresses are identical
	// If IsDifferentFromResidence is false (or not set), shipping is identical to home
	// If IsDifferentFromResidence is true, shipping is different from home
	return !shippingAddr.IsDifferentFromResidence
}
