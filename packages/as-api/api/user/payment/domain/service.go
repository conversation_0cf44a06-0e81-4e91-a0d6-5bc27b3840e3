package domain

import (
	dtos "as-api/as/dtos/user"
	"as-api/as/pkg/context"
)

type PaymentService interface {
	VerifyCreditCard(ctx context.Context, req *dtos.VerifyCreditCardRequest) (*dtos.VerifyCreditCardResponse, error)
	CreateVaultSetupToken(ctx context.Context, req *dtos.CreateVaultSetupTokenRequest) (*dtos.CreateVaultSetupTokenResponse, error)
	CreateVaultPaymentToken(ctx context.Context, req *dtos.CreateVaultPaymentTokenRequest) (*dtos.CreateVaultPaymentTokenResponse, error)
}
