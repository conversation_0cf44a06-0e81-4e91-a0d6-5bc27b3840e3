package service

import (
	"as-api/as/api/user/seller/domain"
	"as-api/as/cache"
	"as-api/as/foundations/logger"
	"as-api/as/internal/favorite"
	"as-api/as/internal/order"
	"as-api/as/internal/product"
	productreviews "as-api/as/internal/product-review"
	sellerdashboard "as-api/as/internal/seller-dashboard"
	"as-api/as/internal/user"
	"as-api/as/pkg/validator"

	"go.uber.org/dig"
)

type ServiceDI struct {
	dig.In

	UserDomain            user.UserDomain
	ProductDomain         product.ProductDomain
	ProductReview         productreviews.ProductReviewsDomain
	OrderDomain           order.OrderDomain
	FavoriteSellerDomain  favorite.FavoriteDomain
	SellerDashboardDomain sellerdashboard.SellerDashboardDomain
	CacheSvc              cache.CacheService
	Validator             validator.Validator
	Logger                logger.Logger
}

func New(di ServiceDI) domain.SellerService {
	return &svc{
		userDomain:            di.UserDomain,
		productDomain:         di.ProductDomain,
		productReviews:        di.ProductReview,
		orderDomain:           di.OrderDomain,
		favoriteDomain:        di.FavoriteSellerDomain,
		sellerDashboardDomain: di.SellerDashboardDomain,
		cacheSvc:              di.CacheSvc,
		v:                     di.Validator,
		logger:                di.Logger,
	}
}
