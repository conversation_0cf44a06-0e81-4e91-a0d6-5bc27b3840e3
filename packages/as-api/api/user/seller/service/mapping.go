package service

import (
	"time"

	dtos "as-api/as/dtos/user"
	"as-api/as/internal/favorite"
	"as-api/as/internal/order"
	"as-api/as/internal/product"
	"as-api/as/internal/user"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/helpers/sliceutils"
)

func convertDomainSellerToDto(seller *user.Seller) *dtos.Seller {
	if seller == nil {
		return nil
	}

	return &dtos.Seller{
		Id:             seller.ID,
		AccountId:      seller.AccountID,
		AccountType:    dtos.SellerAccountType(seller.AccountType),
		About:          seller.About,
		AvatarUrl:      seller.AvatarUrl,
		FavoriteBrands: seller.FavoriteBrands,
		HeaderImgUrl:   seller.HeaderImgUrl,
		ShopName:       seller.ShopName,
		Specialty:      seller.Specialty,
		StockLocation:  seller.StockLocation,
		CountryCode:    seller.CountryCode,
		RegionId:       seller.RegionID,
	}
}

// convertDomainProductToDto converts a domain product to a DTO product
func convertDomainProductToDto(p *product.Product) *dtos.Product {
	if p == nil {
		return nil
	}

	var releaseDate *string
	if p.ReleaseDate != nil {
		date := p.ReleaseDate.Format("2006-01-02")
		releaseDate = &date
	}

	var categories *[]dtos.ProductCategory
	if p.Categories != nil {
		results := make([]dtos.ProductCategory, 0, len(*p.Categories))
		for _, category := range *p.Categories {
			results = append(results, *convertDomainProductCategoryToDto(category))
		}
		categories = &results
	}

	return &dtos.Product{
		Brand:             convertDomainBrandToDto(p.Brand),
		BrandId:           p.BrandID,
		BrandNameOther:    p.BrandNameOther,
		Categories:        categories,
		CategoryId:        p.CategoryID,
		Condition:         pointer.PtrString[dtos.ProductCondition](p.Condition),
		Description:       p.Description,
		DiscountAmount:    p.DiscountAmount,
		DiscountRate:      p.DiscountRate,
		Id:                p.ID,
		Images:            p.Images,
		IsDiscount:        p.IsDiscount,
		Name:              p.Name,
		NumFavorites:      p.NumFavorites,
		Price:             p.Price,
		ReleaseDate:       releaseDate,
		SalesStatus:       pointer.PtrString[dtos.ProductSalesStatus](p.SalesStatus),
		SellerId:          p.SellerID,
		ShipFrom:          p.ShipFrom,
		ShippingMethod:    p.ShippingMethod,
		Size:              p.Size,
		SizeLange:         pointer.PtrInt[int](p.SizeLange),
		SizeDetail:        p.SizeDetail,
		Target:            pointer.PtrString[dtos.ProductTarget](p.Target),
		TransactionStatus: pointer.PtrString[dtos.ProductTransactionStatus](p.TransactionStatus),
	}
}

// convertDomainProductCategoryToDto converts a domain product category to a DTO product category
func convertDomainProductCategoryToDto(p *product.ProductCategory) *dtos.ProductCategory {
	if p == nil {
		return nil
	}

	var subCategories []dtos.ProductCategory
	if p.SubCategories != nil {
		subCategories = make([]dtos.ProductCategory, 0, len(*p.SubCategories))
		for _, subCategory := range *p.SubCategories {
			subCategoryDto := convertDomainProductCategoryToDto(subCategory)
			subCategories = append(subCategories, pointer.Safe(subCategoryDto))
		}
	}

	return &dtos.ProductCategory{
		Id:            p.ID,
		DisplayName:   p.Name,
		SubCategories: &subCategories,
	}
}

// convertDomainBrandToDto converts a domain brand to a DTO brand
func convertDomainBrandToDto(p *product.Brand) *dtos.ProductBrand {
	if p == nil {
		return nil
	}

	return &dtos.ProductBrand{
		Id:          p.ID,
		DisplayName: p.Name,
	}
}

func convertUpdateRequestToProduct(productRequest *dtos.ProductUpdateRequest) *product.Product {
	return &product.Product{
		Name:           productRequest.Name,
		CategoryID:     productRequest.CategoryId,
		Target:         pointer.PtrString[product.Target](productRequest.Target),
		Size:           productRequest.Size,
		SizeLange:      pointer.PtrInt[int32](productRequest.SizeLange),
		BrandID:        productRequest.BrandId,
		BrandNameOther: productRequest.BrandNameOther,
		Condition:      pointer.PtrString[product.Condition](productRequest.Condition),
		Description:    productRequest.Description,
		Images:         productRequest.Images,
		ReleaseDate:    productRequest.ReleaseDate,
		SalesStatus:    pointer.PtrString[product.SalesStatus](productRequest.SalesStatus),
		Price:          productRequest.Price,
		IsDiscount:     productRequest.IsDiscount,
		DiscountAmount: productRequest.DiscountAmount,
		DiscountRate:   productRequest.DiscountRate,
		SizeDetail:     productRequest.SizeDetail,
		ShippingMethod: productRequest.ShippingMethod,
	}
}

func convertProductRequestToProduct(productRequest *dtos.ProductRequest) *product.Product {
	if productRequest == nil {
		return nil
	}

	return &product.Product{
		Name:           pointer.Ptr(productRequest.Name),
		CategoryID:     productRequest.CategoryId,
		Target:         pointer.PtrString[product.Target](productRequest.Target),
		Size:           productRequest.Size,
		SizeLange:      pointer.PtrInt[int32](productRequest.SizeLange),
		BrandID:        productRequest.BrandId,
		BrandNameOther: productRequest.BrandNameOther,
		Condition:      pointer.PtrString[product.Condition](productRequest.Condition),
		Description:    productRequest.Description,
		Images:         productRequest.Images,
		ReleaseDate:    productRequest.ReleaseDate,
		SalesStatus:    pointer.PtrString[product.SalesStatus](productRequest.SalesStatus),
		Price:          pointer.Ptr(productRequest.Price),
		IsDiscount:     productRequest.IsDiscount,
		DiscountAmount: productRequest.DiscountAmount,
		DiscountRate:   productRequest.DiscountRate,
		SizeDetail:     productRequest.SizeDetail,
		ShippingMethod: productRequest.ShippingMethod,
	}
}

func convertSellerBlockUserToDTO(blockUser *user.SellerBlockUser) *dtos.BlockUser {
	if blockUser == nil {
		return nil
	}

	return &dtos.BlockUser{
		SellerId: pointer.Ptr(blockUser.SellerId),
		UserId:   pointer.Ptr(blockUser.UserId),
		Id:       pointer.Ptr(blockUser.Id),
	}
}

func convertOrderDetailDomainToDTOWithBlockStatus(detail *order.OrderDetail, isUserBlocked *bool) *dtos.PurchasedItem {
	if detail == nil {
		return nil
	}

	// Get the basic DTO
	dto := ConvertOrderDetailDomainToDTO(detail)

	// Update the order with block status if order information exists
	if dto.Order != nil && detail.Order != nil {
		dto.Order = convertDomainOrderToDTO(detail.Order, isUserBlocked)
	}

	return dto
}

func ConvertOrderDetailDomainToDTO(detail *order.OrderDetail) *dtos.PurchasedItem {
	if detail == nil {
		return nil
	}

	dto := &dtos.PurchasedItem{
		CreatedAt:         detail.CreatedAt,
		Id:                detail.ID,
		OrderId:           &detail.OrderID,
		ProductId:         &detail.ProductID,
		TransactionStatus: (*dtos.ProductTransactionStatus)(&detail.TransactionStatus),
		WaybillNumber:     detail.WayBillNumber,
		OrderNumber:       detail.OrderNumber,
		Amount:            pointer.Ptr(detail.Price),
		DiscountAmount:    detail.DiscountAmount,
		TotalAmount:       detail.TotalPrice,
		Note:              detail.Note,
		OrderEnd:          detail.OrderEnd,
		ShippedDate:       detail.ShippedDate,
		CompletedDate:     detail.CompletedDate,
		ReceivedDate:      detail.ReceivedDate,
	}

	// Map order information including buyer details (without block status)
	if detail.Order != nil {
		dto.Order = convertDomainOrderToDTO(detail.Order, nil)
	}

	// Only set TaxAmount and TaxRate if they are not zero values
	if detail.TaxAmount != 0 {
		dto.TaxAmount = pointer.Ptr(detail.TaxAmount)
	}
	if detail.TaxRate != 0 {
		dto.TaxRate = pointer.Ptr(detail.TaxRate)
	}

	if len(detail.TaxInfos) > 0 {
		infos := make([]dtos.TaxItemInfo, 0, len(detail.TaxInfos))
		for _, taxInfo := range detail.TaxInfos {
			infos = append(infos, pointer.Safe(ConvertDomainTaxItemInfoToDTO(taxInfo)))
		}
		dto.TaxInfos = pointer.Ptr(infos)
	}

	// Map sales fee information from SalesFeeInfo object
	if detail.SalesFeeInfo != nil {
		salesFeeInfo := &dtos.SalesFeeInfo{
			SalesFee:       detail.SalesFeeInfo.SalesFee,
			SalesFeeRate:   detail.SalesFeeInfo.SalesFeeRate,
			SalesTaxAmount: detail.SalesFeeInfo.SalesTaxAmount,
			SalesTaxRate:   detail.SalesFeeInfo.SalesTaxRate,
		}

		if len(detail.SalesFeeInfo.SalesTaxInfos) > 0 {
			salesTaxInfos := make([]dtos.TaxItemInfo, 0, len(detail.SalesFeeInfo.SalesTaxInfos))
			for _, salesTaxInfo := range detail.SalesFeeInfo.SalesTaxInfos {
				salesTaxInfos = append(salesTaxInfos, pointer.Safe(ConvertDomainTaxItemInfoToDTO(salesTaxInfo)))
			}
			salesFeeInfo.SalesTaxInfos = pointer.Ptr(salesTaxInfos)
		}

		dto.SalesFeeInfo = salesFeeInfo
	}

	if detail.Product != nil {
		dto.Product = &dtos.Product{
			Id:             detail.Product.ID,
			Name:           detail.Product.Name,
			Description:    detail.Product.Description,
			Images:         detail.Product.Images,
			ShippingMethod: detail.Product.ShippingMethod,
			ShipFrom:       detail.Product.CountryCode,
		}

		if detail.Product.Brand != nil {
			dto.Product.Brand = &dtos.ProductBrand{
				Id:          detail.Product.Brand.ID,
				DisplayName: detail.Product.Brand.Name,
			}
		}

		if detail.Product.Seller != nil {
			seller := detail.Product.Seller
			dto.Seller = &dtos.Seller{
				Id:             seller.ID,
				AccountId:      seller.AccountID,
				About:          seller.About,
				AccountType:    dtos.SellerAccountType(seller.AccountType),
				AvatarUrl:      seller.AvatarUrl,
				FavoriteBrands: seller.FavoriteBrands,
				HeaderImgUrl:   seller.HeaderImgUrl,
				ShopName:       seller.ShopName,
				Specialty:      seller.Specialty,
				StockLocation:  seller.StockLocation,
			}
		}
	}

	return dto
}

func convertDomainAddressToDTO(address *order.Address) *dtos.Address {
	if address == nil {
		return nil
	}

	return &dtos.Address{
		FullName:   address.FullName,
		Address1:   address.Address1,
		Address2:   address.Address2,
		City:       address.City,
		State:      address.State,
		Country:    address.Country,
		PostalCode: address.PostalCode,
		RegionId:   address.RegionId,
	}
}

func convertDomainOrderToDTO(orderDomain *order.Order, isUserBlocked *bool) *dtos.Order {
	if orderDomain == nil {
		return nil
	}

	dto := &dtos.Order{
		Id:                         orderDomain.ID,
		OrderNumber:                &orderDomain.OrderNumber,
		Amount:                     &orderDomain.Amount,
		TotalAmount:                &orderDomain.TotalAmount,
		TaxAmount:                  &orderDomain.TaxAmount,
		TaxRate:                    &orderDomain.TaxRate,
		AdministrativeFee:          &orderDomain.AdministrativeFee,
		AdministrativeFeeRate:      &orderDomain.AdministrativeFeeRate,
		AdministrativeFeeTaxRate:   &orderDomain.AdministrativeFeeTaxRate,
		AdministrativeFeeTaxAmount: &orderDomain.AdministrativeFeeTaxAmount,
		PurchaseFee:                &orderDomain.PurchaseFee,
		PurchaseFeeTaxRate:         &orderDomain.PurchaseFeeTaxRate,
		PurchaseFeeTaxAmount:       &orderDomain.PurchaseFeeTaxAmount,
	}

	// Convert time fields to string format
	if orderDomain.CreatedAt != nil {
		createdAtStr := orderDomain.CreatedAt.Format(time.RFC3339)
		dto.CreatedAt = &createdAtStr
	}
	if orderDomain.UpdatedAt != nil {
		updatedAtStr := orderDomain.UpdatedAt.Format(time.RFC3339)
		dto.UpdatedAt = &updatedAtStr
	}

	// Map shipping address
	if orderDomain.ShippingAddress != nil {
		dto.ShippingAddress = convertDomainAddressToDTO(orderDomain.ShippingAddress)
	}

	// Map buyer information and check block status
	if orderDomain.User != nil {
		dto.Buyer = convertDomainUserToDTO(orderDomain.User)

		// Set block status if provided
		if isUserBlocked != nil {
			dto.IsUserBlockedBySeller = isUserBlocked
		}
	}

	return dto
}

func convertDomainUserToDTO(user *order.User) *dtos.User {
	if user == nil {
		return nil
	}

	return &dtos.User{
		Id:          user.ID,
		Nickname:    user.Nickname,
		AccountId:   user.AccountID,
		FirstName:   user.FirstName,
		LastName:    user.LastName,
		CountryCode: user.CountryCode,
	}
}

func ConvertDomainTaxItemInfoToDTO(detail *order.TaxItemInfo) *dtos.TaxItemInfo {
	if detail == nil {
		return nil
	}

	return &dtos.TaxItemInfo{
		Country:  ConvertDomainCountryToDTO(&detail.Country),
		Region:   ConvertDomainRegionToDTO(detail.Region),
		District: ConvertDomainDistrictTaxToDTO(detail.District),
		TaxName:  pointer.PtrString[dtos.TaxName](detail.Country.TaxName),
		TaxDetails: pointer.Ptr(sliceutils.Map(detail.Taxes, func(taxDetail *order.TaxDetail) dtos.TaxDetail {
			return pointer.Safe(ConvertDomainTaxDetailToDTO(taxDetail))
		})),
	}
}

func ConvertDomainTaxDetailToDTO(detail *order.TaxDetail) *dtos.TaxDetail {
	if detail == nil {
		return nil
	}

	return &dtos.TaxDetail{
		TaxRate:   pointer.Ptr(detail.TaxRate),
		Amount:    pointer.Ptr(detail.Amount),
		TaxAmount: pointer.Ptr(detail.TaxAmount),
	}
}

func ConvertDomainCountryToDTO(detail *order.Country) *dtos.Country {
	if detail == nil {
		return nil
	}

	return &dtos.Country{
		Code:        &detail.Code,
		DisplayName: detail.DisplayName,
		TaxName:     pointer.PtrString[dtos.TaxName](detail.TaxName),
	}
}

func ConvertDomainRegionToDTO(detail *order.Region) *dtos.Region {
	if detail == nil {
		return nil
	}

	return &dtos.Region{
		Code:        detail.Code,
		DisplayName: detail.DisplayName,
	}
}

func ConvertDomainDistrictTaxToDTO(detail *order.DistrictTax) *dtos.Region {
	if detail == nil {
		return nil
	}

	return &dtos.Region{
		DisplayName: pointer.Ptr(map[string]string{
			"en": detail.TaxRegionName,
		}),
	}
}

func MapFavoriteSellerToFollowingUser(favSeller *favorite.FavoriteSeller) *dtos.FollowingUser {
	if favSeller == nil {
		return nil
	}

	followingUser := &dtos.FollowingUser{}

	// Map direct fields
	if favSeller.ID != "" {
		followingUser.Id = &favSeller.ID
	}
	if favSeller.UserID != "" {
		followingUser.UserId = &favSeller.UserID
	}
	if favSeller.SellerID != "" {
		followingUser.SellerId = &favSeller.SellerID
	}

	// Map the associated User (if present) to the more detailed UserDetailed
	if favSeller.User != nil {
		followingUser.User = MapUserToUserDetailed(favSeller.User)
	}

	return followingUser
}

// MapUserToUserDetailed maps a simplified User to a more detailed UserDetailed DTO.
// Note: This function only maps the fields that are present in the simplified User.
// Other fields in UserDetailed (like Avatar, Email, DateOfBirth etc.)
// would need to be populated from a more complete source if available.
func MapUserToUserDetailed(user *favorite.User) *dtos.User {
	if user == nil {
		return nil
	}

	detailedUser := &dtos.User{
		FirstName:   user.FirstName,
		LastName:    user.LastName,
		Nickname:    user.Nickname,
		CountryCode: user.CountryCode,
		AccountId:   user.AccountID,
	}

	return detailedUser
}
