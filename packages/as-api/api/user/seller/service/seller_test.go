package service

import (
	"testing"
	"time"

	dtos "as-api/as/dtos/user"
	"as-api/as/internal/favorite"
	"as-api/as/internal/order"
	"as-api/as/internal/product"
	product_review "as-api/as/internal/product-review"
	"as-api/as/internal/user"
	favorite2 "as-api/as/mocks/domains/favorite"
	ordermock "as-api/as/mocks/domains/order"
	productmock "as-api/as/mocks/domains/product"
	productreviewmock "as-api/as/mocks/domains/product-review"
	usermock "as-api/as/mocks/domains/user"
	"as-api/as/mocks/validator"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/jwt"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestService_GetSellerById(t *testing.T) {
	// Define test cases
	testCases := []struct {
		name          string
		sellerId      string
		userID        string
		setupMock     func(*usermock.UserDomain, *favorite2.FavoriteDomain)
		expectedResp  *dtos.Seller
		expectedError error
	}{
		{
			name:     "Success - Seller found with all fields and user logged in",
			sellerId: "seller123",
			userID:   "user456",
			setupMock: func(mockDomain *usermock.UserDomain, mockFavorite *favorite2.FavoriteDomain) {
				// Setup mock to return a complete seller
				mockDomain.On("ReadSellerByID", mock.Anything, "seller123").
					Return(&user.Seller{
						ID:             pointer.Ptr("seller123"),
						About:          pointer.Ptr(map[string]string{"en": "About this seller"}),
						AccountType:    "verified",
						AvatarUrl:      pointer.Ptr("https://example.com/avatar.jpg"),
						FavoriteBrands: &[]string{"nike", "adidas"},
						HeaderImgUrl:   pointer.Ptr("https://example.com/header.jpg"),
						ShopName:       "John's Shop",
						Specialty:      pointer.Ptr(map[string]string{"en": "Sneakers"}),
						StockLocation:  pointer.Ptr("Tokyo, Japan"),
					}, nil)

				mockFavorite.On("GetFavoriteSellersCount", mock.Anything, "seller123").Return(map[string]int{
					"seller123": 10,
				}, nil)

				mockFavorite.On("GetUserFavoriteSellersBySellerIDs", mock.Anything, "user456", "seller123").Return([]string{"seller123"}, nil)
			},
			expectedResp: &dtos.Seller{
				Id:             pointer.Ptr("seller123"),
				About:          pointer.Ptr(map[string]string{"en": "About this seller"}),
				AccountType:    "verified",
				AvatarUrl:      pointer.Ptr("https://example.com/avatar.jpg"),
				FavoriteBrands: &[]string{"nike", "adidas"},
				HeaderImgUrl:   pointer.Ptr("https://example.com/header.jpg"),
				ShopName:       "John's Shop",
				Specialty:      pointer.Ptr(map[string]string{"en": "Sneakers"}),
				StockLocation:  pointer.Ptr("Tokyo, Japan"),
				IsFavorite:     pointer.Ptr(true),
			},
			expectedError: nil,
		},
		{
			name:     "Success - Seller found with minimal fields and user not logged in",
			sellerId: "seller456",
			userID:   "",
			setupMock: func(mockDomain *usermock.UserDomain, mockFavorite *favorite2.FavoriteDomain) {
				// Setup mock to return a seller with minimal fields
				mockDomain.On("ReadSellerByID", mock.Anything, "seller456").
					Return(&user.Seller{
						ID:          pointer.Ptr("seller456"),
						AccountType: "basic",
						ShopName:    "Minimal Shop",
					}, nil)

				mockFavorite.On("GetFavoriteSellersCount", mock.Anything, "seller456").Return(map[string]int{
					"seller456": 5,
				}, nil)
			},
			expectedResp: &dtos.Seller{
				Id:          pointer.Ptr("seller456"),
				AccountType: "basic",
				ShopName:    "Minimal Shop",
			},
			expectedError: nil,
		},
		{
			name:     "Success - Seller found but user not favorited",
			sellerId: "seller789",
			userID:   "user123",
			setupMock: func(mockDomain *usermock.UserDomain, mockFavorite *favorite2.FavoriteDomain) {
				mockDomain.On("ReadSellerByID", mock.Anything, "seller789").
					Return(&user.Seller{
						ID:          pointer.Ptr("seller789"),
						AccountType: "basic",
						ShopName:    "Another Shop",
					}, nil)

				mockFavorite.On("GetFavoriteSellersCount", mock.Anything, "seller789").Return(map[string]int{
					"seller789": 0,
				}, nil)

				mockFavorite.On("GetUserFavoriteSellersBySellerIDs", mock.Anything, "user123", "seller789").Return([]string{}, nil)
			},
			expectedResp: &dtos.Seller{
				Id:          pointer.Ptr("seller789"),
				AccountType: "basic",
				ShopName:    "Another Shop",
				IsFavorite:  pointer.Ptr(false),
			},
			expectedError: nil,
		},
		{
			name:     "Error - Seller not found",
			sellerId: "nonexistent",
			userID:   "",
			setupMock: func(mockDomain *usermock.UserDomain, mockFavorite *favorite2.FavoriteDomain) {
				mockDomain.On("ReadSellerByID", mock.Anything, "nonexistent").
					Return(nil, apiutil.ErrResourceNotFound)
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrResourceNotFound,
		},
		{
			name:     "Error - Database error",
			sellerId: "seller123",
			userID:   "",
			setupMock: func(mockDomain *usermock.UserDomain, mockFavorite *favorite2.FavoriteDomain) {
				mockDomain.On("ReadSellerByID", mock.Anything, "seller123").
					Return(nil, errors.New("database error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("database error"),
		},
		{
			name:     "Error - GetUserFavoriteSellersBySellerIDs fails",
			sellerId: "seller999",
			userID:   "user999",
			setupMock: func(mockDomain *usermock.UserDomain, mockFavorite *favorite2.FavoriteDomain) {
				mockDomain.On("ReadSellerByID", mock.Anything, "seller999").
					Return(&user.Seller{
						ID:          pointer.Ptr("seller999"),
						AccountType: "basic",
						ShopName:    "Test Shop",
					}, nil)

				mockFavorite.On("GetFavoriteSellersCount", mock.Anything, "seller999").Return(map[string]int{
					"seller999": 2,
				}, nil)

				mockFavorite.On("GetUserFavoriteSellersBySellerIDs", mock.Anything, "user999", "seller999").Return(nil, errors.New("favorite error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("favoriteDomain.GetUserFavoriteSellersBySellerIDs: favorite error"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mock
			mockDomain := usermock.NewUserDomain(t)
			mockFavorite := favorite2.NewFavoriteDomain(t)
			tc.setupMock(mockDomain, mockFavorite)

			// Create service
			service := &svc{
				userDomain:     mockDomain,
				favoriteDomain: mockFavorite,
			}

			ctx := context.Background()
			if tc.userID != "" {
				claims := jwt.CustomClaims{
					UID: tc.userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
			}

			// Execute
			resp, err := service.GetSellerById(ctx, tc.sellerId)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				if errors.Is(tc.expectedError, apiutil.ErrResourceNotFound) {
					assert.ErrorIs(t, err, apiutil.ErrResourceNotFound)
				} else {
					assert.Contains(t, err.Error(), tc.expectedError.Error())
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)

				// Check all fields that should be present
				assert.Equal(t, *tc.expectedResp.Id, *resp.Id)
				assert.Equal(t, tc.expectedResp.AccountType, resp.AccountType)
				assert.Equal(t, tc.expectedResp.ShopName, resp.ShopName)

				// Check optional fields
				if tc.expectedResp.About != nil {
					assert.Equal(t, *tc.expectedResp.About, *resp.About)
				} else {
					assert.Nil(t, resp.About)
				}

				if tc.expectedResp.AvatarUrl != nil {
					assert.Equal(t, *tc.expectedResp.AvatarUrl, *resp.AvatarUrl)
				} else {
					assert.Nil(t, resp.AvatarUrl)
				}

				if tc.expectedResp.FavoriteBrands != nil {
					assert.Equal(t, *tc.expectedResp.FavoriteBrands, *resp.FavoriteBrands)
				} else {
					assert.Nil(t, resp.FavoriteBrands)
				}

				if tc.expectedResp.HeaderImgUrl != nil {
					assert.Equal(t, *tc.expectedResp.HeaderImgUrl, *resp.HeaderImgUrl)
				} else {
					assert.Nil(t, resp.HeaderImgUrl)
				}

				if tc.expectedResp.Specialty != nil {
					assert.Equal(t, *tc.expectedResp.Specialty, *resp.Specialty)
				} else {
					assert.Nil(t, resp.Specialty)
				}

				if tc.expectedResp.StockLocation != nil {
					assert.Equal(t, *tc.expectedResp.StockLocation, *resp.StockLocation)
				} else {
					assert.Nil(t, resp.StockLocation)
				}

				if tc.expectedResp.IsFavorite != nil {
					assert.Equal(t, *tc.expectedResp.IsFavorite, *resp.IsFavorite)
				} else {
					assert.Nil(t, resp.IsFavorite)
				}
			}

			// Verify mock expectations
			mockDomain.AssertExpectations(t)
			mockFavorite.AssertExpectations(t)
		})
	}
}

func TestService_GetSellerProducts(t *testing.T) {
	productTargetMen := dtos.ProductTarget("men")

	testCases := []struct {
		name          string
		sellerId      string
		setupMock     func(*productmock.ProductDomain, *favorite2.FavoriteDomain, *validator.Validator)
		expectedResp  *dtos.SellerProductsResponse
		expectedError error
	}{
		{
			name:     "Success - Products found",
			sellerId: "seller123",
			setupMock: func(mockDomain *productmock.ProductDomain, mockFavorite *favorite2.FavoriteDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "seller123", "required,uuid").Return(nil)

				mockDomain.On("GetProductsBySellerId", mock.Anything, "seller123", "", 1, 10).Return([]*product.Product{
					{
						ID:          pointer.Ptr("prod1"),
						Name:        pointer.Ptr(map[string]string{"en": "Nike Shoes"}),
						CategoryID:  pointer.Ptr("cat123"),
						Price:       pointer.Ptr(100.0),
						IsDiscount:  pointer.Ptr(false),
						Target:      pointer.Ptr(product.TargetMen),
						SalesStatus: pointer.Ptr(product.SalesStatusAvailable),
						CreatedAt:   pointer.Ptr(time.Now()),
						UpdatedAt:   pointer.Ptr(time.Now()),
						Brand:       &product.Brand{ID: pointer.Ptr("brand1"), Name: pointer.Ptr(map[string]string{"en": "Nike"})},
						Categories: &[]*product.ProductCategory{
							{ID: pointer.Ptr("cat123"), Name: pointer.Ptr(map[string]string{"en": "Shoes"})},
						},
					},
					{
						ID:          pointer.Ptr("prod2"),
						Name:        pointer.Ptr(map[string]string{"en": "Adidas Shoes"}),
						CategoryID:  pointer.Ptr("cat123"),
						Price:       pointer.Ptr(90.0),
						IsDiscount:  pointer.Ptr(false),
						Target:      pointer.Ptr(product.TargetMen),
						SalesStatus: pointer.Ptr(product.SalesStatusAvailable),
						CreatedAt:   pointer.Ptr(time.Now()),
						UpdatedAt:   pointer.Ptr(time.Now()),
						Brand:       &product.Brand{ID: pointer.Ptr("brand2"), Name: pointer.Ptr(map[string]string{"en": "Adidas"})},
						Categories: &[]*product.ProductCategory{
							{ID: pointer.Ptr("cat123"), Name: pointer.Ptr(map[string]string{"en": "Shoes"})},
						},
					},
				}, nil)

				// Mock favorite domain calls
				mockFavorite.On("GetFavoriteProductsCount", mock.Anything, "prod1", "prod2").Return(map[string]int{
					"prod1": 5,
					"prod2": 3,
				}, nil)
			},
			expectedResp: &dtos.SellerProductsResponse{
				Products: &[]dtos.Product{
					{
						Id:         pointer.Ptr("prod1"),
						Name:       pointer.Ptr(map[string]string{"en": "Nike Shoes"}),
						CategoryId: pointer.Ptr("cat123"),
						Price:      pointer.Ptr(100.0),
						Categories: &[]dtos.ProductCategory{
							{Id: pointer.Ptr("cat123"), DisplayName: pointer.Ptr(map[string]string{"en": "Shoes"})},
						},
						Target:       &productTargetMen,
						Brand:        &dtos.ProductBrand{Id: pointer.Ptr("brand1"), DisplayName: pointer.Ptr(map[string]string{"en": "Nike"})},
						IsDiscount:   pointer.Ptr(false),
						NumFavorites: pointer.Ptr(5),
					},
					{
						Id:         pointer.Ptr("prod2"),
						Name:       pointer.Ptr(map[string]string{"en": "Adidas Shoes"}),
						CategoryId: pointer.Ptr("cat123"),
						Price:      pointer.Ptr(90.0),
						Categories: &[]dtos.ProductCategory{
							{Id: pointer.Ptr("cat123"), DisplayName: pointer.Ptr(map[string]string{"en": "Shoes"})},
						},
						Target:       &productTargetMen,
						Brand:        &dtos.ProductBrand{Id: pointer.Ptr("brand2"), DisplayName: pointer.Ptr(map[string]string{"en": "Adidas"})},
						IsDiscount:   pointer.Ptr(false),
						NumFavorites: pointer.Ptr(3),
					},
				},
			},
			expectedError: nil,
		},
		{
			name:     "Success - No products found",
			sellerId: "empty_seller",
			setupMock: func(mockDomain *productmock.ProductDomain, mockFavorite *favorite2.FavoriteDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "empty_seller", "required,uuid").Return(nil)

				mockDomain.On("GetProductsBySellerId", mock.Anything, "empty_seller", "", 1, 10).Return([]*product.Product{}, nil)
			},
			expectedResp:  nil,
			expectedError: nil,
		},
		{
			name:     "Error - Invalid seller ID",
			sellerId: "invalid-id",
			setupMock: func(mockDomain *productmock.ProductDomain, mockFavorite *favorite2.FavoriteDomain, mockValidator *validator.Validator) {
				// Setup validator mock to return error
				mockValidator.On("ValidateVar", "invalid-id", "required,uuid").Return(errors.New("invalid UUID"))
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name:     "Error - Unauthorized access",
			sellerId: "seller123",
			setupMock: func(mockDomain *productmock.ProductDomain, mockFavorite *favorite2.FavoriteDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "seller123", "required,uuid").Return(nil)

				mockDomain.On("GetProductsBySellerId", mock.Anything, "seller123", "", 1, 10).Return(nil, errors.Wrap(apiutil.ErrPermissionDenied, "database error"))
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrPermissionDenied,
		},
		{
			name:     "Error - Repository error",
			sellerId: "seller123",
			setupMock: func(mockDomain *productmock.ProductDomain, mockFavorite *favorite2.FavoriteDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "seller123", "required,uuid").Return(nil)

				// Setup domain mock to return error
				mockDomain.On("GetProductsBySellerId", mock.Anything, "seller123", "", 1, 10).Return(nil, errors.New("database error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("database error"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			mockDomain := productmock.NewProductDomain(t)
			mockUser := usermock.NewUserDomain(t)
			mockFavorite := favorite2.NewFavoriteDomain(t)
			mockValidator := validator.NewValidator(t)

			// Configure mocks
			tc.setupMock(mockDomain, mockFavorite, mockValidator)

			// Create service
			service := &svc{
				productDomain:  mockDomain,
				userDomain:     mockUser,
				favoriteDomain: mockFavorite,
				v:              mockValidator,
			}

			// Create context with claims
			var ctx context.Context

			claims := jwt.CustomClaims{UID: "uid1"}
			ctx = context.WithValue(context.Background(), context.ClaimsCtx, claims)

			// Execute
			resp, err := service.GetSellerProducts(ctx, tc.sellerId, dtos.GetSellerProductsParams{})

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				// Check if error contains the expected error (not exact match due to wrapping)
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
				if tc.expectedResp == nil {
					assert.Nil(t, resp)
				} else {
					assert.NotNil(t, resp)
					assert.NotNil(t, resp.Products)
					assert.Equal(t, len(*tc.expectedResp.Products), len(*resp.Products))

					// Check first product if present
					if len(*tc.expectedResp.Products) > 0 {
						expected := (*tc.expectedResp.Products)[0]
						actual := (*resp.Products)[0]

						assert.Equal(t, expected.Id, actual.Id)
						assert.Equal(t, expected.Name, actual.Name)
						assert.Equal(t, expected.Price, actual.Price)
						assert.Equal(t, expected.CategoryId, actual.CategoryId)

						// Check NumFavorites
						if expected.NumFavorites != nil {
							assert.Equal(t, *expected.NumFavorites, *actual.NumFavorites)
						}

						if expected.Brand != nil {
							assert.Equal(t, expected.Brand.Id, actual.Brand.Id)
							assert.Equal(t, expected.Brand.DisplayName, actual.Brand.DisplayName)
						}

						if expected.Categories != nil && len(*expected.Categories) > 0 && actual.Categories != nil && len(*actual.Categories) > 0 {
							assert.Equal(t, (*expected.Categories)[0].Id, (*actual.Categories)[0].Id)
							assert.Equal(t, (*expected.Categories)[0].DisplayName, (*actual.Categories)[0].DisplayName)
						}
					}
				}
			}

			// Verify mocks
			mockDomain.AssertExpectations(t)
			mockFavorite.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestService_UpdateSellerProductById(t *testing.T) {
	sellerID := "seller-uuid"
	userID := "user-uuid"
	claims := jwt.CustomClaims{
		UID: userID,
	}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims)
	updatedNames := map[string]string{"en": "Updated Product"}
	updatedDescription := map[string]string{"en": "New Description"}

	testCases := []struct {
		name          string
		ctx           context.Context
		productId     string
		request       *dtos.ProductUpdateRequest
		setupMock     func(*productmock.ProductDomain, *usermock.UserDomain, *validator.Validator)
		expectedError error
	}{
		{
			name:      "Success - Product updated with all fields",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Name:        &updatedNames,
				Price:       pointer.Ptr(120.0),
				Description: &updatedDescription,
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{SellerID: &sellerID}, nil)
				mockProduct.On("GetProductsById", mock.Anything, "valid-uuid").Return([]*product.Product{
					{SellerID: &sellerID},
				}, nil)
				mockProduct.On("UpdateProduct", mock.Anything, "valid-uuid", mock.AnythingOfType("*product.Product")).Return(nil)
			},
			expectedError: nil,
		},
		{
			name:      "Success - Partial update",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Description: &updatedDescription,
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{SellerID: &sellerID}, nil)
				mockProduct.On("GetProductsById", mock.Anything, "valid-uuid").Return([]*product.Product{
					{SellerID: &sellerID},
				}, nil)
				mockProduct.On("UpdateProduct", mock.Anything, "valid-uuid", mock.AnythingOfType("*product.Product")).Return(nil)
			},
			expectedError: nil,
		},
		{
			name:      "Error - Invalid product ID format",
			ctx:       ctxWithUser,
			productId: "invalid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").Return(errors.New("invalid uuid format"))
			},
			expectedError: errors.New("validate: invalid uuid format"),
		},
		{
			name:      "Error - Invalid request payload",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(-10.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(errors.New("invalid price"))
			},
			expectedError: errors.New("validate: invalid price"),
		},
		{
			name:      "Error - No user ID in context",
			ctx:       context.Background(),
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
			},
			expectedError: errors.New("get user id: get user ID: GetClaims: failed get claims in context"),
		},
		{
			name:      "Error - User not found",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(nil, errors.New("user not found"))
			},
			expectedError: errors.New("get user: user not found"),
		},
		{
			name:      "Error - User is not a seller",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{SellerID: nil}, nil)
			},
			expectedError: errors.New("user is not seller"),
		},
		{
			name:      "Error - Product not found",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{SellerID: &sellerID}, nil)
				mockProduct.On("GetProductsById", mock.Anything, "valid-uuid").Return([]*product.Product{}, nil)
			},
			expectedError: apiutil.ErrResourceNotFound,
		},
		{
			name:      "Error - Product belongs to different seller",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				differentSellerID := "different-seller-uuid"
				mockProduct.On("GetProductsById", mock.Anything, "valid-uuid").Return([]*product.Product{
					{SellerID: &differentSellerID},
				}, nil)
			},
			expectedError: errors.New("product not belong seller"),
		},
		{
			name:      "Error - GetProductsById fails",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockProduct.On("GetProductsById", mock.Anything, "valid-uuid").Return(nil, errors.New("database error"))
			},
			expectedError: errors.New("get products: database error"),
		},
		{
			name:      "Error - Update product fails",
			ctx:       ctxWithUser,
			productId: "valid-uuid",
			request: &dtos.ProductUpdateRequest{
				Price: pointer.Ptr(150.0),
			},
			setupMock: func(mockProduct *productmock.ProductDomain, mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "valid-uuid", "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.ProductUpdateRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockProduct.On("GetProductsById", mock.Anything, "valid-uuid").Return([]*product.Product{
					{SellerID: &sellerID},
				}, nil)
				mockProduct.On("UpdateProduct", mock.Anything, "valid-uuid", mock.AnythingOfType("*product.Product")).Return(errors.New("database error"))
			},
			expectedError: errors.New("UpdateSellerProductById: database error"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockProduct := productmock.NewProductDomain(t)
			mockUser := usermock.NewUserDomain(t)
			mockValidator := validator.NewValidator(t)
			tc.setupMock(mockProduct, mockUser, mockValidator)

			s := svc{
				productDomain: mockProduct,
				userDomain:    mockUser,
				v:             mockValidator,
			}

			// Act
			err := s.UpdateSellerProductById(tc.ctx, tc.productId, tc.request)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify
			mockProduct.AssertExpectations(t)
			mockUser.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestService_GetSellerRatings(t *testing.T) {
	testCases := []struct {
		name          string
		sellerId      string
		setupMock     func(*productreviewmock.ProductReviewsDomain, *validator.Validator)
		expectedResp  *dtos.RatingSellerResponse
		expectedError error
	}{
		{
			name:     "Success - Rating found",
			sellerId: "335c5a1c-969c-4a1f-9b42-bbdaf9ff3f0d",
			setupMock: func(mockDomain *productreviewmock.ProductReviewsDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "335c5a1c-969c-4a1f-9b42-bbdaf9ff3f0d", "required,uuid").Return(nil)

				// Setup domain mock
				mockDomain.On("GetRatingBySellerId", mock.Anything, "335c5a1c-969c-4a1f-9b42-bbdaf9ff3f0d").Return(&product_review.GetRatingBySellerIdResponse{
					AvgRating:     pointer.Ptr(4.567),
					TotalOrders:   pointer.Ptr(25),
					AvgDaysToShip: pointer.Ptr(3.2),
				}, nil)
			},
			expectedResp: &dtos.RatingSellerResponse{
				AvgRating:     pointer.Ptr(4.567),
				TotalOrders:   pointer.Ptr(25),
				AvgDaysToShip: pointer.Ptr(3.2),
			},
			expectedError: nil,
		},
		{
			name:     "Success - No ratings found",
			sellerId: "11111111-**************-************",
			setupMock: func(mockDomain *productreviewmock.ProductReviewsDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "11111111-**************-************", "required,uuid").Return(nil)

				// Setup domain mock to return 0
				mockDomain.On("GetRatingBySellerId", mock.Anything, "11111111-**************-************").Return(&product_review.GetRatingBySellerIdResponse{}, nil)
			},
			expectedResp:  &dtos.RatingSellerResponse{},
			expectedError: nil,
		},
		{
			name:     "Error - Invalid seller ID",
			sellerId: "invalid-uuid",
			setupMock: func(mockDomain *productreviewmock.ProductReviewsDomain, mockValidator *validator.Validator) {
				// Setup validator to return error
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").Return(errors.New("invalid UUID format"))
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name:     "Error - Domain service error",
			sellerId: "*************-8888-9999-000000000000",
			setupMock: func(mockDomain *productreviewmock.ProductReviewsDomain, mockValidator *validator.Validator) {
				// Setup validator mock
				mockValidator.On("ValidateVar", "*************-8888-9999-000000000000", "required,uuid").Return(nil)

				// Setup domain to return error
				mockDomain.On("GetRatingBySellerId", mock.Anything, "*************-8888-9999-000000000000").Return(nil, errors.New("database error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("productReviews.GetRatingBySellerId: database error"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			mockDomain := productreviewmock.NewProductReviewsDomain(t)
			mockValidator := validator.NewValidator(t)

			// Configure mocks
			tc.setupMock(mockDomain, mockValidator)

			// Create service
			service := &svc{
				productReviews: mockDomain,
				v:              mockValidator,
			}

			// Execute
			resp, err := service.GetSellerRatings(context.Background(), tc.sellerId)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tc.expectedResp.AvgRating, resp.AvgRating)
				assert.Equal(t, tc.expectedResp.TotalOrders, resp.TotalOrders)
				assert.Equal(t, tc.expectedResp.AvgDaysToShip, resp.AvgDaysToShip)
			}

			// Verify mocks
			mockDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestService_BlockUser(t *testing.T) {
	// Define test cases
	testCases := []struct {
		name          string
		ctx           context.Context
		request       *dtos.BlockUserRequest
		setupMock     func(*validator.Validator, *usermock.UserDomain)
		expectedError error
	}{
		{
			name: "Success - User blocked",
			ctx:  context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: "requesterUserID"}),
			request: &dtos.BlockUserRequest{
				UserId: "targetUserID",
			},
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain) {
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.BlockUserRequest")).Return(nil)
				mockUserDomain.On("ReadOne", mock.Anything, "requesterUserID").Return(&user.User{
					ID:       pointer.Ptr("requesterUserID"),
					SellerID: pointer.Ptr("requesterSellerID"),
				}, nil)
				mockUserDomain.On("BlockUser", mock.Anything, user.SellerBlockUser{
					UserId:   "targetUserID",
					SellerId: "requesterSellerID",
				}).Return(nil)
			},
			expectedError: nil,
		},
		{
			name: "Error - GetUserID from context failed",
			ctx:  context.Background(), // No claims in context
			request: &dtos.BlockUserRequest{
				UserId: "targetUserID",
			},
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain) {
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.BlockUserRequest")).Return(nil)
			},
			expectedError: errors.New("get user id: get user ID: GetClaims: failed get claims in context"),
		},
		{
			name: "Error - ReadOne user domain failed",
			ctx:  context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: "requesterUserID"}),
			request: &dtos.BlockUserRequest{
				UserId: "targetUserID",
			},
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain) {
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.BlockUserRequest")).Return(nil)
				mockUserDomain.On("ReadOne", mock.Anything, "requesterUserID").Return(nil, errors.New("database error"))
			},
			expectedError: errors.Wrap(errors.New("database error"), "get user"),
		},
		{
			name: "Error - User is not a seller",
			ctx:  context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: "requesterUserID"}),
			request: &dtos.BlockUserRequest{
				UserId: "targetUserID",
			},
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain) {
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.BlockUserRequest")).Return(nil)
				mockUserDomain.On("ReadOne", mock.Anything, "requesterUserID").Return(&user.User{
					ID:       pointer.Ptr("requesterUserID"),
					SellerID: nil, // Not a seller
				}, nil)
			},
			expectedError: errors.Wrap(apiutil.ErrForbidden, "user is not seller"),
		},
		{
			name: "Error - BlockUser domain failed",
			ctx:  context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: "requesterUserID"}),
			request: &dtos.BlockUserRequest{
				UserId: "targetUserID",
			},
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain) {
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.BlockUserRequest")).Return(nil)
				mockUserDomain.On("ReadOne", mock.Anything, "requesterUserID").Return(&user.User{
					ID:       pointer.Ptr("requesterUserID"),
					SellerID: pointer.Ptr("requesterSellerID"),
				}, nil)
				mockUserDomain.On("BlockUser", mock.Anything, user.SellerBlockUser{
					UserId:   "targetUserID",
					SellerId: "requesterSellerID",
				}).Return(errors.New("domain block error"))
			},
			expectedError: errors.Wrap(errors.New("domain block error"), "block user"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockValidator := validator.NewValidator(t)
			mockUserDomain := usermock.NewUserDomain(t)
			tc.setupMock(mockValidator, mockUserDomain)

			s := svc{
				v:          mockValidator,
				userDomain: mockUserDomain,
			}

			// Act
			err := s.BlockUser(tc.ctx, tc.request)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify mock expectations
			mockValidator.AssertExpectations(t)
			mockUserDomain.AssertExpectations(t)
		})
	}
}

func TestService_CreateSellerProduct(t *testing.T) {
	// Define a common user ID and seller ID for authenticated contexts
	userID := "user-123"
	sellerID := "seller-abc"
	ctxWithSeller := context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID})

	testCases := []struct {
		name          string
		ctx           context.Context
		request       *dtos.ProductRequest
		setupMocks    func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain)
		expectedError error
	}{
		{
			name: "SUCCESS - Product created successfully",
			ctx:  ctxWithSeller,
			request: &dtos.ProductRequest{
				Name:        map[string]string{"en": "Test Product"},
				Price:       100.0,
				Description: pointer.Ptr(map[string]string{"en": "A description"}),
				CategoryId:  pointer.Ptr("cat-1"),
				BrandId:     pointer.Ptr("brand-1"),
			},
			setupMocks: func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain) {
				v.On("Validate", mock.AnythingOfType("*dtos.ProductRequest")).Return(nil).Once()
				ud.On("ReadOne", mock.Anything, userID).Return(&user.User{ID: pointer.Ptr(userID), SellerID: pointer.Ptr(sellerID)}, nil).Once()
				pd.On("CreateProduct", mock.Anything, mock.AnythingOfType("*product.Product")).Return(nil).Once()
			},
			expectedError: nil,
		},
		{
			name: "FAILED - Invalid request payload",
			ctx:  ctxWithSeller,
			request: &dtos.ProductRequest{
				Price: -10.0, // Invalid price
			},
			setupMocks: func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain) {
				v.On("Validate", mock.AnythingOfType("*dtos.ProductRequest")).Return(errors.New("validation error")).Once()
			},
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name: "FAILED - User ID not found in context",
			ctx:  context.Background(), // No user ID in this context
			request: &dtos.ProductRequest{
				Name:  map[string]string{"en": "Test Product"},
				Price: 100.0,
			},
			setupMocks: func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain) {
				v.On("Validate", mock.AnythingOfType("*dtos.ProductRequest")).Return(nil).Once()
				// ReadOne should not be called, or should return an error if it is called with a nil user ID.
			},
			expectedError: errors.New("get user id"), // This error will be wrapped
		},
		{
			name: "FAILED - Error reading user from domain",
			ctx:  ctxWithSeller,
			request: &dtos.ProductRequest{
				Name:  map[string]string{"en": "Test Product"},
				Price: 100.0,
			},
			setupMocks: func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain) {
				v.On("Validate", mock.AnythingOfType("*dtos.ProductRequest")).Return(nil).Once()
				ud.On("ReadOne", mock.Anything, userID).Return(nil, errors.New("user not found")).Once()
			},
			expectedError: errors.New("get user: user not found"),
		},
		{
			name: "FAILED - User is not a seller",
			ctx:  ctxWithSeller,
			request: &dtos.ProductRequest{
				Name:  map[string]string{"en": "Test Product"},
				Price: 100.0,
			},
			setupMocks: func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain) {
				v.On("Validate", mock.AnythingOfType("*dtos.ProductRequest")).Return(nil).Once()
				ud.On("ReadOne", mock.Anything, userID).Return(&user.User{ID: pointer.Ptr(userID), SellerID: nil}, nil).Once() // User has no SellerID
			},
			expectedError: apiutil.ErrPermissionDenied,
		},
		{
			name: "FAILED - Error creating product in domain",
			ctx:  ctxWithSeller,
			request: &dtos.ProductRequest{
				Name:  map[string]string{"en": "Test Product"},
				Price: 100.0,
			},
			setupMocks: func(v *validator.Validator, ud *usermock.UserDomain, pd *productmock.ProductDomain) {
				v.On("Validate", mock.AnythingOfType("*dtos.ProductRequest")).Return(nil).Once()
				ud.On("ReadOne", mock.Anything, userID).Return(&user.User{ID: pointer.Ptr(userID), SellerID: pointer.Ptr(sellerID)}, nil).Once()
				pd.On("CreateProduct", mock.Anything, mock.AnythingOfType("*product.Product")).Return(errors.New("db write error")).Once()
			},
			expectedError: errors.New("productDomain.CreateProduct"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Initialize mocks
			mockValidator := validator.NewValidator(t)
			mockUserDomain := usermock.NewUserDomain(t)
			mockProductDomain := productmock.NewProductDomain(t)

			// Setup mock behaviors
			tc.setupMocks(mockValidator, mockUserDomain, mockProductDomain)

			// Create the service instance
			s := svc{
				v:             mockValidator,
				userDomain:    mockUserDomain,
				productDomain: mockProductDomain,
			}

			// Call the function under test
			err := s.CreateSellerProduct(tc.ctx, tc.request)

			// Assertions
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify all mock expectations were met
			mockValidator.AssertExpectations(t)
			mockUserDomain.AssertExpectations(t)
			mockProductDomain.AssertExpectations(t)
		})
	}
}

func TestService_DeleteBlockUser(t *testing.T) {
	sellerID := "seller-uuid"
	userID := "user-uuid"
	claims := jwt.CustomClaims{
		UID: userID,
	}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims)

	testCases := []struct {
		name             string
		ctx              context.Context
		blockUserID      string
		setupMock        func(*usermock.UserDomain, *validator.Validator)
		expectedError    error
		expectedErrorMsg string // For wrapped errors
	}{
		{
			name:        "Success - User unblocked",
			ctx:         ctxWithUser,
			blockUserID: "block-user-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "block-user-uuid", "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockUser.On("DeleteBlockUser", mock.Anything, "block-user-uuid").Return(nil)
			},
			expectedError: nil,
		},
		{
			name:        "Error - Invalid block user ID",
			ctx:         ctxWithUser,
			blockUserID: "invalid-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").Return(errors.New("invalid UUID format"))
			},
			expectedError:    apiutil.ErrInvalidRequest,
			expectedErrorMsg: "validate: invalid UUID format",
		},
		{
			name:        "Error - No user ID in context",
			ctx:         context.Background(),
			blockUserID: "block-user-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "block-user-uuid", "required,uuid").Return(nil)
			},
			expectedError:    errors.New("get user id"),
			expectedErrorMsg: "get user id: get user ID: GetClaims: failed get claims in context",
		},
		{
			name:        "Error - User not found",
			ctx:         ctxWithUser,
			blockUserID: "block-user-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "block-user-uuid", "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(nil, errors.New("user not found"))
			},
			expectedError:    errors.New("get user"),
			expectedErrorMsg: "get user: user not found",
		},
		{
			name:        "Error - User is not a seller",
			ctx:         ctxWithUser,
			blockUserID: "block-user-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "block-user-uuid", "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: nil, // User is not a seller
				}, nil)
			},
			expectedError:    apiutil.ErrForbidden,
			expectedErrorMsg: "user is not seller",
		},
		{
			name:        "Error - Delete block user fails",
			ctx:         ctxWithUser,
			blockUserID: "block-user-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "block-user-uuid", "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockUser.On("DeleteBlockUser", mock.Anything, "block-user-uuid").Return(errors.New("database error"))
			},
			expectedError:    errors.New("delete block user"),
			expectedErrorMsg: "delete block user: database error",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockUser := usermock.NewUserDomain(t)
			mockValidator := validator.NewValidator(t)
			tc.setupMock(mockUser, mockValidator)

			s := svc{
				userDomain: mockUser,
				v:          mockValidator,
			}

			// Act
			err := s.DeleteBlockUser(tc.ctx, tc.blockUserID)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErrorMsg)
			} else {
				assert.NoError(t, err)
			}

			// Verify
			mockUser.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestService_GetBlockUsers(t *testing.T) {
	// Define a common user ID and seller ID for authenticated context
	userID := "user-id-123"
	sellerID := "seller-id-456"

	// Create a context with user ID for authenticated calls
	claims := jwt.CustomClaims{UID: userID}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims)

	tests := []struct {
		name          string
		ctx           context.Context
		params        dtos.GetBlockUsersParams
		setupMock     func(*usermock.UserDomain)
		expectedResp  *dtos.BlockUsersResponse
		expectedError error
	}{
		{
			name: "SUCCESS - Get block users successfully",
			ctx:  ctxWithUser,
			params: dtos.GetBlockUsersParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUserDomain *usermock.UserDomain) {
				// Mock ReadOne to return a user who is a seller
				mockUserDomain.On("ReadOne", mock.Anything, userID).
					Return(&user.User{
						ID:       pointer.Ptr(userID),
						SellerID: pointer.Ptr(sellerID),
					}, nil).Once()

				// Mock GetListBlockUsers to return a list of block users
				mockUserDomain.On("GetListBlockUsers", mock.Anything, &user.GetListBlockUsersRequest{
					SellerId: sellerID,
					Page:     pointer.Ptr(1),
					Limit:    pointer.Ptr(10),
				}).Return([]*user.SellerBlockUser{
					{Id: "block-id-1", SellerId: sellerID, UserId: "blocked-user-a"},
					{Id: "block-id-2", SellerId: sellerID, UserId: "blocked-user-b"},
				}, nil).Once()
			},
			expectedResp: &dtos.BlockUsersResponse{
				Data: &[]dtos.BlockUser{
					{Id: pointer.Ptr("block-id-1"), SellerId: pointer.Ptr(sellerID), UserId: pointer.Ptr("blocked-user-a")},
					{Id: pointer.Ptr("block-id-2"), SellerId: pointer.Ptr(sellerID), UserId: pointer.Ptr("blocked-user-b")},
				},
			},
			expectedError: nil,
		},
		{
			name: "SUCCESS - Get block users with no results",
			ctx:  ctxWithUser,
			params: dtos.GetBlockUsersParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUserDomain *usermock.UserDomain) {
				mockUserDomain.On("ReadOne", mock.Anything, userID).
					Return(&user.User{
						ID:       pointer.Ptr(userID),
						SellerID: pointer.Ptr(sellerID),
					}, nil).Once()

				// Mock GetListBlockUsers to return an empty list
				mockUserDomain.On("GetListBlockUsers", mock.Anything, &user.GetListBlockUsersRequest{
					SellerId: sellerID,
					Page:     pointer.Ptr(1),
					Limit:    pointer.Ptr(10),
				}).Return([]*user.SellerBlockUser{}, nil).Once()
			},
			expectedResp: &dtos.BlockUsersResponse{
				Data: pointer.Ptr([]dtos.BlockUser{}), // Should return an empty slice, not nil
			},
			expectedError: nil,
		},
		{
			name: "FAILED - Get user ID from context error",
			ctx:  context.Background(), // Context without user ID
			params: dtos.GetBlockUsersParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock:     func(mockUserDomain *usermock.UserDomain) {}, // No mock calls expected
			expectedResp:  nil,
			expectedError: errors.New("get user id: get user ID: GetClaims: failed get claims in context"),
		},
		{
			name: "FAILED - Read user fails",
			ctx:  ctxWithUser,
			params: dtos.GetBlockUsersParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUserDomain *usermock.UserDomain) {
				mockUserDomain.On("ReadOne", mock.Anything, userID).
					Return(nil, errors.New("user not found in DB")).Once()
			},
			expectedResp:  nil,
			expectedError: errors.New("get user: user not found in DB"),
		},
		{
			name: "FAILED - User is not a seller",
			ctx:  ctxWithUser,
			params: dtos.GetBlockUsersParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUserDomain *usermock.UserDomain) {
				// Mock ReadOne to return a user who is NOT a seller (SellerID is nil)
				mockUserDomain.On("ReadOne", mock.Anything, userID).
					Return(&user.User{
						ID:       pointer.Ptr(userID),
						SellerID: nil, // This user is not a seller
					}, nil).Once()
			},
			expectedResp:  nil,
			expectedError: errors.Wrap(apiutil.ErrForbidden, "user is not seller"),
		},
		{
			name: "FAILED - GetListBlockUsers returns error",
			ctx:  ctxWithUser,
			params: dtos.GetBlockUsersParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUserDomain *usermock.UserDomain) {
				mockUserDomain.On("ReadOne", mock.Anything, userID).
					Return(&user.User{
						ID:       pointer.Ptr(userID),
						SellerID: pointer.Ptr(sellerID),
					}, nil).Once()

				mockUserDomain.On("GetListBlockUsers", mock.Anything, &user.GetListBlockUsersRequest{
					SellerId: sellerID,
					Page:     pointer.Ptr(1),
					Limit:    pointer.Ptr(10),
				}).Return(nil, errors.New("DB error on GetListBlockUsers")).Once()
			},
			expectedResp:  nil,
			expectedError: errors.New("userDomain.GetListBlockUsers: DB error on GetListBlockUsers"),
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			mockUserDomain := usermock.NewUserDomain(t)
			tt.setupMock(mockUserDomain) // Configure mocks for this test case

			s := svc{
				userDomain: mockUserDomain,
				// Add other dependencies if 'svc' has them, e.g., productDomain: productmock.NewProductDomain(t)
			}

			// Act
			resp, err := s.GetBlockUsers(tt.ctx, tt.params)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				// Check for wrapped errors: `errors.Cause` or `assert.Contains(t, err.Error(), expectedError.Error())`
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				if errors.Is(tt.expectedError, apiutil.ErrForbidden) {
					assert.ErrorIs(t, err, apiutil.ErrForbidden)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedResp.Data, resp.Data) // Compare data pointers directly
			}

			// Verify mock expectations
			mockUserDomain.AssertExpectations(t)
		})
	}
}

func TestService_DeleteProduct(t *testing.T) {
	validProductID := "valid-product-uuid"
	validUserID := "user-uuid-123"
	validSellerID := "seller-uuid-456"

	claims := jwt.CustomClaims{UID: validUserID}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims)
	ctxWithoutUser := context.Background()

	testCases := []struct {
		name          string
		ctx           context.Context
		productId     string
		setupMock     func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain)
		expectedError error
	}{
		{
			name:      "Success - Product deleted",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{{ID: pointer.Ptr(validProductID), SellerID: pointer.Ptr(validSellerID), SalesStatus: pointer.Ptr(product.SalesStatusAvailable)}}, nil).Once()
				mockProductDomain.On("DeleteProduct", ctxWithUser, validProductID).Return(nil).Once()
			},
			expectedError: nil,
		},
		{
			name:      "Error - Invalid Product ID",
			ctx:       ctxWithUser,
			productId: "invalid-product-id",
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", "invalid-product-id", "required,uuid").Return(errors.New("uuid validation failed")).Once()
			},
			expectedError: errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", errors.New("uuid validation failed")),
		},
		{
			name:      "Error - GetUserID fails (context without user)",
			ctx:       ctxWithoutUser, // Using context without user ID
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				// context.GetUserID will fail internally, no specific mock for GetUserID itself, but we test its effect.
			},
			// This exact error message depends on your context.GetUserID implementation
			expectedError: errors.Wrap(errors.New("get user ID: GetClaims: failed get claims in context"), "get user id"),
		},
		{
			name:      "Error - User not found",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(nil, errors.New("db error: user not found")).Once()
			},
			expectedError: errors.Wrap(errors.New("db error: user not found"), "get user"),
		},
		{
			name:      "Error - User is not a seller",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: nil}, nil).Once() // SellerID is nil
			},
			expectedError: errors.Wrap(apiutil.ErrForbidden, "user is not seller"),
		},
		{
			name:      "Error - GetProductsById fails",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return(nil, errors.New("db error: get products failed")).Once()
			},
			expectedError: errors.Wrap(errors.New("db error: get products failed"), "get product by id"),
		},
		{
			name:      "Error - Product not found (empty slice)",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{}, nil).Once() // Empty slice
			},
			expectedError: errors.Wrap(apiutil.ErrResourceNotFound, "product not found"),
		},
		{
			name:      "Error - Product not found (slice with nil product)",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{nil}, nil).Once() // Slice with nil product
			},
			expectedError: errors.Wrap(apiutil.ErrResourceNotFound, "product not found"),
		},
		{
			name:      "Error - Product does not belong to seller",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				differentSellerID := "different-seller-uuid"
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{{ID: pointer.Ptr(validProductID), SellerID: pointer.Ptr(differentSellerID)}}, nil).Once()
			},
			expectedError: errors.Wrap(apiutil.ErrForbidden, "product is not belong to seller"),
		},
		{
			name:      "Error - Product does not belong to seller (user seller ID nil, product seller ID exists)",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()     // User is a seller
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{{ID: pointer.Ptr(validProductID), SellerID: nil}}, nil).Once() // Product has no seller
			},
			expectedError: errors.Wrap(apiutil.ErrForbidden, "product is not belong to seller"),
		},
		{
			name:      "Error - Product cannot be deleted (sold status)",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{{ID: pointer.Ptr(validProductID), SellerID: pointer.Ptr(validSellerID), SalesStatus: pointer.Ptr(product.SalesStatusSold)}}, nil).Once()
			},
			expectedError: errors.Wrap(apiutil.ErrResourcePreconditionFailed, "product can only be deleted when status is draft or available"),
		},
		{
			name:      "Error - DeleteProduct fails",
			ctx:       ctxWithUser,
			productId: validProductID,
			setupMock: func(mockValidator *validator.Validator, mockUserDomain *usermock.UserDomain, mockProductDomain *productmock.ProductDomain) {
				mockValidator.On("ValidateVar", validProductID, "required,uuid").Return(nil).Once()
				mockUserDomain.On("ReadOne", ctxWithUser, validUserID).Return(&user.User{ID: pointer.Ptr(validUserID), SellerID: pointer.Ptr(validSellerID)}, nil).Once()
				mockProductDomain.On("GetProductsById", ctxWithUser, validProductID).Return([]*product.Product{{ID: pointer.Ptr(validProductID), SellerID: pointer.Ptr(validSellerID), SalesStatus: pointer.Ptr(product.SalesStatusDraft)}}, nil).Once()
				mockProductDomain.On("DeleteProduct", ctxWithUser, validProductID).Return(errors.New("db error: delete failed")).Once()
			},
			expectedError: errors.Wrap(errors.New("db error: delete failed"), "delete product"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockValidator := validator.NewValidator(t)
			mockUserDomain := usermock.NewUserDomain(t)
			mockProductDomain := productmock.NewProductDomain(t)

			s := svc{
				v:             mockValidator,
				userDomain:    mockUserDomain,
				productDomain: mockProductDomain,
			}

			tc.setupMock(mockValidator, mockUserDomain, mockProductDomain)

			// Act
			err := s.DeleteProduct(tc.ctx, tc.productId)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				// Using Contains because errors.Wrapf includes dynamic parts (like the original error message from validation)
				// and context.GetUserID might return a more specific internal error message.
				assert.Contains(t, err.Error(), tc.expectedError.Error())

				// For more precise checks on specific error types if needed
				if errors.Is(tc.expectedError, apiutil.ErrInvalidRequest) {
					assert.True(t, errors.Is(err, apiutil.ErrInvalidRequest) || errors.As(err, &apiutil.ErrInvalidRequest))
				}
				if errors.Is(tc.expectedError, apiutil.ErrForbidden) {
					assert.True(t, errors.Is(err, apiutil.ErrForbidden) || errors.As(err, &apiutil.ErrForbidden))
				}
				if errors.Is(tc.expectedError, apiutil.ErrResourceNotFound) {
					assert.True(t, errors.Is(err, apiutil.ErrResourceNotFound) || errors.As(err, &apiutil.ErrResourceNotFound))
				}

			} else {
				assert.NoError(t, err)
			}

			// Verify
			mockValidator.AssertExpectations(t)
			mockUserDomain.AssertExpectations(t)
			mockProductDomain.AssertExpectations(t)
		})
	}
}

func TestService_UpdatePublishSeller(t *testing.T) {
	userID := "user-uuid-test"
	sellerID := "seller-uuid-test"
	claims := jwt.CustomClaims{
		UID: userID,
	}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims) // Assuming ClaimsCtx is your context key

	validRequest := dtos.UpdatePublishSellerRequest{
		PublicStatus: pointer.Ptr(dtos.UpdatePublishSellerRequestPublicStatusDraft),
	}

	testCases := []struct {
		name             string
		ctx              context.Context
		request          dtos.UpdatePublishSellerRequest
		setupMock        func(*usermock.UserDomain, *validator.Validator) // Adjusted for your validator mock
		expectedError    error
		expectedErrorMsg string
	}{
		{
			name:    "Success - Seller status updated",
			ctx:     ctxWithUser,
			request: validRequest,
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("Validate", validRequest).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					SellerID: &sellerID,
				}, nil)
				mockUser.On("ReadSellerByID", mock.Anything, sellerID).Return(&user.Seller{
					ID: pointer.Ptr(sellerID),
				}, nil)
				mockUser.On("UpdateSeller", mock.Anything, userID, &user.Seller{
					ID:           pointer.Ptr(sellerID),
					PublicStatus: pointer.Ptr(user.PublishStatusDraft),
				}).Return(&user.Seller{}, nil)
			},
			expectedError: nil,
		},
		{
			name:    "Error - No user ID in context",
			ctx:     context.Background(), // Context without user ID
			request: validRequest,
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("Validate", validRequest).Return(nil)
			},
			expectedError:    errors.New("get user id"), // Placeholder, adjust to actual error from context.GetUserID
			expectedErrorMsg: "get user id: ",           // Example, adjust to your actual error message
		},
		{
			name:    "Error - User not found on ReadOne",
			ctx:     ctxWithUser,
			request: validRequest,
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("Validate", validRequest).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(nil, errors.New("user not found in db"))
			},
			expectedError:    errors.New("get user"),
			expectedErrorMsg: "get user: user not found in db",
		},
		{
			name:    "Error - User is not a seller",
			ctx:     ctxWithUser,
			request: validRequest,
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("Validate", validRequest).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					SellerID: nil, // User is not a seller
				}, nil)
			},
			expectedError:    apiutil.ErrForbidden,
			expectedErrorMsg: "user is not seller",
		},
		{
			name:    "Error - Seller not found on ReadSellerByID",
			ctx:     ctxWithUser,
			request: validRequest,
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("Validate", validRequest).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					SellerID: &sellerID,
				}, nil)
				mockUser.On("ReadSellerByID", mock.Anything, sellerID).Return(nil, errors.New("seller not found in db"))
			},
			expectedError:    errors.New("userDomain.ReadSellerByID"),
			expectedErrorMsg: "userDomain.ReadSellerByID: seller not found in db",
		},
		{
			name:    "Error - UpdateSeller fails",
			ctx:     ctxWithUser,
			request: validRequest,
			setupMock: func(mockUser *usermock.UserDomain, mockValidator *validator.Validator) {
				mockValidator.On("Validate", validRequest).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       pointer.Ptr(userID),
					SellerID: &sellerID,
				}, nil)
				mockUser.On("ReadSellerByID", mock.Anything, sellerID).Return(&user.Seller{
					ID:           pointer.Ptr(sellerID),
					PublicStatus: pointer.Ptr(user.PublishStatusPublish),
				}, nil)
				mockUser.On("UpdateSeller", mock.Anything, userID, mock.AnythingOfType("*user.Seller")).Return(nil, errors.New("db update failed"))
			},
			expectedError:    errors.New("userDomain.UpdateSeller"),
			expectedErrorMsg: "userDomain.UpdateSeller: db update failed",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockUserDomain := usermock.NewUserDomain(t) // Assuming NewUserDomain is the constructor for your mock
			mockValidator := validator.NewValidator(t)  // Assuming NewValidator is the constructor for your mock
			tc.setupMock(mockUserDomain, mockValidator)

			// s := NewService(mockUserDomain, mockValidator) // Assuming you have a constructor like this
			s := svc{ // Or initialize directly if simple
				userDomain: mockUserDomain,
				v:          mockValidator,
			}

			// Act
			err := s.UpdatePublishSeller(tc.ctx, tc.request)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				// Using Is for error type matching if errors are wrapped consistently,
				// or Contains for message checking.
				// For wrapped errors, ErrorContains is often more reliable than matching the exact error object.
				if tc.expectedErrorMsg != "" {
					assert.Contains(t, err.Error(), tc.expectedErrorMsg)
				}
				// You might also want to check the underlying error type if apiutil has specific error types
				if errors.Is(tc.expectedError, apiutil.ErrInvalidRequest) || errors.Is(tc.expectedError, apiutil.ErrForbidden) {
					assert.True(t, errors.Is(err, tc.expectedError) || errors.As(err, &tc.expectedError))
				}

			} else {
				assert.NoError(t, err)
			}

			// Verify
			mockUserDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestService_GetSoldItems(t *testing.T) {
	// Define a common time for testing
	now := time.Now().UTC().Truncate(time.Second)
	yesterday := now.AddDate(0, 0, -1)

	nowStr := now.Format(time.RFC3339)
	yesterdayStr := yesterday.Format(time.RFC3339)

	userID := "user-seller-123"
	sellerID := "seller-id-123"
	claims := jwt.CustomClaims{UID: userID}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims)

	testCases := []struct {
		name          string
		ctx           context.Context
		params        dtos.GetSoldItemsParams
		setupMock     func(*usermock.UserDomain, *ordermock.OrderDomain)
		expectedResp  *dtos.PurchasedItemsResponse
		expectedError error
	}{
		{
			name: "Success - Sold items found",
			ctx:  ctxWithUser,
			params: dtos.GetSoldItemsParams{
				From:    &yesterdayStr,
				To:      &nowStr,
				OrderBy: (*dtos.GetSoldItemsParamsOrderBy)(pointer.Ptr("createdAt")),
				Page:    pointer.Ptr(1),
				Limit:   pointer.Ptr(10),
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain) {
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)

				orderItemsDomain := []*order.OrderDetail{
					{
						ID:        pointer.Ptr("orderItem1"),
						CreatedAt: &now,
						OrderID:   "orderABC",
						ProductID: "prod1",
						Price:     99.99,
						Product: &order.Product{
							ID:   pointer.Ptr("prod1"),
							Name: pointer.Ptr(map[string]string{"en": "Sold Item A"}),
						},
						TransactionStatus: "completed",
					},
					{
						ID:        pointer.Ptr("orderItem2"),
						CreatedAt: &yesterday,
						OrderID:   "orderDEF",
						ProductID: "prod2",
						Price:     149.99,
						Product: &order.Product{
							ID:   pointer.Ptr("prod2"),
							Name: pointer.Ptr(map[string]string{"en": "Sold Item B"}),
						},
						TransactionStatus: "pending",
					},
				}
				mockOrder.On("GetOrderDetails", mock.Anything, order.GetOrderDetailsParam{
					OrderBy:  pointer.Ptr("createdAt"),
					Page:     pointer.Ptr(1),
					Limit:    pointer.Ptr(10),
					From:     &yesterday,
					To:       &now,
					SellerId: pointer.Safe(&sellerID),
				}).Return(orderItemsDomain, nil)
			},
			expectedResp: &dtos.PurchasedItemsResponse{
				Items: &[]dtos.PurchasedItem{
					{
						Id:        pointer.Ptr("orderItem1"),
						CreatedAt: &now,
						OrderId:   pointer.Ptr("orderABC"),
						Product: &dtos.Product{
							Id:   pointer.Ptr("prod1"),
							Name: pointer.Ptr(map[string]string{"en": "Sold Item A"}),
						},
						ProductId:         pointer.Ptr("prod1"),
						TransactionStatus: (*dtos.ProductTransactionStatus)(pointer.Ptr("completed")),
						Amount:            pointer.Ptr(99.99),
					},
					{
						Id:        pointer.Ptr("orderItem2"),
						CreatedAt: &yesterday,
						OrderId:   pointer.Ptr("orderDEF"),
						Product: &dtos.Product{
							Id:   pointer.Ptr("prod2"),
							Name: pointer.Ptr(map[string]string{"en": "Sold Item B"}),
						},
						ProductId:         pointer.Ptr("prod2"),
						TransactionStatus: (*dtos.ProductTransactionStatus)(pointer.Ptr("pending")),
						Amount:            pointer.Ptr(149.99),
					},
				},
			},
			expectedError: nil,
		},
		{
			name: "Success - No sold items found",
			ctx:  ctxWithUser,
			params: dtos.GetSoldItemsParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain) {
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetails", mock.Anything, order.GetOrderDetailsParam{
					Page:     pointer.Ptr(1),
					Limit:    pointer.Ptr(10),
					SellerId: pointer.Safe(&sellerID),
				}).Return([]*order.OrderDetail{}, nil)
			},
			expectedResp: &dtos.PurchasedItemsResponse{
				Items: &[]dtos.PurchasedItem{},
			},
			expectedError: nil,
		},
		{
			name:   "Error - No user ID in context",
			ctx:    context.Background(), // Context without user ID
			params: dtos.GetSoldItemsParams{},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain) {
				// No mock setup for user or order domain calls as it should fail early
			},
			expectedResp:  nil,
			expectedError: errors.New("get user id: get user ID: GetClaims: failed get claims in context"),
		},
		{
			name: "Error - User domain ReadOne fails",
			ctx:  ctxWithUser,
			params: dtos.GetSoldItemsParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain) {
				mockUser.On("ReadOne", mock.Anything, userID).Return(nil, errors.New("database error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("get user: database error"),
		},
		{
			name: "Error - User is not a seller",
			ctx:  ctxWithUser,
			params: dtos.GetSoldItemsParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain) {
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: nil, // User is not a seller
				}, nil)
			},
			expectedResp:  nil,
			expectedError: errors.Wrap(apiutil.ErrForbidden, "user is not seller"),
		},
		{
			name: "Error - Order domain GetOrderDetails fails",
			ctx:  ctxWithUser,
			params: dtos.GetSoldItemsParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain) {
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetails", mock.Anything, order.GetOrderDetailsParam{
					Page:     pointer.Ptr(1),
					Limit:    pointer.Ptr(10),
					SellerId: pointer.Safe(&sellerID),
				}).Return(nil, errors.New("order service error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("orderDomain.GetOrderDetails: order service error"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockUserDomain := usermock.NewUserDomain(t)
			mockOrderDomain := ordermock.NewOrderDomain(t)
			mockValidator := validator.NewValidator(t) // Validator is not directly used in GetSoldItems, but keep for consistency

			tc.setupMock(mockUserDomain, mockOrderDomain)

			s := svc{
				userDomain:  mockUserDomain,
				orderDomain: mockOrderDomain,
				v:           mockValidator, // Assign if your svc struct has it
			}

			// Act
			resp, err := s.GetSoldItems(tc.ctx, tc.params)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				if tc.expectedResp.Items != nil {
					assert.Equal(t, len(*tc.expectedResp.Items), len(*resp.Items))
					assert.Equal(t, tc.expectedResp, resp)
				} else {
					assert.Nil(t, resp.Items)
				}
			}

			// Verify
			mockUserDomain.AssertExpectations(t)
			mockOrderDomain.AssertExpectations(t)
			// mockValidator.AssertExpectations(t) // Only assert if validator was called
		})
	}
}

func TestService_CheckOrderDetailStatusSeller(t *testing.T) {
	// Define test cases
	testCases := []struct {
		name               string
		setupMocks         func(*usermock.UserDomain, *ordermock.OrderDomain)
		expectedResult     *dtos.GetStatusOrderDetailResponse
		expectedErr        error
		contextUserID      string // User ID to be returned by context.GetUserID
		contextUserIDError error  // Error to be returned by context.GetUserID
	}{
		{
			name:               "Success - Seller has pending orders",
			contextUserID:      "test-user-id",
			contextUserIDError: nil,
			setupMocks: func(umd *usermock.UserDomain, omd *ordermock.OrderDomain) {
				umd.On("ReadOne", mock.Anything, "test-user-id").
					Return(&user.User{ID: pointer.Ptr("test-user-id"), SellerID: pointer.Ptr("test-seller-id")}, nil)
				omd.On("CheckOrderDetailStatusSeller", mock.Anything, "test-seller-id").
					Return(true, nil)
			},
			expectedResult: &dtos.GetStatusOrderDetailResponse{Result: pointer.Ptr(true)},
			expectedErr:    nil,
		},
		{
			name:               "Success - Seller has no pending orders",
			contextUserID:      "test-user-id-no-pending",
			contextUserIDError: nil,
			setupMocks: func(umd *usermock.UserDomain, omd *ordermock.OrderDomain) {
				umd.On("ReadOne", mock.Anything, "test-user-id-no-pending").
					Return(&user.User{ID: pointer.Ptr("test-user-id-no-pending"), SellerID: pointer.Ptr("test-seller-id-2")}, nil)
				omd.On("CheckOrderDetailStatusSeller", mock.Anything, "test-seller-id-2").
					Return(false, nil)
			},
			expectedResult: &dtos.GetStatusOrderDetailResponse{Result: pointer.Ptr(false)},
			expectedErr:    nil,
		},
		{
			name:               "Error - GetUserID returns error",
			contextUserID:      "", // Not used, as error will be returned
			contextUserIDError: errors.New("context user id error"),
			setupMocks:         func(umd *usermock.UserDomain, omd *ordermock.OrderDomain) {}, // No domain mocks needed
			expectedResult:     nil,
			expectedErr:        errors.New("get user id: get user ID: GetClaims: failed get claims in context"), // Expected wrapped error
		},
		{
			name:               "Error - UserDomain ReadOne returns error",
			contextUserID:      "test-user-id",
			contextUserIDError: nil,
			setupMocks: func(umd *usermock.UserDomain, omd *ordermock.OrderDomain) {
				umd.On("ReadOne", mock.Anything, "test-user-id").
					Return(nil, apiutil.ErrResourceNotFound)
			},
			expectedResult: nil,
			expectedErr:    errors.New("get user: EC404"), // Expected wrapped error
		},
		{
			name:               "Error - User is not a seller (SellerID is nil)",
			contextUserID:      "test-user-not-seller",
			contextUserIDError: nil,
			setupMocks: func(umd *usermock.UserDomain, omd *ordermock.OrderDomain) {
				umd.On("ReadOne", mock.Anything, "test-user-not-seller").
					Return(&user.User{ID: pointer.Ptr("test-user-not-seller"), SellerID: nil}, nil)
			},
			expectedResult: nil,
			expectedErr:    errors.New("user is not seller: access denied"), // Expected wrapped error
		},
		{
			name:               "Error - OrderDomain CheckOrderDetailStatusSeller returns error",
			contextUserID:      "test-user-id-order-error",
			contextUserIDError: nil,
			setupMocks: func(umd *usermock.UserDomain, omd *ordermock.OrderDomain) {
				umd.On("ReadOne", mock.Anything, "test-user-id-order-error").
					Return(&user.User{ID: pointer.Ptr("test-user-id-order-error"), SellerID: pointer.Ptr("test-seller-id-3")}, nil)
				omd.On("CheckOrderDetailStatusSeller", mock.Anything, "test-seller-id-3").
					Return(false, errors.New("database connection error"))
			},
			expectedResult: nil,
			expectedErr:    errors.New("orderDomain.CheckOrderDetailStatusSeller: database connection error"), // Expected wrapped error
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create mock domains
			mockUserDomain := &usermock.UserDomain{}
			mockOrderDomain := &ordermock.OrderDomain{}

			// Configure mock behavior for domains
			tc.setupMocks(mockUserDomain, mockOrderDomain)

			svc := svc{ // Use 'svc' as defined in your provided code
				userDomain:  mockUserDomain,
				orderDomain: mockOrderDomain,
			}

			// Create a context that can be manipulated for user ID
			ctx := context.Background()
			if tc.contextUserID != "" {
				jwtClaims := jwt.CustomClaims{
					UID: tc.contextUserID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, jwtClaims)
			}

			// Call the service function
			result, err := svc.CheckOrderDetailStatusSeller(ctx)

			// Verify the result and error
			if tc.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErr.Error()) // Use Contains for wrapped errors
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedResult, result)
			}

			// Verify that all expected mock calls were made
			mockUserDomain.AssertExpectations(t)
			mockOrderDomain.AssertExpectations(t)
		})
	}
}

func TestSvc_GetFollowingUsers(t *testing.T) {
	testCases := []struct {
		name             string
		userID           string
		params           dtos.GetFollowingUsersParams
		setupMocks       func(*usermock.UserDomain, *favorite2.FavoriteDomain)
		expectedResponse *dtos.FollowingUserResponse
		expectedError    error
	}{
		{
			name:   "Success - User is a seller and has following users",
			userID: "user123",
			params: dtos.GetFollowingUsersParams{
				Limit: pointer.Ptr(10),
				Page:  pointer.Ptr(1),
			},
			setupMocks: func(umd *usermock.UserDomain, fdm *favorite2.FavoriteDomain) {
				umd.On("ReadOne", mock.Anything, "user123").Return(&user.User{
					SellerID: pointer.Ptr("seller123"),
				}, nil).Once()
				fdm.On("GetFollowingUsers", mock.Anything, &favorite.GetFollowingUsersParams{
					SellerID: pointer.Safe(pointer.Ptr("seller123")),
					Limit:    pointer.Ptr(10),
					Page:     pointer.Ptr(1),
				}).Return([]*favorite.FavoriteSeller{
					{
						ID: "followedUser1",
					},
					{
						ID: "followedUser2",
					},
				}, nil).Once()
			},
			expectedResponse: &dtos.FollowingUserResponse{
				Data: pointer.Ptr([]dtos.FollowingUser{
					{
						Id: pointer.Ptr("followedUser1"),
					},
					{
						Id: pointer.Ptr("followedUser2"),
					},
				}),
			},
			expectedError: nil,
		},
		{
			name:   "Success - User is a seller but no following users",
			userID: "user456",
			params: dtos.GetFollowingUsersParams{
				Limit: pointer.Ptr(10),
				Page:  pointer.Ptr(1),
			},
			setupMocks: func(umd *usermock.UserDomain, fdm *favorite2.FavoriteDomain) {
				umd.On("ReadOne", mock.Anything, "user456").Return(&user.User{
					SellerID: pointer.Ptr("seller456"),
				}, nil).Once()
				fdm.On("GetFollowingUsers", mock.Anything, &favorite.GetFollowingUsersParams{
					SellerID: pointer.Safe(pointer.Ptr("seller456")),
					Limit:    pointer.Ptr(10),
					Page:     pointer.Ptr(1),
				}).Return([]*favorite.FavoriteSeller{}, nil).Once()
			},
			expectedResponse: &dtos.FollowingUserResponse{
				Data: pointer.Ptr([]dtos.FollowingUser{}),
			},
			expectedError: nil,
		},
		{
			name:   "Error - Failed to get user ID from context",
			userID: "", // Simulate missing user ID in context
			params: dtos.GetFollowingUsersParams{
				Limit: pointer.Ptr(10),
				Page:  pointer.Ptr(1),
			},
			setupMocks: func(umd *usermock.UserDomain, fdm *favorite2.FavoriteDomain) {
				// No calls to userDomain or favoriteDomain expected
			},
			expectedResponse: nil,
			expectedError:    errors.New("get user id"), // The original function wraps the error
		},
		{
			name:   "Error - User is not found",
			userID: "user789",
			params: dtos.GetFollowingUsersParams{
				Limit: pointer.Ptr(10),
				Page:  pointer.Ptr(1),
			},
			setupMocks: func(umd *usermock.UserDomain, fdm *favorite2.FavoriteDomain) {
				umd.On("ReadOne", mock.Anything, "user789").Return(nil, apiutil.ErrResourceNotFound).Once()
			},
			expectedResponse: nil,
			expectedError:    errors.Wrap(apiutil.ErrResourceNotFound, "get user"),
		},
		{
			name:   "Error - User is not a seller",
			userID: "userNotSeller",
			params: dtos.GetFollowingUsersParams{
				Limit: pointer.Ptr(10),
				Page:  pointer.Ptr(1),
			},
			setupMocks: func(umd *usermock.UserDomain, fdm *favorite2.FavoriteDomain) {
				umd.On("ReadOne", mock.Anything, "userNotSeller").Return(&user.User{
					SellerID: nil, // User is not a seller
				}, nil).Once()
			},
			expectedResponse: nil,
			expectedError:    errors.Wrap(apiutil.ErrForbidden, "user is not seller"),
		},
		{
			name:   "Error - Failed to get following users from domain",
			userID: "user101",
			params: dtos.GetFollowingUsersParams{
				Limit: pointer.Ptr(10),
				Page:  pointer.Ptr(1),
			},
			setupMocks: func(umd *usermock.UserDomain, fdm *favorite2.FavoriteDomain) {
				umd.On("ReadOne", mock.Anything, "user101").Return(&user.User{
					SellerID: pointer.Ptr("seller101"),
				}, nil).Once()
				fdm.On("GetFollowingUsers", mock.Anything, &favorite.GetFollowingUsersParams{
					SellerID: pointer.Safe(pointer.Ptr("seller101")),
					Limit:    pointer.Ptr(10),
					Page:     pointer.Ptr(1),
				}).Return(nil, errors.New("internal server error")).Once()
			},
			expectedResponse: nil,
			expectedError:    errors.Wrap(errors.New("internal server error"), "favoriteDomain.GetFollowingUsers"), // Assuming the error is wrapped in the real function
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create mock domain services
			mockUserDomain := &usermock.UserDomain{}
			mockFavoriteDomain := &favorite2.FavoriteDomain{}

			// Setup mock behavior
			tc.setupMocks(mockUserDomain, mockFavoriteDomain)

			// Create the service instance with mocks
			s := svc{
				userDomain:     mockUserDomain,
				favoriteDomain: mockFavoriteDomain,
			}

			// Create a context with the user ID if provided
			ctx := context.Background()
			if tc.userID != "" {
				claims := jwt.CustomClaims{
					UID: tc.userID,
				}
				ctx = context.WithValue(ctx, context.ClaimsCtx, claims)
			}

			// Call the function under test
			resp, err := s.GetFollowingUsers(ctx, tc.params)

			// Assert error
			if tc.expectedError != nil {
				assert.Error(t, err)
				// Check for unwrapped error message or specific error type if needed
				assert.Contains(t, err.Error(), tc.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}

			// Assert response
			assert.Equal(t, tc.expectedResponse, resp)

			// Verify that all expected mock calls were made
			mockUserDomain.AssertExpectations(t)
			mockFavoriteDomain.AssertExpectations(t)
		})
	}
}

func TestService_GetSoldItemById(t *testing.T) {
	now := time.Now().UTC().Truncate(time.Second)
	userID := "user-seller-123"
	sellerID := "seller-id-123"
	orderDetailID := "order-detail-123"
	productID := "product-123"
	orderID := "order-abc"

	claims := jwt.CustomClaims{UID: userID}
	ctxWithUser := context.WithValue(context.Background(), context.ClaimsCtx, claims)

	testCases := []struct {
		name          string
		ctx           context.Context
		orderDetailId string
		setupMock     func(*usermock.UserDomain, *ordermock.OrderDomain, *validator.Validator)
		expectedResp  *dtos.PurchasedItem
		expectedError error
	}{
		{
			name:          "Success - Valid order detail found",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)

				orderDetailDomain := &order.OrderDetail{
					ID:                pointer.Ptr(orderDetailID),
					CreatedAt:         &now,
					OrderID:           orderID,
					ProductID:         productID,
					Price:             99.99,
					TransactionStatus: "completed",
					Product: &order.Product{
						ID:       pointer.Ptr(productID),
						Name:     pointer.Ptr(map[string]string{"en": "Test Product"}),
						SellerId: sellerID, // Product belongs to the seller
					},
				}
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(orderDetailDomain, nil)
			},
			expectedResp: &dtos.PurchasedItem{
				Id:                pointer.Ptr(orderDetailID),
				CreatedAt:         &now,
				OrderId:           &orderID,
				ProductId:         &productID,
				TransactionStatus: (*dtos.ProductTransactionStatus)(pointer.Ptr("completed")),
				Amount:            pointer.Ptr(99.99),
				Product: &dtos.Product{
					Id:   pointer.Ptr(productID),
					Name: pointer.Ptr(map[string]string{"en": "Test Product"}),
				},
			},
			expectedError: nil,
		},
		{
			name:          "Error - Invalid order detail ID format",
			ctx:           ctxWithUser,
			orderDetailId: "invalid-uuid",
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").Return(errors.New("invalid uuid format"))
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name:          "Error - No user ID in context",
			ctx:           context.Background(),
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
			},
			expectedResp:  nil,
			expectedError: errors.New("get user id"),
		},
		{
			name:          "Error - User not found",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(nil, apiutil.ErrResourceNotFound)
			},
			expectedResp:  nil,
			expectedError: errors.New("get user"),
		},
		{
			name:          "Error - User is not a seller",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: nil, // User is not a seller
				}, nil)
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrForbidden,
		},
		{
			name:          "Error - Order detail not found",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(nil, nil)
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrResourceNotFound,
		},
		{
			name:          "Error - Order detail service error",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(nil, errors.New("database error"))
			},
			expectedResp:  nil,
			expectedError: errors.New("orderDomain.GetOrderDetailById"),
		},
		{
			name:          "Error - Order detail belongs to different seller",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)

				differentSellerID := "different-seller-id"
				orderDetailDomain := &order.OrderDetail{
					ID:                pointer.Ptr(orderDetailID),
					CreatedAt:         &now,
					OrderID:           orderID,
					ProductID:         productID,
					Price:             99.99,
					TransactionStatus: "completed",
					Product: &order.Product{
						ID:       pointer.Ptr(productID),
						Name:     pointer.Ptr(map[string]string{"en": "Test Product"}),
						SellerId: differentSellerID, // Product belongs to different seller
					},
				}
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(orderDetailDomain, nil)
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrForbidden,
		},
		{
			name:          "Error - Order detail has no product",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)

				orderDetailDomain := &order.OrderDetail{
					ID:                pointer.Ptr(orderDetailID),
					CreatedAt:         &now,
					OrderID:           orderID,
					ProductID:         productID,
					Price:             99.99,
					TransactionStatus: "completed",
					Product:           nil, // No product associated
				}
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(orderDetailDomain, nil)
			},
			expectedResp:  nil,
			expectedError: apiutil.ErrForbidden,
		},
		{
			name:          "Success - Order detail with enhanced fields (shipping address, buyer info, block status)",
			ctx:           ctxWithUser,
			orderDetailId: orderDetailID,
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					SellerID: &sellerID,
				}, nil)

				// Mock IsUserBlockedBySeller to return false
				mockUser.On("IsUserBlockedBySeller", mock.Anything, sellerID, "buyer-123").Return(pointer.Ptr(false), nil)

				orderDetailDomain := &order.OrderDetail{
					ID:                pointer.Ptr(orderDetailID),
					CreatedAt:         &now,
					OrderID:           orderID,
					ProductID:         productID,
					Price:             99.99,
					TransactionStatus: "completed",
					Product: &order.Product{
						ID:       pointer.Ptr(productID),
						Name:     pointer.Ptr(map[string]string{"en": "Test Product"}),
						SellerId: sellerID,
					},
					Order: &order.Order{
						ID:          pointer.Ptr(orderID),
						CreatedAt:   &now,
						OrderNumber: "ORD-123",
						Amount:      100.0,
						TotalAmount: 90.0,
						ShippingAddress: &order.Address{
							FullName:   pointer.Ptr("John Doe"),
							Address1:   pointer.Ptr("123 Main St"),
							City:       pointer.Ptr("New York"),
							State:      pointer.Ptr("NY"),
							Country:    pointer.Ptr("USA"),
							PostalCode: pointer.Ptr("10001"),
						},
						User: &order.User{
							ID:          pointer.Ptr("buyer-123"),
							AccountID:   pointer.Ptr("buyer_account"),
							Nickname:    pointer.Ptr("nickname"),
							FirstName:   pointer.Ptr("John"),
							LastName:    pointer.Ptr("Doe"),
							CountryCode: pointer.Ptr("US"),
						},
					},
				}
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(orderDetailDomain, nil)
			},
			expectedResp: &dtos.PurchasedItem{
				Id:                pointer.Ptr(orderDetailID),
				CreatedAt:         &now,
				OrderId:           &orderID,
				ProductId:         &productID,
				TransactionStatus: (*dtos.ProductTransactionStatus)(pointer.Ptr("completed")),
				Amount:            pointer.Ptr(99.99),
				Product: &dtos.Product{
					Id:   pointer.Ptr(productID),
					Name: pointer.Ptr(map[string]string{"en": "Test Product"}),
				},
				Order: &dtos.Order{
					Id:          pointer.Ptr(orderID),
					CreatedAt:   pointer.Ptr(now.Format(time.RFC3339)),
					OrderNumber: pointer.Ptr("ORD-123"),
					Amount:      pointer.Ptr(100.0),
					TotalAmount: pointer.Ptr(90.0),
					ShippingAddress: &dtos.Address{
						FullName:   pointer.Ptr("John Doe"),
						Address1:   pointer.Ptr("123 Main St"),
						City:       pointer.Ptr("New York"),
						State:      pointer.Ptr("NY"),
						Country:    pointer.Ptr("USA"),
						PostalCode: pointer.Ptr("10001"),
					},
					Buyer: &dtos.User{
						Id:          pointer.Ptr("buyer-123"),
						AccountId:   pointer.Ptr("buyer_account"),
						Nickname:    pointer.Ptr("nickname"),
						FirstName:   pointer.Ptr("John"),
						LastName:    pointer.Ptr("Doe"),
						CountryCode: pointer.Ptr("US"),
					},
					IsUserBlockedBySeller: pointer.Ptr(false),
				},
			},
			expectedError: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockUserDomain := usermock.NewUserDomain(t)
			mockOrderDomain := ordermock.NewOrderDomain(t)
			mockValidator := validator.NewValidator(t)

			tc.setupMock(mockUserDomain, mockOrderDomain, mockValidator)

			s := svc{
				userDomain:  mockUserDomain,
				orderDomain: mockOrderDomain,
				v:           mockValidator,
			}

			// Act
			resp, err := s.GetSoldItemById(tc.ctx, tc.orderDetailId)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				if errors.Is(tc.expectedError, apiutil.ErrInvalidRequest) ||
					errors.Is(tc.expectedError, apiutil.ErrForbidden) ||
					errors.Is(tc.expectedError, apiutil.ErrResourceNotFound) {
					assert.True(t, errors.Is(err, tc.expectedError))
				} else {
					assert.Contains(t, err.Error(), tc.expectedError.Error())
				}
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tc.expectedResp.Id, resp.Id)
				assert.Equal(t, tc.expectedResp.OrderId, resp.OrderId)
				assert.Equal(t, tc.expectedResp.ProductId, resp.ProductId)
				assert.Equal(t, tc.expectedResp.TransactionStatus, resp.TransactionStatus)
				assert.Equal(t, tc.expectedResp.Amount, resp.Amount)
				if tc.expectedResp.Product != nil && resp.Product != nil {
					assert.Equal(t, tc.expectedResp.Product.Id, resp.Product.Id)
					assert.Equal(t, tc.expectedResp.Product.Name, resp.Product.Name)
				}
			}

			// Verify
			mockUserDomain.AssertExpectations(t)
			mockOrderDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestService_UpdateSoldItemStatus(t *testing.T) {
	// Define test constants
	userID := "user-seller-123"
	sellerID := "seller-id-123"
	orderDetailID := "order-detail-123"

	testCases := []struct {
		name          string
		ctx           context.Context
		orderDetailId string
		request       *dtos.UpdateSoldItemStatusRequest
		setupMock     func(*usermock.UserDomain, *ordermock.OrderDomain, *validator.Validator)
		expectedError error
	}{
		{
			name:          "Success - Update to preparing for shipment",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "preparingForShipment",
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       &userID,
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(&order.OrderDetail{
					ID:                &orderDetailID,
					TransactionStatus: "unshipped",
					Product: &order.Product{
						SellerId: sellerID,
					},
				}, nil)
				mockOrder.On("UpdateOrderDetailStatus", mock.Anything, orderDetailID, order.TransactionStatus("preparingForShipment"), (*string)(nil)).Return(nil)
			},
			expectedError: nil,
		},
		{
			name:          "Success - Update to shipped with waybill",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "shipped",
				WaybillNumber:     &[]string{"JP123456789"}[0],
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       &userID,
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(&order.OrderDetail{
					ID:                &orderDetailID,
					TransactionStatus: "preparingForShipment",
					Product: &order.Product{
						SellerId: sellerID,
					},
				}, nil)
				mockOrder.On("UpdateOrderDetailStatus", mock.Anything, orderDetailID, order.TransactionStatus("shipped"), &[]string{"JP123456789"}[0]).Return(nil)
			},
			expectedError: nil,
		},
		{
			name:          "Error - Invalid order detail ID format",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: "invalid-uuid",
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "shipped",
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", "invalid-uuid", "required,uuid").Return(errors.New("invalid UUID"))
			},
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name:          "Error - User not found",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "shipped",
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(nil, errors.New("user not found"))
			},
			expectedError: errors.New("get user"),
		},
		{
			name:          "Error - User is not a seller",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "shipped",
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       &userID,
					SellerID: nil, // Not a seller
				}, nil)
			},
			expectedError: apiutil.ErrForbidden,
		},
		{
			name:          "Error - Order detail not found",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "shipped",
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       &userID,
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(nil, nil)
			},
			expectedError: apiutil.ErrResourceNotFound,
		},
		{
			name:          "Error - Invalid status transition",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "received", // Invalid: unshipped -> received (skipping steps)
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       &userID,
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(&order.OrderDetail{
					ID:                &orderDetailID,
					TransactionStatus: "unshipped",
					Product: &order.Product{
						SellerId: sellerID,
					},
				}, nil)
			},
			expectedError: apiutil.ErrInvalidRequest,
		},
		{
			name:          "Error - Missing waybill for shipped status",
			ctx:           context.WithValue(context.Background(), context.ClaimsCtx, jwt.CustomClaims{UID: userID}),
			orderDetailId: orderDetailID,
			request: &dtos.UpdateSoldItemStatusRequest{
				TransactionStatus: "shipped",
				WaybillNumber:     nil, // Missing waybill
			},
			setupMock: func(mockUser *usermock.UserDomain, mockOrder *ordermock.OrderDomain, mockValidator *validator.Validator) {
				mockValidator.On("ValidateVar", orderDetailID, "required,uuid").Return(nil)
				mockValidator.On("Validate", mock.AnythingOfType("*dtos.UpdateSoldItemStatusRequest")).Return(nil)
				mockUser.On("ReadOne", mock.Anything, userID).Return(&user.User{
					ID:       &userID,
					SellerID: &sellerID,
				}, nil)
				mockOrder.On("GetOrderDetailById", mock.Anything, orderDetailID).Return(&order.OrderDetail{
					ID:                &orderDetailID,
					TransactionStatus: "preparingForShipment",
					Product: &order.Product{
						SellerId: sellerID,
					},
				}, nil)
			},
			expectedError: apiutil.ErrInvalidRequest,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockUserDomain := usermock.NewUserDomain(t)
			mockOrderDomain := ordermock.NewOrderDomain(t)
			mockValidator := validator.NewValidator(t)

			tc.setupMock(mockUserDomain, mockOrderDomain, mockValidator)

			s := svc{
				userDomain:  mockUserDomain,
				orderDomain: mockOrderDomain,
				v:           mockValidator,
			}

			// Act
			err := s.UpdateSoldItemStatus(tc.ctx, tc.orderDetailId, tc.request)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				if errors.Is(tc.expectedError, apiutil.ErrInvalidRequest) ||
					errors.Is(tc.expectedError, apiutil.ErrForbidden) ||
					errors.Is(tc.expectedError, apiutil.ErrResourceNotFound) {
					assert.True(t, errors.Is(err, tc.expectedError))
				} else {
					assert.Contains(t, err.Error(), tc.expectedError.Error())
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify
			mockUserDomain.AssertExpectations(t)
			mockOrderDomain.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}

func TestIsValidStatusTransition(t *testing.T) {
	testCases := []struct {
		name     string
		current  order.TransactionStatus
		new      order.TransactionStatus
		expected bool
	}{
		// Same status (allowed)
		{
			name:     "Same status - unshipped to unshipped",
			current:  order.TransactionStatusUnshipped,
			new:      order.TransactionStatusUnshipped,
			expected: true,
		},
		{
			name:     "Same status - shipped to shipped",
			current:  order.TransactionStatusShipped,
			new:      order.TransactionStatusShipped,
			expected: true,
		},
		// Valid forward transitions
		{
			name:     "Valid - unshipped to preparingForShipment",
			current:  order.TransactionStatusUnshipped,
			new:      order.TransactionStatusPreparingForShipment,
			expected: true,
		},
		{
			name:     "Valid - preparingForShipment to shipped",
			current:  order.TransactionStatusPreparingForShipment,
			new:      order.TransactionStatusShipped,
			expected: true,
		},
		{
			name:     "Valid - shipped to received",
			current:  order.TransactionStatusShipped,
			new:      order.TransactionStatusReceived,
			expected: true,
		},
		// Invalid transitions (skipping steps)
		{
			name:     "Invalid - unshipped to shipped (skip preparingForShipment)",
			current:  order.TransactionStatusUnshipped,
			new:      order.TransactionStatusShipped,
			expected: false,
		},
		{
			name:     "Invalid - unshipped to received (skip multiple steps)",
			current:  order.TransactionStatusUnshipped,
			new:      order.TransactionStatusReceived,
			expected: false,
		},
		{
			name:     "Invalid - preparingForShipment to received (skip shipped)",
			current:  order.TransactionStatusPreparingForShipment,
			new:      order.TransactionStatusReceived,
			expected: false,
		},
		// Invalid backward transitions
		{
			name:     "Invalid - preparingForShipment to unshipped (backward)",
			current:  order.TransactionStatusPreparingForShipment,
			new:      order.TransactionStatusUnshipped,
			expected: false,
		},
		{
			name:     "Invalid - shipped to preparingForShipment (backward)",
			current:  order.TransactionStatusShipped,
			new:      order.TransactionStatusPreparingForShipment,
			expected: false,
		},
		{
			name:     "Invalid - shipped to unshipped (backward)",
			current:  order.TransactionStatusShipped,
			new:      order.TransactionStatusUnshipped,
			expected: false,
		},
		{
			name:     "Invalid - received to shipped (backward)",
			current:  order.TransactionStatusReceived,
			new:      order.TransactionStatusShipped,
			expected: false,
		},
		{
			name:     "Invalid - received to preparingForShipment (backward)",
			current:  order.TransactionStatusReceived,
			new:      order.TransactionStatusPreparingForShipment,
			expected: false,
		},
		{
			name:     "Invalid - received to unshipped (backward)",
			current:  order.TransactionStatusReceived,
			new:      order.TransactionStatusUnshipped,
			expected: false,
		},
		// Terminal state (received cannot transition to anything except itself)
		{
			name:     "Invalid - received is terminal state",
			current:  order.TransactionStatusReceived,
			new:      order.TransactionStatusReceived,
			expected: true, // Same status allowed
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isValidStatusTransition(tc.current, tc.new)
			assert.Equal(t, tc.expected, result, "Expected %v for transition from %s to %s", tc.expected, tc.current, tc.new)
		})
	}
}
