package service

import (
	stdcontext "context"
	"time"

	"as-api/as/cache"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/logger"
	"as-api/as/internal/favorite"
	"as-api/as/internal/order"
	"as-api/as/internal/product"
	productreviews "as-api/as/internal/product-review"
	sellerdashboard "as-api/as/internal/seller-dashboard"
	"as-api/as/internal/user"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pagination"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
)

// isValidStatusTransition validates if the status transition is allowed
// Flow: unshipped -> preparingForShipment -> shipped -> received
func isValidStatusTransition(current, new order.TransactionStatus) bool {
	// Allow same status (no change)
	if current == new {
		return true
	}

	// Define valid transitions
	validTransitions := map[order.TransactionStatus][]order.TransactionStatus{
		order.TransactionStatusUnshipped:            {order.TransactionStatusPreparingForShipment},
		order.TransactionStatusPreparingForShipment: {order.TransactionStatusShipped},
		order.TransactionStatusShipped:              {order.TransactionStatusReceived},
		order.TransactionStatusReceived:             {}, // Terminal state
	}

	allowedNext, exists := validTransitions[current]
	if !exists {
		return false
	}

	for _, allowed := range allowedNext {
		if new == allowed {
			return true
		}
	}

	return false
}

type svc struct {
	productDomain         product.ProductDomain
	productReviews        productreviews.ProductReviewsDomain
	userDomain            user.UserDomain
	orderDomain           order.OrderDomain
	favoriteDomain        favorite.FavoriteDomain
	sellerDashboardDomain sellerdashboard.SellerDashboardDomain

	cacheSvc cache.CacheService
	v        validator.Validator
	logger   logger.Logger
}

func (s svc) CheckOrderDetailStatusSeller(ctx stdcontext.Context) (*dtos.GetStatusOrderDetailResponse, error) {
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return nil, errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	isProgressStatus, err := s.orderDomain.CheckOrderDetailStatusSeller(ctx, pointer.Safe(userDomain.SellerID))
	if err != nil {
		return nil, errors.Wrap(err, "orderDomain.CheckOrderDetailStatusSeller")
	}

	return &dtos.GetStatusOrderDetailResponse{
		Result: pointer.Ptr(isProgressStatus),
	}, nil
}

func (s svc) GetFollowingUsers(ctx context.Context, params dtos.GetFollowingUsersParams) (*dtos.FollowingUserResponse, error) {
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return nil, errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	followingUsersDomain, err := s.favoriteDomain.GetFollowingUsers(ctx, &favorite.GetFollowingUsersParams{
		SellerID: pointer.Safe(userDomain.SellerID),
		Limit:    params.Limit,
		Page:     params.Page,
	})
	if err != nil {
		return nil, errors.Wrap(err, "favoriteDomain.GetFollowingUsers")
	}

	followingUsersDto := make([]dtos.FollowingUser, 0, len(followingUsersDomain))
	for _, followingUserDomain := range followingUsersDomain {
		followingUsersDto = append(followingUsersDto, pointer.Safe(MapFavoriteSellerToFollowingUser(followingUserDomain)))
	}

	return &dtos.FollowingUserResponse{
		Data: pointer.Ptr(followingUsersDto),
	}, nil
}

func (s svc) GetSoldItems(ctx context.Context, params dtos.GetSoldItemsParams) (*dtos.PurchasedItemsResponse, error) {
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return nil, errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	var from *time.Time
	if fromStr := pointer.Safe(params.From); fromStr != "" {
		fromParsed, err := time.Parse(time.RFC3339, fromStr)
		if err != nil {
			return nil, errors.Wrap(err, "parse from")
		}
		from = &fromParsed
	}

	var to *time.Time
	if toStr := pointer.Safe(params.To); toStr != "" {
		toParsed, err := time.Parse(time.RFC3339, toStr)
		if err != nil {
			return nil, errors.Wrap(err, "parse to")
		}
		to = &toParsed
	}

	orderItemsDomain, err := s.orderDomain.GetOrderDetails(ctx, order.GetOrderDetailsParam{
		OrderBy:  pointer.PtrString[string](params.OrderBy),
		Page:     params.Page,
		Limit:    params.Limit,
		From:     from,
		To:       to,
		SellerId: pointer.Safe(userDomain.SellerID),
	})
	if err != nil {
		return nil, errors.Wrap(err, "orderDomain.GetOrderDetails")
	}

	orderItems := make([]dtos.PurchasedItem, 0, len(orderItemsDomain))
	for _, orderItemDomain := range orderItemsDomain {
		orderItems = append(orderItems, pointer.Safe(ConvertOrderDetailDomainToDTO(orderItemDomain)))
	}

	return &dtos.PurchasedItemsResponse{
		Items: &orderItems,
	}, nil

}

func (s svc) GetSoldItemById(ctx context.Context, orderDetailId string) (*dtos.PurchasedItem, error) {
	// Validate order detail ID
	if err := s.v.ValidateVar(orderDetailId, "required,uuid"); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate order detail ID: %v", err)
	}

	// Get the current user to check permissions
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	// Check if the user is a seller
	if userDomain.SellerID == nil {
		return nil, errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	// Get the order detail by ID
	orderDetailDomain, err := s.orderDomain.GetOrderDetailById(ctx, orderDetailId)
	if err != nil {
		return nil, errors.Wrap(err, "orderDomain.GetOrderDetailById")
	}

	if orderDetailDomain == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "order detail not found")
	}

	// Check if this order detail belongs to the current seller
	// We need to verify that the product in this order detail belongs to the current seller
	if orderDetailDomain.Product == nil || orderDetailDomain.Product.SellerId != pointer.Safe(userDomain.SellerID) {
		return nil, errors.Wrap(apiutil.ErrForbidden, "order detail does not belong to this seller")
	}

	// Check if seller has blocked the buyer (if order and user information exists)
	var isUserBlocked *bool
	if orderDetailDomain.Order != nil && orderDetailDomain.Order.User != nil && orderDetailDomain.Order.User.ID != nil && userDomain.SellerID != nil {
		blocked, err := s.userDomain.IsUserBlockedBySeller(ctx, *userDomain.SellerID, *orderDetailDomain.Order.User.ID)
		if err != nil {
			s.logger.Error("error when check user blocked by seller", logger.FieldMap{
				"error": err,
			})
		}

		isUserBlocked = blocked
	}

	// Convert domain to DTO with block status
	purchasedItem := convertOrderDetailDomainToDTOWithBlockStatus(orderDetailDomain, isUserBlocked)

	return purchasedItem, nil
}

func (s svc) UpdateSoldItemStatus(ctx context.Context, orderDetailId string, request *dtos.UpdateSoldItemStatusRequest) error {
	// Validate order detail ID
	if err := s.v.ValidateVar(orderDetailId, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate order detail ID: %v", err)
	}

	// Validate request
	if err := s.v.Validate(request); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	// Get the current user to check permissions
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	// Check if the user is a seller
	if userDomain.SellerID == nil {
		return errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	// Get the order detail by ID
	orderDetailDomain, err := s.orderDomain.GetOrderDetailById(ctx, orderDetailId)
	if err != nil {
		return errors.Wrap(err, "orderDomain.GetOrderDetailById")
	}

	if orderDetailDomain == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "order detail not found")
	}

	// Check if this order detail belongs to the current seller
	if orderDetailDomain.Product == nil || orderDetailDomain.Product.SellerId != pointer.Safe(userDomain.SellerID) {
		return errors.Wrap(apiutil.ErrForbidden, "order detail does not belong to this seller")
	}

	// Validate status transition
	currentStatus := orderDetailDomain.TransactionStatus
	newStatus := order.TransactionStatus(request.TransactionStatus)

	if !isValidStatusTransition(currentStatus, newStatus) {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "invalid status transition from %s to %s", currentStatus, newStatus)
	}

	// Validate waybill requirement for shipped and received status
	if (newStatus == order.TransactionStatusShipped || newStatus == order.TransactionStatusReceived) &&
		(request.WaybillNumber == nil || *request.WaybillNumber == "") {
		return errors.Wrap(apiutil.ErrInvalidRequest, "waybill number is required for shipped and received status")
	}

	// Update the order detail status
	err = s.orderDomain.UpdateOrderDetailStatus(ctx, orderDetailId, newStatus, request.WaybillNumber)
	if err != nil {
		return errors.Wrap(err, "orderDomain.UpdateOrderDetailStatus")
	}

	return nil
}

func (s svc) UpdatePublishSeller(ctx context.Context, request dtos.UpdatePublishSellerRequest) error {
	if err := s.v.Validate(request); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	sellerDomain, err := s.userDomain.ReadSellerByID(ctx, pointer.Safe(userDomain.SellerID))
	if err != nil {
		return errors.Wrap(err, "userDomain.ReadSellerByID")
	}

	sellerDomain.PublicStatus = pointer.PtrString[user.PublishStatus](request.PublicStatus)

	if _, err := s.userDomain.UpdateSeller(ctx, userId, sellerDomain); err != nil {
		return errors.Wrap(err, "userDomain.UpdateSeller")
	}

	return nil
}

func (s svc) DeleteProduct(ctx context.Context, productId string) error {
	// Validate ID
	if err := s.v.ValidateVar(productId, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	productDomain, err := s.productDomain.GetProductsById(ctx, productId)
	if err != nil {
		return errors.Wrap(err, "get product by id")
	}

	if len(productDomain) == 0 || productDomain[0] == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "product not found")
	}

	if pointer.Safe(productDomain[0].SellerID) != pointer.Safe(userDomain.SellerID) {
		return errors.Wrap(apiutil.ErrForbidden, "product is not belong to seller")
	}

	// Check if product can be deleted - only draft or available (onSale) products can be deleted
	productStatus := pointer.Safe(productDomain[0].SalesStatus)
	if productStatus != product.SalesStatusDraft && productStatus != product.SalesStatusAvailable {
		return errors.Wrap(apiutil.ErrResourcePreconditionFailed, "product can only be deleted when status is draft or available")
	}

	// Delete the product - related favorites and cart items will be automatically deleted
	// by the database foreign key constraints with ON DELETE CASCADE
	if err := s.productDomain.DeleteProduct(ctx, productId); err != nil {
		return errors.Wrap(err, "delete product")
	}

	return nil
}

func (s svc) DeleteBlockUser(ctx context.Context, blockUserId string) error {
	// Validate ID
	if err := s.v.ValidateVar(blockUserId, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	if err := s.userDomain.DeleteBlockUser(ctx, blockUserId); err != nil {
		return errors.Wrap(err, "delete block user")
	}

	return nil
}

func (s svc) BlockUser(ctx context.Context, request *dtos.BlockUserRequest) error {
	if err := s.v.Validate(request); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	if err := s.userDomain.BlockUser(ctx, user.SellerBlockUser{
		UserId:   request.UserId,
		SellerId: pointer.Safe(userDomain.SellerID),
	}); err != nil {
		return errors.Wrap(err, "block user")
	}

	return nil
}

func (s svc) CreateSellerProduct(ctx context.Context, request *dtos.ProductRequest) error {
	if err := s.v.Validate(request); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "user is not seller")
	}

	productDomain := convertProductRequestToProduct(request)
	productDomain.SellerID = userDomain.SellerID
	productDomain.ShipFrom = userDomain.CountryCode

	if err := s.productDomain.CreateProduct(ctx, productDomain); err != nil {
		return errors.Wrap(err, "productDomain.CreateProduct")
	}

	return nil
}

func (s svc) UpdateSellerProductById(ctx context.Context, productId string, request *dtos.ProductUpdateRequest) error {
	// Validate ID
	if err := s.v.ValidateVar(productId, "required,uuid"); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	if err := s.v.Validate(request); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	userId, err := context.GetUserID(ctx)
	if err != nil {
		return errors.Wrap(err, "get user id")
	}

	user, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return errors.Wrap(err, "get user")
	}

	if user.SellerID == nil {
		return errors.Wrap(apiutil.ErrPermissionDenied, "user is not seller")
	}

	products, err := s.productDomain.GetProductsById(ctx, productId)
	if err != nil {
		return errors.Wrap(err, "get products")
	}

	if len(products) == 0 {
		return errors.Wrap(apiutil.ErrResourceNotFound, "get products")
	}

	if pointer.Safe(user.SellerID) != pointer.Safe(products[0].SellerID) {
		return errors.Wrap(apiutil.ErrPermissionDenied, "product not belong seller")
	}

	if err := s.productDomain.UpdateProduct(ctx, productId, convertUpdateRequestToProduct(request)); err != nil {
		return errors.Wrap(err, "UpdateSellerProductById")
	}

	return nil
}

func (s svc) GetSellerById(ctx context.Context, sellerId string) (*dtos.Seller, error) {
	seller, err := s.userDomain.ReadSellerByID(ctx, sellerId)
	if err != nil {
		return nil, errors.Wrap(err, "Error when Get Seller By Id")
	}

	dtoSeller := convertDomainSellerToDto(seller)

	// Get favorite count for the seller
	counts, err := s.favoriteDomain.GetFavoriteSellersCount(ctx, sellerId)
	if err != nil {
		return nil, errors.Wrap(err, "favoriteDomain.GetFavoriteSellersCount")
	}
	dtoSeller.NumFavorites = pointer.Ptr(counts[sellerId])

	uid, _ := context.GetUserID(ctx)
	if uid != "" {
		userFavoriteSellerIds, err := s.favoriteDomain.GetUserFavoriteSellersBySellerIDs(ctx, uid, sellerId)
		if err != nil {
			return nil, errors.Wrap(err, "favoriteDomain.GetUserFavoriteSellersBySellerIDs")
		}

		dtoSeller.IsFavorite = pointer.Ptr(len(userFavoriteSellerIds) > 0)
	}

	return dtoSeller, nil
}

func (s *svc) GetSellerProducts(ctx context.Context, sellerId string, params dtos.GetSellerProductsParams) (*dtos.SellerProductsResponse, error) {
	// Validate ID
	if err := s.v.ValidateVar(sellerId, "required,uuid"); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	page, limit := pagination.Paginate(pointer.Safe(params.Page), pointer.Safe(params.Limit))

	products, err := s.productDomain.GetProductsBySellerId(ctx, sellerId, pointer.SafeString[string](params.Sort), page, limit)
	if err != nil {
		return nil, errors.Wrap(err, "productDomain.GetProductsBySellerId")

	}

	if len(products) == 0 {
		return nil, nil
	}

	// Get product IDs for favorite count lookup
	productIDs := make([]string, 0, len(products))
	for _, p := range products {
		if p.ID != nil {
			productIDs = append(productIDs, *p.ID)
		}
	}

	// Get favorite counts for products
	counts, err := s.favoriteDomain.GetFavoriteProductsCount(ctx, productIDs...)
	if err != nil {
		return nil, errors.Wrap(err, "favoriteDomain.GetFavoriteProductsCount")
	}

	// Map domain products to DTO products
	dtoProducts := make([]dtos.Product, 0, len(products))
	for _, p := range products {
		dtoProduct := convertDomainProductToDto(p)
		// Set favorite count
		if p.ID != nil {
			dtoProduct.NumFavorites = pointer.Ptr(counts[*p.ID])
		}
		dtoProducts = append(dtoProducts, pointer.Safe(dtoProduct))
	}

	return &dtos.SellerProductsResponse{
		Products: &dtoProducts,
	}, nil
}

func (s svc) GetSellerRatings(ctx context.Context, sellerId string) (*dtos.RatingSellerResponse, error) {
	// Validate ID
	if err := s.v.ValidateVar(sellerId, "required,uuid"); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	rating, err := s.productReviews.GetRatingBySellerId(ctx, sellerId)
	if err != nil {
		return nil, errors.Wrap(err, "productReviews.GetRatingBySellerId")
	}

	return &dtos.RatingSellerResponse{
		AvgRating:       rating.AvgRating,
		AvgSpeedRating:  rating.AvgSpeedRating,
		AvgPoliteRating: rating.AvgPoliteRating,
		AvgPackRating:   rating.AvgPackRating,
		TotalOrders:     rating.TotalOrders,
		AvgDaysToShip:   rating.AvgDaysToShip,
	}, nil
}

func (s svc) GetBlockUsers(ctx context.Context, params dtos.GetBlockUsersParams) (*dtos.BlockUsersResponse, error) {
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return nil, errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	sellerBlockUsersDomain, err := s.userDomain.GetListBlockUsers(ctx, &user.GetListBlockUsersRequest{
		SellerId: pointer.Safe(userDomain.SellerID),
		Page:     params.Page,
		Limit:    params.Limit,
	})
	if err != nil {
		return nil, errors.Wrap(err, "userDomain.GetListBlockUsers")
	}

	sellerBlockUserResponse := make([]dtos.BlockUser, 0, len(sellerBlockUsersDomain))
	for _, sellerBlockUserDomain := range sellerBlockUsersDomain {
		sellerBlockUserResponse = append(sellerBlockUserResponse, pointer.Safe(convertSellerBlockUserToDTO(sellerBlockUserDomain)))
	}

	return &dtos.BlockUsersResponse{
		Data: pointer.Ptr(sellerBlockUserResponse),
	}, nil
}

func (s svc) GetSellerDashboard(ctx stdcontext.Context) (*dtos.SellerDashboardResponse, error) {
	userId, err := context.GetUserID(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get user id")
	}

	userDomain, err := s.userDomain.ReadOne(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	if userDomain.SellerID == nil {
		return nil, errors.Wrap(apiutil.ErrForbidden, "user is not seller")
	}

	result, err := s.sellerDashboardDomain.GetSellerDashboard(ctx, *userDomain.SellerID)
	if err != nil {
		return nil, errors.Wrap(err, "get seller dashboard")
	}

	if result == nil {
		return nil, nil
	}

	return &dtos.SellerDashboardResponse{
		Yesterday: &dtos.ShopResult{
			Views:                  pointer.Ptr(result.Views),
			ProductFavorites:       pointer.Ptr(result.ProductFavorites),
			ShopFavorites:          pointer.Ptr(result.ShopFavorites),
			OrderAmountTaxExcluded: pointer.Ptr(result.OrderAmount),
			OrderAmountTaxIncluded: pointer.Ptr(result.TotalOrderAmount),
			SalesAmount:            pointer.Ptr(result.TotalSalesAmount),
		},
	}, nil
}
