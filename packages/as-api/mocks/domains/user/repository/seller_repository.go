// Code generated by mockery v2.43.0. DO NOT EDIT.

package repository

import (
	entities "as-api/as/foundations/db/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// SellerRepository is an autogenerated mock type for the SellerRepository type
type SellerRepository struct {
	mock.Mock
}

// BlockUser provides a mock function with given fields: ctx, blockUser
func (_m *SellerRepository) BlockUser(ctx context.Context, blockUser *entities.SellerBlockUser) error {
	ret := _m.Called(ctx, blockUser)

	if len(ret) == 0 {
		panic("no return value specified for BlockUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.SellerBlockUser) error); ok {
		r0 = rf(ctx, blockUser)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Create provides a mock function with given fields: ctx, seller
func (_m *SellerRepository) Create(ctx context.Context, seller *entities.Seller) (*entities.Seller, error) {
	ret := _m.Called(ctx, seller)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *entities.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Seller) (*entities.Seller, error)); ok {
		return rf(ctx, seller)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entities.Seller) *entities.Seller); ok {
		r0 = rf(ctx, seller)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entities.Seller) error); ok {
		r1 = rf(ctx, seller)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteBlockUser provides a mock function with given fields: ctx, blockUserId
func (_m *SellerRepository) DeleteBlockUser(ctx context.Context, blockUserId string) error {
	ret := _m.Called(ctx, blockUserId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteBlockUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, blockUserId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// First provides a mock function with given fields: ctx, id
func (_m *SellerRepository) First(ctx context.Context, id string) (*entities.Seller, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for First")
	}

	var r0 *entities.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entities.Seller, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entities.Seller); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllSellers provides a mock function with given fields: ctx
func (_m *SellerRepository) GetAllSellers(ctx context.Context) ([]*entities.Seller, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllSellers")
	}

	var r0 []*entities.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*entities.Seller, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*entities.Seller); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entities.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetListBlockUsers provides a mock function with given fields: ctx, filter, page, limit
func (_m *SellerRepository) GetListBlockUsers(ctx context.Context, filter map[string]interface{}, page int, limit int) ([]*entities.SellerBlockUser, error) {
	ret := _m.Called(ctx, filter, page, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetListBlockUsers")
	}

	var r0 []*entities.SellerBlockUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}, int, int) ([]*entities.SellerBlockUser, error)); ok {
		return rf(ctx, filter, page, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}, int, int) []*entities.SellerBlockUser); ok {
		r0 = rf(ctx, filter, page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entities.SellerBlockUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[string]interface{}, int, int) error); ok {
		r1 = rf(ctx, filter, page, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSellersByIDs provides a mock function with given fields: ctx, ids
func (_m *SellerRepository) GetSellersByIDs(ctx context.Context, ids ...string) ([]*entities.Seller, error) {
	_va := make([]interface{}, len(ids))
	for _i := range ids {
		_va[_i] = ids[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetSellersByIDs")
	}

	var r0 []*entities.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...string) ([]*entities.Seller, error)); ok {
		return rf(ctx, ids...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...string) []*entities.Seller); ok {
		r0 = rf(ctx, ids...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entities.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...string) error); ok {
		r1 = rf(ctx, ids...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsUserBlockedBySeller provides a mock function with given fields: ctx, sellerID, userID
func (_m *SellerRepository) IsUserBlockedBySeller(ctx context.Context, sellerID string, userID string) (*bool, error) {
	ret := _m.Called(ctx, sellerID, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsUserBlockedBySeller")
	}

	var r0 *bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*bool, error)); ok {
		return rf(ctx, sellerID, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *bool); ok {
		r0 = rf(ctx, sellerID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, sellerID, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, id, seller
func (_m *SellerRepository) Update(ctx context.Context, id string, seller *entities.Seller) (*entities.Seller, error) {
	ret := _m.Called(ctx, id, seller)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 *entities.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *entities.Seller) (*entities.Seller, error)); ok {
		return rf(ctx, id, seller)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *entities.Seller) *entities.Seller); ok {
		r0 = rf(ctx, id, seller)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *entities.Seller) error); ok {
		r1 = rf(ctx, id, seller)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewSellerRepository creates a new instance of SellerRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSellerRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *SellerRepository {
	mock := &SellerRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
