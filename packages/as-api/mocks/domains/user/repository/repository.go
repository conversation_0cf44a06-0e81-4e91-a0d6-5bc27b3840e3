// Code generated by mockery v2.43.0. DO NOT EDIT.

package repository

import (
	entities "as-api/as/foundations/db/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

// CancelAccount provides a mock function with given fields: ctx, id, user
func (_m *Repository) CancelAccount(ctx context.Context, id string, user *entities.User) error {
	ret := _m.Called(ctx, id, user)

	if len(ret) == 0 {
		panic("no return value specified for CancelAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *entities.User) error); ok {
		r0 = rf(ctx, id, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Create provides a mock function with given fields: ctx, user
func (_m *Repository) Create(ctx context.Context, user *entities.User) (*entities.User, error) {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.User) (*entities.User, error)); ok {
		return rf(ctx, user)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entities.User) *entities.User); ok {
		r0 = rf(ctx, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entities.User) error); ok {
		r1 = rf(ctx, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByEmail provides a mock function with given fields: ctx, email
func (_m *Repository) FindByEmail(ctx context.Context, email string) (*entities.User, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for FindByEmail")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entities.User, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entities.User); ok {
		r0 = rf(ctx, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindBySellerID provides a mock function with given fields: ctx, sellerID
func (_m *Repository) FindBySellerID(ctx context.Context, sellerID string) (*entities.User, error) {
	ret := _m.Called(ctx, sellerID)

	if len(ret) == 0 {
		panic("no return value specified for FindBySellerID")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entities.User, error)); ok {
		return rf(ctx, sellerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entities.User); ok {
		r0 = rf(ctx, sellerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, sellerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// First provides a mock function with given fields: ctx, id
func (_m *Repository) First(ctx context.Context, id string) (*entities.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for First")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entities.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entities.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SearchUsers provides a mock function with given fields: ctx, filter, page, limit
func (_m *Repository) SearchUsers(ctx context.Context, filter map[string]interface{}, page int, limit int) ([]*entities.User, int, error) {
	ret := _m.Called(ctx, filter, page, limit)

	if len(ret) == 0 {
		panic("no return value specified for SearchUsers")
	}

	var r0 []*entities.User
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}, int, int) ([]*entities.User, int, error)); ok {
		return rf(ctx, filter, page, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[string]interface{}, int, int) []*entities.User); ok {
		r0 = rf(ctx, filter, page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[string]interface{}, int, int) int); ok {
		r1 = rf(ctx, filter, page, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, map[string]interface{}, int, int) error); ok {
		r2 = rf(ctx, filter, page, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Update provides a mock function with given fields: ctx, id, user
func (_m *Repository) Update(ctx context.Context, id string, user *entities.User) (*entities.User, error) {
	ret := _m.Called(ctx, id, user)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 *entities.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *entities.User) (*entities.User, error)); ok {
		return rf(ctx, id, user)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *entities.User) *entities.User); ok {
		r0 = rf(ctx, id, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *entities.User) error); ok {
		r1 = rf(ctx, id, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
