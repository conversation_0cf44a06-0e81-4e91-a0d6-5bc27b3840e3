// Code generated by mockery v2.43.0. DO NOT EDIT.

package user

import (
	internaluser "as-api/as/internal/user"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// UserDomain is an autogenerated mock type for the UserDomain type
type UserDomain struct {
	mock.Mock
}

// BlockUser provides a mock function with given fields: ctx, blockUser
func (_m *UserDomain) BlockUser(ctx context.Context, blockUser internaluser.SellerBlockUser) error {
	ret := _m.Called(ctx, blockUser)

	if len(ret) == 0 {
		panic("no return value specified for BlockUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, internaluser.SellerBlockUser) error); ok {
		r0 = rf(ctx, blockUser)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CancelAccount provides a mock function with given fields: ctx, id, user
func (_m *UserDomain) CancelAccount(ctx context.Context, id string, user *internaluser.User) error {
	ret := _m.Called(ctx, id, user)

	if len(ret) == 0 {
		panic("no return value specified for CancelAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.User) error); ok {
		r0 = rf(ctx, id, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Create provides a mock function with given fields: ctx, user
func (_m *UserDomain) Create(ctx context.Context, user *internaluser.User) (*internaluser.User, error) {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *internaluser.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.User) (*internaluser.User, error)); ok {
		return rf(ctx, user)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.User) *internaluser.User); ok {
		r0 = rf(ctx, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internaluser.User) error); ok {
		r1 = rf(ctx, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSeller provides a mock function with given fields: ctx, uid, seller
func (_m *UserDomain) CreateSeller(ctx context.Context, uid string, seller *internaluser.Seller) (*internaluser.Seller, error) {
	ret := _m.Called(ctx, uid, seller)

	if len(ret) == 0 {
		panic("no return value specified for CreateSeller")
	}

	var r0 *internaluser.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.Seller) (*internaluser.Seller, error)); ok {
		return rf(ctx, uid, seller)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.Seller) *internaluser.Seller); ok {
		r0 = rf(ctx, uid, seller)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *internaluser.Seller) error); ok {
		r1 = rf(ctx, uid, seller)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteBlockUser provides a mock function with given fields: ctx, blockUserId
func (_m *UserDomain) DeleteBlockUser(ctx context.Context, blockUserId string) error {
	ret := _m.Called(ctx, blockUserId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteBlockUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, blockUserId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByEmail provides a mock function with given fields: ctx, email
func (_m *UserDomain) FindByEmail(ctx context.Context, email string) (*internaluser.User, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for FindByEmail")
	}

	var r0 *internaluser.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internaluser.User, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internaluser.User); ok {
		r0 = rf(ctx, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllSellers provides a mock function with given fields: ctx
func (_m *UserDomain) GetAllSellers(ctx context.Context) ([]*internaluser.Seller, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllSellers")
	}

	var r0 []*internaluser.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*internaluser.Seller, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*internaluser.Seller); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internaluser.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetListBlockUsers provides a mock function with given fields: ctx, req
func (_m *UserDomain) GetListBlockUsers(ctx context.Context, req *internaluser.GetListBlockUsersRequest) ([]*internaluser.SellerBlockUser, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetListBlockUsers")
	}

	var r0 []*internaluser.SellerBlockUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.GetListBlockUsersRequest) ([]*internaluser.SellerBlockUser, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.GetListBlockUsersRequest) []*internaluser.SellerBlockUser); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internaluser.SellerBlockUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internaluser.GetListBlockUsersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetListEmails provides a mock function with given fields: ctx, param
func (_m *UserDomain) GetListEmails(ctx context.Context, param internaluser.GetListEmailsParams) ([]string, error) {
	ret := _m.Called(ctx, param)

	if len(ret) == 0 {
		panic("no return value specified for GetListEmails")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, internaluser.GetListEmailsParams) ([]string, error)); ok {
		return rf(ctx, param)
	}
	if rf, ok := ret.Get(0).(func(context.Context, internaluser.GetListEmailsParams) []string); ok {
		r0 = rf(ctx, param)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, internaluser.GetListEmailsParams) error); ok {
		r1 = rf(ctx, param)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetNotificationSettings provides a mock function with given fields: ctx, id
func (_m *UserDomain) GetNotificationSettings(ctx context.Context, id string) (*internaluser.NotificationSettings, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetNotificationSettings")
	}

	var r0 *internaluser.NotificationSettings
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internaluser.NotificationSettings, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internaluser.NotificationSettings); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.NotificationSettings)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSellersByIDs provides a mock function with given fields: ctx, ids
func (_m *UserDomain) GetSellersByIDs(ctx context.Context, ids ...string) ([]*internaluser.Seller, error) {
	_va := make([]interface{}, len(ids))
	for _i := range ids {
		_va[_i] = ids[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetSellersByIDs")
	}

	var r0 []*internaluser.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...string) ([]*internaluser.Seller, error)); ok {
		return rf(ctx, ids...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...string) []*internaluser.Seller); ok {
		r0 = rf(ctx, ids...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internaluser.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...string) error); ok {
		r1 = rf(ctx, ids...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserLanguage provides a mock function with given fields: ctx, userID
func (_m *UserDomain) GetUserLanguage(ctx context.Context, userID string) (string, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserLanguage")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsUserBlockedBySeller provides a mock function with given fields: ctx, sellerID, userID
func (_m *UserDomain) IsUserBlockedBySeller(ctx context.Context, sellerID string, userID string) (*bool, error) {
	ret := _m.Called(ctx, sellerID, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsUserBlockedBySeller")
	}

	var r0 *bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*bool, error)); ok {
		return rf(ctx, sellerID, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *bool); ok {
		r0 = rf(ctx, sellerID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, sellerID, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ReadOne provides a mock function with given fields: ctx, id
func (_m *UserDomain) ReadOne(ctx context.Context, id string) (*internaluser.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for ReadOne")
	}

	var r0 *internaluser.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internaluser.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internaluser.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ReadOneBySellerID provides a mock function with given fields: ctx, sellerID
func (_m *UserDomain) ReadOneBySellerID(ctx context.Context, sellerID string) (*internaluser.User, error) {
	ret := _m.Called(ctx, sellerID)

	if len(ret) == 0 {
		panic("no return value specified for ReadOneBySellerID")
	}

	var r0 *internaluser.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internaluser.User, error)); ok {
		return rf(ctx, sellerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internaluser.User); ok {
		r0 = rf(ctx, sellerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, sellerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ReadSellerByID provides a mock function with given fields: ctx, id
func (_m *UserDomain) ReadSellerByID(ctx context.Context, id string) (*internaluser.Seller, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for ReadSellerByID")
	}

	var r0 *internaluser.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internaluser.Seller, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internaluser.Seller); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RegisterSeller provides a mock function with given fields: ctx, seller
func (_m *UserDomain) RegisterSeller(ctx context.Context, seller *internaluser.Seller) (*internaluser.Seller, error) {
	ret := _m.Called(ctx, seller)

	if len(ret) == 0 {
		panic("no return value specified for RegisterSeller")
	}

	var r0 *internaluser.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.Seller) (*internaluser.Seller, error)); ok {
		return rf(ctx, seller)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.Seller) *internaluser.Seller); ok {
		r0 = rf(ctx, seller)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internaluser.Seller) error); ok {
		r1 = rf(ctx, seller)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SearchUsers provides a mock function with given fields: ctx, req
func (_m *UserDomain) SearchUsers(ctx context.Context, req *internaluser.UserSearchRequest) (*internaluser.UserSearchResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SearchUsers")
	}

	var r0 *internaluser.UserSearchResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.UserSearchRequest) (*internaluser.UserSearchResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internaluser.UserSearchRequest) *internaluser.UserSearchResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.UserSearchResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internaluser.UserSearchRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SettingPin provides a mock function with given fields: ctx, id, pin
func (_m *UserDomain) SettingPin(ctx context.Context, id string, pin string) error {
	ret := _m.Called(ctx, id, pin)

	if len(ret) == 0 {
		panic("no return value specified for SettingPin")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, id, pin)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, id, user
func (_m *UserDomain) Update(ctx context.Context, id string, user *internaluser.User) (*internaluser.User, error) {
	ret := _m.Called(ctx, id, user)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 *internaluser.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.User) (*internaluser.User, error)); ok {
		return rf(ctx, id, user)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.User) *internaluser.User); ok {
		r0 = rf(ctx, id, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *internaluser.User) error); ok {
		r1 = rf(ctx, id, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateMembership provides a mock function with given fields: ctx, id, membership
func (_m *UserDomain) UpdateMembership(ctx context.Context, id string, membership int32) error {
	ret := _m.Called(ctx, id, membership)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMembership")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int32) error); ok {
		r0 = rf(ctx, id, membership)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateNotificationSettings provides a mock function with given fields: ctx, id, settings
func (_m *UserDomain) UpdateNotificationSettings(ctx context.Context, id string, settings *internaluser.NotificationSettings) error {
	ret := _m.Called(ctx, id, settings)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationSettings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.NotificationSettings) error); ok {
		r0 = rf(ctx, id, settings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateSeller provides a mock function with given fields: ctx, uid, seller
func (_m *UserDomain) UpdateSeller(ctx context.Context, uid string, seller *internaluser.Seller) (*internaluser.Seller, error) {
	ret := _m.Called(ctx, uid, seller)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSeller")
	}

	var r0 *internaluser.Seller
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.Seller) (*internaluser.Seller, error)); ok {
		return rf(ctx, uid, seller)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *internaluser.Seller) *internaluser.Seller); ok {
		r0 = rf(ctx, uid, seller)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internaluser.Seller)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *internaluser.Seller) error); ok {
		r1 = rf(ctx, uid, seller)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VerifyPin provides a mock function with given fields: ctx, id, pin
func (_m *UserDomain) VerifyPin(ctx context.Context, id string, pin *string) error {
	ret := _m.Called(ctx, id, pin)

	if len(ret) == 0 {
		panic("no return value specified for VerifyPin")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *string) error); ok {
		r0 = rf(ctx, id, pin)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserDomain creates a new instance of UserDomain. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserDomain(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserDomain {
	mock := &UserDomain{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
