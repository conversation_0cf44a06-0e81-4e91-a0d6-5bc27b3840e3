package paypalapi

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/pkg/errors"
)

type PayPalConfig struct {
	BaseURL      string
	ClientID     string
	ClientSecret string
}

type PayPalClient interface {
	CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error)
	CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error)
	GetAccessToken(ctx context.Context) (*AccessTokenResponse, error)
}

type paypalClient struct {
	config      PayPalConfig
	httpClient  *http.Client
	accessToken string
	tokenExpiry time.Time
}

// Request/Response structures based on PayPal API
type CreateVaultSetupTokenRequest struct {
	PaymentSource PaymentSource `json:"payment_source"`
}

type PaymentSource struct {
	Card map[string]interface{} `json:"card"`
}

type CreateVaultSetupTokenResponse struct {
	ID            string                 `json:"id"`
	Status        string                 `json:"status"`
	PaymentSource PaymentSource          `json:"payment_source"`
	Links         []Link                 `json:"links"`
}

type CreateVaultPaymentTokenRequest struct {
	PaymentSource PaymentTokenSource `json:"payment_source"`
}

type PaymentTokenSource struct {
	Token Token `json:"token"`
}

type Token struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type CreateVaultPaymentTokenResponse struct {
	ID            string                      `json:"id"`
	Status        string                      `json:"status"`
	Customer      Customer                    `json:"customer"`
	PaymentSource PaymentTokenResponseSource  `json:"payment_source"`
	Links         []Link                      `json:"links"`
}

type Customer struct {
	ID string `json:"id"`
}

type PaymentTokenResponseSource struct {
	Card Card `json:"card"`
}

type Card struct {
	LastDigits string `json:"last_digits"`
	Brand      string `json:"brand"`
	Type       string `json:"type"`
}

type Link struct {
	Href   string `json:"href"`
	Rel    string `json:"rel"`
	Method string `json:"method"`
}

type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

func NewPayPalClient(config PayPalConfig) PayPalClient {
	return &paypalClient{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *paypalClient) GetAccessToken(ctx context.Context) (*AccessTokenResponse, error) {
	// Check if we have a valid token
	if c.accessToken != "" && time.Now().Before(c.tokenExpiry) {
		return &AccessTokenResponse{
			AccessToken: c.accessToken,
			TokenType:   "Bearer",
		}, nil
	}

	// Request new token
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.BaseURL+"/v1/oauth2/token", 
		bytes.NewBufferString("grant_type=client_credentials"))
	if err != nil {
		return nil, errors.Wrap(err, "create token request")
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(c.config.ClientID, c.config.ClientSecret)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "execute token request")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, errors.Errorf("token request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResp AccessTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, errors.Wrap(err, "decode token response")
	}

	// Cache the token
	c.accessToken = tokenResp.AccessToken
	c.tokenExpiry = time.Now().Add(time.Duration(tokenResp.ExpiresIn-60) * time.Second) // Refresh 1 minute early

	return &tokenResp, nil
}

func (c *paypalClient) CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error) {
	return c.makeVaultRequest(ctx, "POST", "/v3/vault/setup-tokens", req, &CreateVaultSetupTokenResponse{})
}

func (c *paypalClient) CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error) {
	return c.makeVaultRequest(ctx, "POST", "/v3/vault/payment-tokens", req, &CreateVaultPaymentTokenResponse{})
}

func (c *paypalClient) makeVaultRequest(ctx context.Context, method, path string, reqBody interface{}, respBody interface{}) (interface{}, error) {
	// Get access token
	tokenResp, err := c.GetAccessToken(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get access token")
	}

	// Prepare request body
	var bodyReader io.Reader
	if reqBody != nil {
		jsonBody, err := json.Marshal(reqBody)
		if err != nil {
			return nil, errors.Wrap(err, "marshal request body")
		}
		bodyReader = bytes.NewBuffer(jsonBody)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, method, c.config.BaseURL+path, bodyReader)
	if err != nil {
		return nil, errors.Wrap(err, "create request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+tokenResp.AccessToken)
	req.Header.Set("PayPal-Request-Id", strconv.FormatInt(time.Now().UnixNano(), 10))

	// Execute request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "execute request")
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "read response body")
	}

	// Check status code
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, errors.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Decode response
	if err := json.Unmarshal(body, respBody); err != nil {
		return nil, errors.Wrap(err, "decode response body")
	}

	return respBody, nil
}
