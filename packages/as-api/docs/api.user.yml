openapi: 3.1.0
info:
  title: AS API User
  version: '1.0'
  description: This is API specification for AS project.
servers:
  - url: 'localhost:3000/user/v1'
paths:
  /auth:
    post:
      operationId: auth
      summary: POST auth
      description: Auth request
      tags:
        - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/signup:
    post:
      operationId: signup
      summary: Signup
      description: Signup a new user
      tags:
        - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignupRequest'
      responses:
        '204':
          description: Send signup email successfully
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/signup/verify:
    post:
      operationId: verify-signup
      summary: Verify signup token
      description: Verify signup token
      tags:
        - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignupVerifyRequest'
      responses:
        '200':
          description: Signup token verified
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Signup token not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/reset-password/request:
    post:
      operationId: request-reset-password
      summary: Request password reset
      description: Send password reset instructions to user's email
      tags:
        - auth
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequestRequest'
      responses:
        '204':
          description: Reset password email sent successfully
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/reset-password/verify:
    post:
      operationId: verify-reset-password
      summary: Verify reset password token
      description: Verify if the reset password token is valid
      tags:
        - auth
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordVerifyRequest'
      responses:
        '204':
          description: Token is valid
        '400':
          description: Invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/reset-password/confirm:
    post:
      operationId: confirm-reset-password
      summary: Confirm new password
      description: Set new password after reset
      tags:
        - auth
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordConfirmRequest'
      responses:
        '204':
          description: Password reset successfully
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /users/register:
    post:
      summary: User Creates Users
      description: User Creates Users
      operationId: register-user
      tags:
        - user
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterUserRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /system/status:
    get:
      summary: Get system status
      operationId: get-system-status
      description: 'Check network connection, server status and app version when app starts'
      tags:
        - system
      security: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /home-feeds:
    get:
      operationId: get-home-feeds
      summary: Timeline Feed
      description: Get timeline items with pagination and filtering options
      tags:
        - home
      parameters:
        - name: countryCode
          in: query
          required: true
          schema:
            type: string
          description: Country code
        - name: target
          in: query
          required: false
          schema:
            $ref: '#/components/schemas/ProductTarget'
          description: Target
        - name: keyword
          in: query
          required: false
          schema:
            type: string
          description: Keyword
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HomeFeedsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /seller/dashboard:
    get:
      operationId: get-seller-dashboard
      summary: Seller Dashboard
      description: Retrieve and display the seller dashboard.
      tags:
        - seller
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SellerDashboardResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /brands:
    get:
      operationId: get-brands
      summary: Retrieve Brands
      description: Fetch a list of all available brands.
      tags:
        - product
      parameters:
        - name: keyword
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBrandsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /product-categories:
    get:
      operationId: get-product-categories
      summary: Retrieve Product Categories
      description: Fetch a list of all available product categories.
      tags:
        - product
      parameters:
        - name: keyword
          in: query
          required: false
          schema:
            type: string
        - name: parentId
          in: query
          required: false
          schema:
            type: string
            description: Parent category ID
        - name: sellerId
          in: query
          required: false
          schema:
            type: string
            description: Seller ID
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductCategoriesResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/notifications':
    get:
      operationId: get-user-notifications
      summary: List of User Notifications
      description: Retrieve a list of notifications registered through the admin screen and system notifications based on user behavior.
      tags:
        - notification
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the user to retrieve notifications for
          schema:
            type: string
        - name: systemType
          in: query
          required: false
          description: Filter notifications by type
          schema:
            type: string
            enum:
              - notification
              - notice
        - name: page
          in: query
          required: false
          description: Page number
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          required: false
          description: Number of notifications per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetNotificationsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/notifications/unread-count':
    get:
      operationId: count-user-unread-notifications
      tags:
        - notification
      summary: Count unread notifications
      description: Count unread notifications for the current user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the user to retrieve notifications for
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountUnreadNotificationsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/notifications/read-all':
    post:
      operationId: read-all-notifications
      tags:
        - notification
      summary: Read all notifications
      description: Read all notifications for the current user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the notification to read
          schema:
            type: string
      responses:
        '204':
          description: Notification read successfully
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Notification not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/notifications/{id}/read':
    post:
      operationId: read-notification
      tags:
        - notification
      summary: Read a notification
      description: Read a notification by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the notification to read
          schema:
            type: string
      responses:
        '204':
          description: Notification read successfully
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Notification not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/notifications/{id}':
    get:
      operationId: get-notification
      summary: Get a notification
      description: Get a notification by ID
      tags:
        - notification
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the notification to get
          schema:
            type: string
      responses:
        '200':
          description: Notification retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Notification not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /credit-cards:
    post:
      operationId: verify-credit-card
      summary: Verify Credit Card
      description: Verify credit card
      tags:
        - payment
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyCreditCardRequest'
      responses:
        '200':
          description: Successfully verified payment methods
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifyCreditCardResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Payment method not allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Unprocessable Entity (Blacklist Match)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /vault/setup-token:
    post:
      operationId: create-vault-setup-token
      summary: Create Vault Setup Token
      description: Create a PayPal vault setup token for secure payment method storage
      tags:
        - payment
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVaultSetupTokenRequest'
      responses:
        '200':
          description: Successfully created vault setup token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateVaultSetupTokenResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /vault/payment-token:
    post:
      operationId: create-vault-payment-token
      summary: Create Vault Payment Token
      description: Create a PayPal vault payment token from a setup token for future payments
      tags:
        - payment
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVaultPaymentTokenRequest'
      responses:
        '200':
          description: Successfully created vault payment token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateVaultPaymentTokenResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/payment-methods':
    put:
      operationId: update-user-payment-method
      summary: Update User Payment Method
      description: Update payment method
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePaymentMethodsRequest'
      responses:
        '200':
          description: Successfully updated payment method
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePaymentMethodsResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Payment method not allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Unprocessable Entity (Blacklist Match)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/seller':
    get:
      operationId: get-seller-profile
      summary: Get Seller Profile
      description: Get seller profile
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      responses:
        '200':
          description: Seller info
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SellerProfileResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-seller-info
      summary: Update Seller Information
      description: Update seller information
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSellerRequest'
      responses:
        '204':
          description: Successfully updated seller information
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Cannot Update Seller Information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict - Seller Information Already Updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/profile':
    get:
      operationId: get-user-profile
      summary: Get User Profile
      description: 'Retrieve detailed account information for a user, including seller profile if seller functionality is enabled'
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      responses:
        '200':
          description: Successfully retrieved user profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-user-profile
      summary: Update User Profile Information
      description: Update user profile information
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserProfileRequest'
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/seller/bank-account':
    put:
      operationId: update-seller-bank-account
      summary: Update Seller Bank Account Information
      description: 'Update seller bank account details following initial registration process, including blacklist verification'
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        '204':
          description: Successfully updated seller bank account information
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Cannot Update Bank Account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/pin':
    put:
      operationId: update-pin-code
      summary: Update Pin Code
      description: Set or update the admin PIN for additional security features
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePinRequest'
      responses:
        '204':
          description: Successfully updated admin PIN
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: setting-pin-code
      summary: Setting Pin Code
      description: Set or update the admin PIN for additional security features
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SettingPinRequest'
      responses:
        '204':
          description: Successfully set admin PIN
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/pin/check':
    post:
      operationId: check-pin-code
      summary: Check Pin Code
      description: Check the admin PIN for additional security features
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CheckPinRequest'
      responses:
        '204':
          description: Pin code is correct
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/password':
    put:
      operationId: update-password
      summary: Update Password
      description: Update the password for the user
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordRequest'
      responses:
        '204':
          description: Successfully updated password
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/email':
    put:
      operationId: update-email
      summary: Update Email
      description: Update the email for the user
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailRequest'
      responses:
        '204':
          description: Send email successfully
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/email/verify':
    post:
      operationId: verify-email
      summary: Verify Email
      description: Verify the email for the user
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyEmailRequest'
      responses:
        '204':
          description: Email verified successfully
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/notification-settings':
    get:
      operationId: get-user-notification-settings
      summary: Get User Notification Settings
      description: Retrieve the notification settings for a user.
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      responses:
        '200':
          description: Successfully retrieved notification settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationSettingsResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-user-notification-settings
      summary: Update User Notification Settings
      description: Enable or disable various notification settings for the user.
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationSettingsRequest'
      responses:
        '204':
          description: Successfully updated notification settings
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/fcm-token':
    put:
      operationId: upsert-user-fcm-token
      summary: Upsert User FCM Token
      description: Register or update FCM token and device information for push notifications
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertFCMTokenRequest'
      responses:
        '204':
          description: Successfully upserted FCM token
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-user-fcm-token
      summary: Delete User FCM Token
      description: Remove FCM token when user logs out to stop receiving push notifications
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteFCMTokenRequest'
      responses:
        '204':
          description: Successfully deleted FCM token
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: FCM token not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/cancel':
    post:
      operationId: cancel-account
      summary: Cancel Account
      description: 'Process to cancel account, considering conditions such as ongoing transactions'
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelAccountRequest'
      responses:
        '204':
          description: Account cancelled successfully
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Cannot cancel account due to ongoing transactions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/check-cancel':
    post:
      operationId: check-cancel-account
      summary: Check Account Cancellation Eligibility
      description: Validate whether an account can be cancelled without actually performing the cancellation. Checks for incomplete buy or sell orders that would prevent cancellation.
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      responses:
        '204':
          description: Account can be cancelled
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/favorite-products':
    get:
      operationId: get-favorite-products
      summary: Favorite Products List
      description: Display list of favorite products in a grid layout similar to search results
      tags:
        - favorite
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
          example: 123e4567-e89b-12d3-a456-************
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number
          example: 1
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of items per page
          example: 10
      responses:
        '200':
          description: Successfully retrieved favorite products
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFavoriteProductsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: add-favorite-product
      summary: Add Favorite Product
      description: Add a product to user's favorites list
      tags:
        - favorite
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddFavoriteProductRequest'
      responses:
        '201':
          description: Successfully added favorite product
        '400':
          description: Bad Request - Invalid product ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User cannot add this product to favorites
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-favorite-products
      summary: Delete Favorite Products
      description: Remove multiple products from user's favorites list
      tags:
        - favorite
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteFavoriteProductsRequest'
      responses:
        '204':
          description: Successfully deleted favorite products
        '400':
          description: Bad Request - Invalid product IDs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User cannot delete these favorites
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/favorite-sellers':
    get:
      operationId: get-favorite-sellers
      summary: Favorite Sellers List
      description: Display list of favorite sellers in a grid layout similar to search results
      tags:
        - favorite
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number
          example: 1
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of items per page
          example: 10
      responses:
        '200':
          description: Successfully retrieved favorite sellers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFavoriteSellersResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: add-favorite-seller
      summary: Add Favorite Seller
      description: Add a seller to user's favorites list
      tags:
        - favorite
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddFavoriteSellersRequest'
      responses:
        '201':
          description: Successfully added favorite seller
        '400':
          description: Bad Request - Invalid seller ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User cannot add this seller to favorites
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-favorite-sellers
      summary: Delete Multiple Favorite Sellers
      description: Remove multiple sellers from user's favorites list
      tags:
        - favorite
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteFavoriteSellersRequest'
      responses:
        '204':
          description: Successfully deleted favorite sellers
        '400':
          description: Bad Request - Invalid seller IDs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User cannot delete these favorites
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /countries:
    get:
      operationId: get-countries
      summary: Get countries
      description: Get a list of all available countries
      tags:
        - country
      parameters:
        - name: mode
          in: query
          description: Mode
          required: false
          schema:
            type: string
            enum:
              - inquiry
              - register
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountriesResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /postal-codes:
    get:
      operationId: get-postal-codes
      summary: Get US postal codes
      description: Get a list of all valid US postal codes (ZIP codes) for client-side validation
      tags:
        - system
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OK
          headers:
            Cache-Control:
              description: Cache control header for performance optimization
              schema:
                type: string
                example: 'public, max-age=3600'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostalCodesResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /faqs:
    get:
      operationId: get-faqs
      summary: Retrieve FAQs
      description: Fetch a list of all available FAQ categories and articles.
      tags:
        - faq
      parameters:
        - name: categoryId
          in: query
          description: Category ID
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaqsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /terms:
    get:
      operationId: get-terms
      summary: Retrieve Terms
      description: Fetch a list of all available Terms.
      tags:
        - term
      parameters:
        - name: categoryId
          in: query
          description: Category ID
          required: false
          schema:
            type: string
        - name: countryCode
          in: query
          description: Country Code
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Type
          required: false
          schema:
            type: string
            enum:
              - communityGuidelines
              - privacyPolicy
              - termsOfUseForGeneral
              - termsOfUseForSeller
              - paymentTerms
              - buyerProtection
              - commercialTransactionLaw
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TermsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /products/search:
    post:
      operationId: search-products
      summary: Search products
      description: Search products
      tags:
        - product
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchProductsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/products/{id}':
    get:
      operationId: get-product-by-id
      summary: Get product by ID
      description: Get product by ID
      tags:
        - product
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product to update.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sellers/{id}/products':
    get:
      operationId: get-seller-products
      summary: GET seller's products
      description: Get product list
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: Seller ID
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
        - name: sort
          in: query
          schema:
            type: string
            enum:
              - newest
              - views
              - favorites
              - priceLow
              - priceHigh
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SellerProductsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /threads:
    get:
      operationId: get-user-threads
      summary: Get user threads
      description: Retrieve a list of user threads.
      tags:
        - message
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          description: Status of the thread
          required: false
          schema:
            type: string
            enum:
              - unread
              - unreplied
        - name: keyword
          in: query
          description: Keyword to search nickname
          required: false
          schema:
            type: string
        - name: productId
          in: query
          description: Product ID to filter threads
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: The page number for pagination
          required: false
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of threads per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetThreadsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-thread
      summary: Create a new thread
      description: Create a new thread with either a seller or user.
      tags:
        - message
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateThreadRequest'
      responses:
        '201':
          description: Thread created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Thread'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/threads/unread-count':
    get:
      operationId: count-user-unread-threads
      summary: Count user unread threads
      description: Count the number of unread threads for the user.
      tags:
        - message
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: The id of the user
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountUnreadThreadsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/threads/{id}':
    get:
      operationId: get-user-thread-by-id
      summary: Get user thread by ID
      description: Retrieve a specific user thread by its ID.
      tags:
        - message
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Thread ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetThreadByIdResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/threads/{id}/messages':
    get:
      operationId: get-user-threads-messages
      summary: Get user threads messages
      description: Retrieve a list of user threads messages.
      tags:
        - message
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: The id of the thread
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The page number for pagination
          required: false
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of threads per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMessagesResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /messages:
    post:
      operationId: send-message
      summary: Send message
      description: Send a new message to a thread.
      tags:
        - message
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sellers/{id}':
    get:
      operationId: get-seller-by-id
      summary: Get seller by ID
      description: Retrieve a seller's details using their unique identifier.
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the seller to retrieve.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Seller'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /inquiries:
    post:
      operationId: create-inquiry
      summary: Create a new inquiry
      description: Create a new inquiry
      tags:
        - inquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InquiryCreateRequest'
      responses:
        '201':
          description: Created
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user/subscription-membership:
    post:
      operationId: subscription-membership
      summary: Subscription Membership
      description: Subscription Membership
      tags:
        - subscription
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionMembershipRequest'
      responses:
        '204':
          description: Successfully Subscription Membership
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      operationId: get-subscription-membership
      summary: Get Subscription Membership
      description: Get Subscription Membership
      tags:
        - subscription
      responses:
        '200':
          description: Subscription membership info
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/terms':
    put:
      operationId: update-confirm-terms
      summary: Update confirm Terms
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserTermsRequest'
      responses:
        '204':
          description: Successfully Update Confirm Terms
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sellers/{id}/rating':
    get:
      operationId: get-rating-seller
      summary: Get Rating seller by ID
      description: Retrieve a seller's rating using their unique identifier.
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the seller to retrieve.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RatingSellerResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/seller/products/{id}':
    put:
      operationId: update-product-by-id
      summary: Update product by ID
      description: Update an existing product's details using its unique identifier.
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product to update.
          schema:
            type: string
      requestBody:
        required: true
        description: The updated product information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductUpdateRequest'
      responses:
        '204':
          description: Successfully Update Product
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-product-by-id
      summary: Delete product by ID
      description: Delete an existing product's details using its unique identifier.
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product to update.
          schema:
            type: string
      responses:
        '204':
          description: Successfully Update Product
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /uploads:
    put:
      operationId: upload-file
      summary: Upload file to S3
      description: This API allows uploading a file to Amazon S3 and returns the key of the stored file.
      tags:
        - upload
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UploadFileRequest'
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadFileResponse'
        '400':
          description: Bad request (missing file or invalid format)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error during file upload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /seller/block-users:
    post:
      operationId: block-user
      summary: Block specific user
      tags:
        - seller
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockUserRequest'
      responses:
        '204':
          description: Successfully block users
        '400':
          description: Invalid request body
    get:
      operationId: get-block-users
      summary: GET block users
      tags:
        - seller
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockUsersResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /banners:
    get:
      operationId: get-banners
      summary: Get Banners
      description: Get Banners
      tags:
        - banner
      parameters:
        - name: countryCode
          in: query
          schema:
            type: string
          description: The country code for filter data
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBannersResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/seller/block-users/{id}':
    delete:
      operationId: delete-block-user
      summary: Delete Block specific user
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: block user ID
          schema:
            type: string
      responses:
        '204':
          description: Deleted block user successfully
        '404':
          description: Block User not found
  /seller/products:
    post:
      operationId: add-seller-products
      summary: Add a product by specific seller
      description: Add a product by specific seller
      tags:
        - seller
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductRequest'
      responses:
        '201':
          description: Created
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /followed-sellers/products:
    get:
      operationId: get-followed-seller-products
      summary: GET followed seller's products
      tags:
        - product
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
        - name: target
          in: query
          schema:
            $ref: '#/components/schemas/ProductTarget'
        - name: sort
          in: query
          schema:
            type: string
            enum:
              - newest
              - views
              - favorites
              - priceLow
              - priceHigh
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SellerProductsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /reports:
    post:
      operationId: create-report
      summary: Create a new report
      description: Create a new report
      tags:
        - report
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Report'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Report'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user/purchased-items:
    get:
      operationId: get-purchased-items
      summary: Get Purchased Items
      tags:
        - order
      parameters:
        - name: transactionStatus
          in: query
          schema:
            type: string
            enum:
              - all
              - unshipped
              - shipped
              - completed
        - name: from
          in: query
          schema:
            type: string
            example: '2025-03-13T14:00:00Z'
        - name: to
          in: query
          schema:
            type: string
            example: '2025-03-13T14:00:00Z'
        - name: orderBy
          in: query
          schema:
            type: string
            enum:
              - desc
              - asc
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchasedItemsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/user/purchased-items/{id}':
    get:
      operationId: get-order-detail-by-id
      summary: Get order detail by ID
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchasedItem'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: cancel-order-detail-by-id
      summary: Cancel Order Detail by ID
      description: Cancel order detail by ID
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Order Detail ID
      responses:
        '204':
          description: Successfully canceled order detail
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/user/purchased-items/{id}/reviews':
    post:
      operationId: create-order-detail-review
      summary: Create Order Detail Review
      description: Create a new order detail review.
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateReviewRequest'
      responses:
        '201':
          description: Created Review
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cart:
    get:
      operationId: getCartItems
      summary: Cart Items List
      description: Display list of items in your shopping cart
      tags:
        - cart
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successfully retrieved cart items
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: add-cart-item
      summary: Add Cart Item
      description: Add Cart Item
      tags:
        - cart
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CartItem'
      responses:
        '201':
          description: Successfully add cart item
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/cart-items/{id}':
    put:
      operationId: update-cart-item
      summary: Update Cart Item
      description: Update Cart Item
      tags:
        - cart
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the cart item
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CartItem'
      responses:
        '204':
          description: Successfully updated cart item
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-cart-item
      summary: Delete Cart Item
      description: Delete Cart Item
      tags:
        - cart
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier of the cart item
      responses:
        '204':
          description: Successfully deleted cart item
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /seller/publish-status:
    put:
      operationId: update-publish-status-seller
      summary: Update publish status seller
      description: Update publish status seller
      tags:
        - seller
      requestBody:
        required: true
        description: The updated publish status seller information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePublishSellerRequest'
      responses:
        '204':
          description: Successfully Publish Status Seller
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /seller/sold-items:
    get:
      operationId: get-sold-items
      summary: Get Sold Items
      tags:
        - seller
      parameters:
        - name: from
          in: query
          schema:
            type: string
            example: '2025-03-13T14:00:00Z'
        - name: to
          in: query
          schema:
            type: string
            example: '2025-03-13T14:00:00Z'
        - name: orderBy
          in: query
          schema:
            type: string
            enum:
              - desc
              - asc
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchasedItemsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/seller/sold-items/{id}':
    get:
      operationId: get-sold-item-by-id
      summary: Get Sold Item by ID
      description: Retrieve a specific sold item (order detail) by its unique identifier.
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the order detail (sold item) to retrieve.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchasedItem'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User is not authorized to access this sold item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Sold item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/seller/sold-items/{id}/status':
    put:
      operationId: update-sold-item-status
      summary: Update sold item status
      description: |
        Update the transaction status of a sold item (order detail).

        **Status Flow (one-way only):**
        - unshipped → preparingForShipment → shipped → received

        **Requirements:**
        - waybillNumber is required for 'shipped' and 'received' status
        - received_date is automatically set when status becomes 'received'
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: Order detail ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSoldItemStatusRequest'
            examples:
              preparing_for_shipment:
                summary: Update to preparing for shipment
                value:
                  transactionStatus: preparingForShipment
              shipped:
                summary: Update to shipped (requires waybill)
                value:
                  transactionStatus: shipped
                  waybillNumber: JP123456789
              received:
                summary: Update to received (requires waybill)
                value:
                  transactionStatus: received
                  waybillNumber: JP123456789
      responses:
        '204':
          description: Status updated successfully
        '400':
          description: 'Bad request (invalid status transition, missing waybill, etc.)'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden (not the seller of this item)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order detail not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orders:
    post:
      operationId: create-order
      summary: Create a new order
      description: Create a new order
      tags:
        - order
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/orders/{id}':
    get:
      operationId: get-order-by-id
      summary: Get Order by ID
      description: Display order by ID
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Order ID
      responses:
        '200':
          description: Successfully retrieved order
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /subscriptions/cancel/android:
    post:
      operationId: cancel-subscription-membership-android
      summary: Cancel Subscription Membership Android
      description: Cancel Subscription Membership Android
      tags:
        - subscription
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelSubscriptionAndroidRequest'
      responses:
        '204':
          description: Cancel Subscription Membership Successfully
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /subscriptions/cancel/ios:
    post:
      operationId: cancel-subscription-membership-ios
      summary: Cancel Subscription Membership IOS
      description: Cancel Subscription Membership IOS
      tags:
        - subscription
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelSubscriptionIOSRequest'
      responses:
        '204':
          description: Cancel Subscription Membership Successfully
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/orders/{id}/purchase':
    post:
      operationId: purchase-order
      summary: Purchase Order
      description: Purchase an order
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Order ID
      responses:
        '200':
          description: Successfully retrieved order
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderRequest'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /seller/orders-detail/status:
    get:
      operationId: check-order-detail-status
      summary: Check order detail status
      tags:
        - seller
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetStatusOrderDetailResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /seller/following-users:
    get:
      operationId: get-following-users
      summary: Get Following Users
      tags:
        - seller
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FollowingUserResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sales-summary/daily/{date}':
    get:
      operationId: get-daily-sales-summary
      summary: Get Daily Sales Summary
      description: Retrieve daily sales summary for a specific date
      tags:
        - sales-summary
      security:
        - bearerAuth: []
      parameters:
        - name: date
          in: path
          required: true
          description: Date in YYYY-MM-DD format
          schema:
            type: string
            format: date
            example: '2024-01-15'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailySalesSummaryResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sales-summary/monthly/{year}/{month}':
    get:
      operationId: get-monthly-sales-summary
      summary: Get Monthly Sales Summary
      description: Retrieve monthly sales summary for a specific year and month
      tags:
        - sales-summary
      security:
        - bearerAuth: []
      parameters:
        - name: year
          in: path
          required: true
          description: 'Year (e.g., 2024)'
          schema:
            type: integer
            minimum: 2020
            maximum: 2100
            example: 2024
        - name: month
          in: path
          required: true
          description: Month (1-12)
          schema:
            type: integer
            minimum: 1
            maximum: 12
            example: 1
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MonthlySalesSummaryResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sales-summary/yearly/{year}':
    get:
      operationId: get-yearly-sales-summary
      summary: Get Yearly Sales Summary
      description: Retrieve yearly sales summary for a specific year
      tags:
        - sales-summary
      security:
        - bearerAuth: []
      parameters:
        - name: year
          in: path
          required: true
          description: 'Year (e.g., 2024)'
          schema:
            type: integer
            minimum: 2020
            maximum: 2100
            example: 2024
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/YearlySalesSummaryResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /sales-summary/overall:
    get:
      operationId: get-overall-sales-summary
      summary: Get Overall Sales Summary
      description: Retrieve overall sales summary across all time periods
      tags:
        - sales-summary
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OverallSalesSummaryResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /sales-insights:
    get:
      operationId: get-sales-insights
      summary: Get Sales Insights
      description: Retrieve follower statistics and insights for the authenticated seller
      tags:
        - sales-insights
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesInsightsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User is not a seller
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - Sales insights data not available
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
tags:
  - name: auth
  - name: home
  - name: seller
  - name: sales-summary
  - name: sales-insights
  - name: followed-sellers
  - name: notification
  - name: profile
  - name: business
  - name: bank-account
  - name: membership
  - name: password
  - name: payment
  - name: pin
  - name: cart
  - name: favorites
  - name: system
  - name: user
  - name: faq
  - name: product
  - name: message
  - name: inquiry
  - name: subscription
  - name: upload
  - name: banner
  - name: report
  - name: order
components:
  schemas:
    Pagination:
      type: object
      required:
        - limit
        - page
      properties:
        limit:
          type: integer
          description: number of items per page
          x-oapi-codegen-extra-tags:
            validate: 'required,gte=1'
        page:
          type: integer
          description: number of selected page
          x-oapi-codegen-extra-tags:
            validate: 'required,gte=1'
    Address:
      type: object
      properties:
        isDifferentFromResidence:
          type: boolean
          description: Whether the address is different from the residence address
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        fullName:
          type: string
          description: Full name of the user
          example: John Doe
        address1:
          type: string
          description: Address line 1
          example: 123 Main St
        address2:
          type: string
          description: Address line 2
          example: Apt 1
        city:
          type: string
          description: City of the address
          example: New York
        state:
          type: string
          description: State of the address
          example: NY
        country:
          type: string
          description: Country of the address
          example: USA
        postalCode:
          type: string
          description: Postal code of the address
          example: '10001'
        phone:
          $ref: '#/components/schemas/Phone'
        mobile:
          $ref: '#/components/schemas/Phone'
        regionId:
          type: string
          description: Region id of the address
    Phone:
      type: object
      required:
        - countryCode
        - number
      properties:
        countryCode:
          type: string
          description: The country code of the phone number without the plus sign (+) (e.g. in Japan it is 81)
        number:
          type: string
    User:
      type: object
      required:
        - password
      properties:
        id:
          type: string
          description: User's unique identifier
          example: 123e4567-e89b-12d3-a456-************
        avatar:
          type: string
          description: User's avatar URL
        email:
          type: string
          description: User's email address
          example: <EMAIL>
        nickname:
          type: string
          description: Nickname of the user
          example: John Doe
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        gender:
          type: string
          description: Gender of the user
          example: Male
        dateOfBirth:
          type: string
          description: 'Date of birth of the user. Format: YYYY-MM-DD'
          example: '1990-01-01'
        pinSetting:
          type: boolean
          description: Whether the user has set a PIN
          example: true
        countryCode:
          type: string
          description: Country code of the user
          example: us
        regionId:
          type: string
          description: Region ID of the user
          example: 123e4567-e89b-12d3-a456-************
        receiveNewsletter:
          type: boolean
          description: Whether the user wants to receive newsletter
          example: true
        sellerId:
          type: string
          description: Seller ID of the user
          example: 123e4567-e89b-12d3-a456-************
        confirmTermDate:
          type: string
        isTermsRequired:
          type: boolean
          description: Whether the user has confirmed the terms
          example: true
        accountId:
          type: string
        membership:
          type: integer
        lang:
          type: string
          description: Language of the user
          example: en
    MaintenanceDescription:
      type: object
      required:
        - language
        - message
      properties:
        language:
          type: string
          x-oapi-codegen-extra-tags:
            validate: notEmpty
        message:
          type: string
          x-oapi-codegen-extra-tags:
            validate: notEmpty
    BankAccount:
      type: object
      description: Validation model for bank account profile information
      required:
        - bankName
        - accountNumber
        - accountHolderName
        - accountType
      properties:
        country:
          type: string
          description: Country/Region
        accountHolderName:
          type: string
          minLength: 2
          maxLength: 100
          description: Account holder name validation
        accountHolderKatakana:
          type: string
          description: Account holder name in katakana validation (JP only)
        bankCode:
          type: string
          pattern: '^\d{3,6}$'
          description: Bank code format validation
        bankName:
          type: string
          minLength: 2
          maxLength: 100
          description: Bank name with length constraints
        branchCode:
          type: string
          pattern: '^\d{3,6}$'
          description: Branch code format validation
        branchName:
          type: string
          minLength: 2
          maxLength: 100
        accountType:
          type: string
          enum:
            - savings
            - checking
            - corporate
          description: Validated account types
        routingNumber:
          type: string
          description: Routing number
        accountNumber:
          type: string
          pattern: '^\d{8,20}$'
          description: Account number format validation
        iban:
          type: string
          description: IBAN code
        swiftCode:
          type: string
          pattern: '^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$'
          description: SWIFT/BIC code format validation
        pin:
          type: string
          description: PIN for bank account
          example: '1234'
    CompanyInfo:
      type: object
      description: Validation model for indentity information
      properties:
        companyName:
          type: string
          description: Company name
          example: Devdev Inc.
        contactName:
          type: string
          description: Contact person name
          example: John Doe
        email:
          type: string
          description: Company/Contact person's email address
          example: <EMAIL>
        phoneNumber:
          type: string
          pattern: '^\+?[1-9]\d{1,14}$'
          description: Phone number with international format validation
        dunsNumber:
          type: string
          description: DUNS number
          example: '**********'
        websiteURL:
          type: string
          description: Website URL
          example: 'https://www.example.com'
    Kyc:
      type: object
      description: Validation model for indentity information
      properties:
        kycType:
          type: string
          enum:
            - passport
            - driverlicense
          description: Proof of indentity
        kycImgUrl:
          type: string
          description: 'Url to KYC image (S3, CDN, or any...)'
    Seller:
      type: object
      description: 'Comprehensive seller profile model representing a user''s seller account, including associated user information, store details, and seller-specific metadata'
      required:
        - accountType
        - shopName
      properties:
        id:
          type: string
          description: Seller ID
        accountId:
          type: string
          description: Account ID
        accountType:
          type: string
          enum:
            - individual
            - bussiness
          description: Validated account types
        stockLocation:
          type: string
          description: Stock locaiton. (Ship from)
          example: Japan
        shopName:
          type: string
          description: Shop name
        about:
          type: object
          additionalProperties:
            type: string
          description: Something about shop
        specialty:
          type: object
          additionalProperties:
            type: string
          description: Specialty
        favoriteBrands:
          type: array
          description: List of favorite brands
          items:
            type: string
            description: Name of brand
        avatarUrl:
          type: string
          description: 'Url to avatar (S3, CDN, or any...)'
        headerImgUrl:
          type: string
          description: 'Url to header image (S3, CDN, or any...)'
        isFavorite:
          type: boolean
          description: Whether the seller is a favorite of the user
        countryCode:
          type: string
          description: Country code
        regionId:
          type: string
          description: Region id
        numFavorites:
          type: integer
          description: Number of favorites
    Pin:
      type: string
      description: Admin PIN for additional security features
      example: '123456'
    PushNotificationSettings:
      type: object
      properties:
        stopAll:
          type: boolean
          description: Temporarily stop all push notifications from this app.
        importantNotices:
          type: boolean
          description: Get important updates about your account and services.
        campaignsAndPromotions:
          type: boolean
          description: 'Get updates on special offers, deals, and promotions.'
        messages:
          type: boolean
          description: Get notifications when you receive a new message.
        orderStatus:
          type: boolean
          description: Get updates on shipping and purchase status.
        cartAndFavorites:
          type: boolean
          description: Get alerts on price changes and stock updates.
        favoritedShops:
          type: boolean
          description: Get notified when someone favorites your shop.
        listedItems:
          type: boolean
          description: Get updates when your listed items are purchased.
        withdrawals:
          type: boolean
          description: Get updates on your withdrawal request status.
    EmailNotificationSettings:
      type: object
      properties:
        importantNotices:
          type: boolean
          description: Get important updates about your account and services.
        campaignsAndPromotions:
          type: boolean
          description: 'Get updates on special offers, deals, and promotions.'
        messages:
          type: boolean
          description: Get notifications when you receive a new message.
        orderStatus:
          type: boolean
          description: Get updates on shipping and purchase status.
        cartAndFavorites:
          type: boolean
          description: Get alerts on price changes and stock updates.
        favoritedShops:
          type: boolean
          description: Get notified when someone favorites your shop.
        listedItems:
          type: boolean
          description: Get updates when your listed items are purchased.
        withdrawals:
          type: boolean
          description: Get updates on your withdrawal request status.
        login:
          type: boolean
          description: Get alerts when a new device logs in.
    Country:
      type: object
      properties:
        code:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        regions:
          type: array
          items:
            $ref: '#/components/schemas/Region'
        phoneCode:
          type: string
        isCorporateOnly:
          type: boolean
        bankAccountType:
          $ref: '#/components/schemas/BankAccountType'
        currencies:
          type: array
          items:
            $ref: '#/components/schemas/Currency'
        taxName:
          $ref: '#/components/schemas/TaxName'
    Region:
      type: object
      properties:
        id:
          type: string
        code:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        subRegions:
          type: array
          items:
            $ref: '#/components/schemas/Region'
    Faq:
      type: object
      properties:
        id:
          type: string
        title:
          type: object
          additionalProperties:
            type: string
        content:
          $ref: '#/components/schemas/FaqContent'
        isCategory:
          type: boolean
        createdAt:
          type: string
        updatedAt:
          type: string
    FaqContent:
      type: object
      properties:
        bodyHtml:
          type: object
          additionalProperties:
            type: string
        attachments:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
    Term:
      type: object
      properties:
        id:
          type: string
        title:
          type: object
          additionalProperties:
            type: string
        content:
          $ref: '#/components/schemas/TermContent'
        isCategory:
          type: boolean
        countryCode:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
    TermContent:
      type: object
      properties:
        bodyUrl:
          type: object
          additionalProperties:
            type: string
        isRequired:
          type: boolean
          description: 同意必須
    Product:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier for the product
        name:
          type: object
          additionalProperties:
            type: string
          description: Name of the product
          example:
            en: iPhone 15
            ko: 아이폰 15
        categoryId:
          type: string
          description: Category of the product
        categories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
        target:
          $ref: '#/components/schemas/ProductTarget'
        size:
          type: string
          description: Size information (if applicable)
        sizeLange:
          type: integer
          description: Size lange
          example: -3
        sizeDetail:
          type: string
          description: Size detail of the product
        brandId:
          type: string
          description: Brand of the product
        brand:
          $ref: '#/components/schemas/ProductBrand'
        brandNameOther:
          type: string
          description: Brand other of the product in case of not in the list
          example: Apple
        condition:
          $ref: '#/components/schemas/ProductCondition'
        description:
          type: object
          additionalProperties:
            type: string
          description: Detailed description of the product
          example:
            en: Latest Apple iPhone with A16 Bionic chip.
            ko: 최신 Apple iPhone A16 Bionic 칩을 탑재한 제품입니다.
        images:
          type: array
          description: List of product images
          items:
            type: string
        releaseDate:
          type: string
          description: Timestamp of when the product was released
          example: '2025-03-13T14:00:00Z'
        salesStatus:
          $ref: '#/components/schemas/ProductSalesStatus'
        transactionStatus:
          $ref: '#/components/schemas/ProductTransactionStatus'
        price:
          type: number
          format: double
          description: Price of the product
          example: 999.99
        isDiscount:
          type: boolean
          description: Does the product have a discount?
          example: true
        discountAmount:
          type: number
          format: double
          description: Discount amount applied to the product
          example: 30000
          minimum: 0
        discountRate:
          type: number
          format: double
          description: Discount percentage applied to the product
          example: 10.5
          minimum: 0
          maximum: 100
        shipFrom:
          type: string
          description: Country of the product
          example: KR
        shippingMethod:
          type: string
          description: Shipping method of the product
        numFavorites:
          type: integer
          description: Number of favorites for the product
          example: 100
        isFavorite:
          type: boolean
          description: Whether the product is in the user's favorites
          example: true
        sellerId:
          type: string
        seller:
          $ref: '#/components/schemas/Seller'
    ProductTarget:
      type: string
      enum:
        - men
        - women
        - unisex
        - children
    ProductCondition:
      type: string
      enum:
        - level1
        - level2
        - level3
        - level4
        - level5
        - level6
    ProductSalesStatus:
      type: string
      enum:
        - draft
        - onSale
        - sold
        - suspended
        - available
    ProductTransactionStatus:
      type: string
      enum:
        - preparingForShipment
        - shipped
        - completed
        - onHold
        - unshipped
        - underReview
        - canceled
        - received
    ProductCategory:
      type: object
      properties:
        id:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        sizeTable:
          $ref: '#/components/schemas/SizeTable'
        availableSizes:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/ProductSize'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
    ProductBrand:
      type: object
      properties:
        id:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
    Thread:
      type: object
      properties:
        id:
          type: string
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
        user:
          $ref: '#/components/schemas/User'
        seller:
          $ref: '#/components/schemas/Seller'
        message:
          $ref: '#/components/schemas/Message'
        updatedAt:
          type: string
    Message:
      type: object
      properties:
        id:
          type: string
        content:
          type: string
        productId:
          type: string
        product:
          $ref: '#/components/schemas/Product'
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/Attachment'
        userId:
          type: string
        createdAt:
          type: string
        readAt:
          type: string
    Notification:
      type: object
      description: Notification model representing a single notification
      properties:
        id:
          type: string
          description: Unique identifier for the notification
        icon:
          type: string
          description: Icon of the notification
        title:
          type: object
          description: Title of the notification
          additionalProperties:
            type: string
        systemType:
          type: string
          description: System type of the notification
          enum:
            - notification
            - notice
        type:
          type: string
          description: Type of the notification
          enum:
            - IMPORTANT_SYSTEM
            - REMINDER_UNRATED_TXN
            - CAMPAIGN_PROMOTION
            - FEATURE_UPDATE
            - INCOMING_MESSAGE
            - TXN_PREPARING
            - TXN_SHIPPED
            - FAV_ON_SALE
            - CART_PURCHASED
            - PRICE_CHANGED
            - ITEM_ON_SALE
            - ITEM_REMOVED
            - LOGIN_DETECTED
            - SELLER_IMPORTANT
            - SHOP_FAVORITED
            - PRODUCT_FAVORITED
            - PRODUCT_PURCHASED
            - PRODUCT_RECEIVED
            - WITHDRAWAL_ACCEPTED
            - WITHDRAWAL_COMPLETED
        message:
          type: object
          description: Notification message
          additionalProperties:
            type: string
        keyImage:
          type: object
          additionalProperties:
            type: string
        articleImages:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        data:
          type: object
          description: Additional data associated with the notification
          additionalProperties:
            type: string
        url:
          type: string
          description: URL of the notification
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the notification was created
        isRead:
          type: boolean
          description: Indicates if the notification has been read
    InquiryCategory:
      type: string
      enum:
        - general
        - purchasing
        - selling
        - areaService
    InquiryStatus:
      type: string
      enum:
        - new
        - inProgress
        - resolved
        - closed
        - rejected
    InquiryContentCountry:
      type: object
      properties:
        country:
          type: string
          description: Stores country when category code is ‘areaService’
        state:
          type: string
          description: Stores state when category code is ‘areaService’
        city:
          type: string
          description: Stores city when category code is ‘areaService’
    InquiryContentMessage:
      type: object
      properties:
        accountId:
          type: string
          description: Stores account ID when category code is not ‘areaService’
        fullName:
          type: string
          description: Stores full name when category code is not ‘areaService’
        emailAddress:
          type: string
          description: Stores email address when category code is not ‘areaService’
        message:
          type: string
          description: Stores message when category code is not ‘areaService’
    InquiryContent:
      type: object
      properties:
        countryInquiry:
          $ref: '#/components/schemas/InquiryContentCountry'
        messageInquiry:
          $ref: '#/components/schemas/InquiryContentMessage'
    Inquiry:
      type: object
      required:
        - categoryCode
        - status
        - content
      properties:
        id:
          type: string
        categoryCode:
          $ref: '#/components/schemas/InquiryCategory'
        status:
          $ref: '#/components/schemas/InquiryStatus'
        content:
          $ref: '#/components/schemas/InquiryContent'
        createdAt:
          type: string
        updatedAt:
          type: string
    BannerActionType:
      type: string
      enum:
        - query
        - url
        - inAppUrl
        - shop
        - noAction
      description: Type of banner action
    BannerAction:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/BannerActionType'
        query:
          $ref: '#/components/schemas/BannerQuery'
        url:
          type: string
          description: URL to redirect to
        inAppUrl:
          type: string
          description: In-app URL to redirect to
        shop:
          type: string
          description: Shop ID to redirect to
    BannerQuery:
      type: object
      properties:
        targets:
          type: array
          items:
            $ref: '#/components/schemas/ProductTarget'
        categoryIds:
          type: array
          items:
            type: string
            description: Category ID
        brandNames:
          type: array
          items:
            type: string
            description: Brand name
        minPrice:
          type: number
          format: double
          description: Minimum price to search for
        maxPrice:
          type: number
          format: double
          description: Maximum price to search for
        conditions:
          type: array
          items:
            type: string
            description: Condition
        sizes:
          type: array
          items:
            type: string
            description: Size
        countryCodes:
          type: array
          items:
            type: string
            description: Country code
        saleStatuses:
          type: array
          items:
            $ref: '#/components/schemas/ProductSalesStatus'
        keyword:
          type: string
          description: Keyword to search for
    BannerTranslation:
      type: object
      description: additionalProperty is language code
      properties:
        status:
          $ref: '#/components/schemas/BannerStatus'
        title:
          type: string
        position:
          type: array
          items:
            type: string
            description: position
        bannerImageUrl:
          type: string
        heroImageUrl:
          type: string
    BannerStatus:
      type: string
      enum:
        - published
        - draft
    Banner:
      type: object
      properties:
        id:
          type: string
        action:
          $ref: '#/components/schemas/BannerAction'
        countryCodes:
          type: array
          items:
            type: string
            description: Country code
        bannerTranslations:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/BannerTranslation'
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'
    Admin:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
    BlockUser:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        sellerId:
          type: string
          format: uri
    Report:
      type: object
      properties:
        id:
          type: string
        status:
          $ref: '#/components/schemas/ReportStatus'
        detail:
          type: string
        reporterId:
          type: string
        reporterType:
          type: string
          enum:
            - user
            - seller
        reporter:
          oneOf:
            - $ref: '#/components/schemas/User'
            - $ref: '#/components/schemas/Seller'
        itemId:
          type: string
        itemType:
          type: string
          enum:
            - product
            - user
            - seller
        item:
          oneOf:
            - $ref: '#/components/schemas/Product'
            - $ref: '#/components/schemas/User'
            - $ref: '#/components/schemas/Seller'
        createdAt:
          type: string
        updatedAt:
          type: string
    ReportStatus:
      type: string
      enum:
        - notHandled
        - inProgress
        - pending
        - completed
        - uncompleted
    PurchasedItem:
      type: object
      properties:
        id:
          type: string
        orderId:
          type: string
        transactionStatus:
          $ref: '#/components/schemas/ProductTransactionStatus'
        createdAt:
          type: string
          format: date-time
        productId:
          type: string
        product:
          $ref: '#/components/schemas/Product'
        seller:
          $ref: '#/components/schemas/Seller'
        waybillNumber:
          type: string
        amount:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        totalAmount:
          type: number
          format: double
        taxDetails:
          type: array
          items:
            $ref: '#/components/schemas/TaxDetail'
        taxInfos:
          type: array
          items:
            $ref: '#/components/schemas/TaxItemInfo'
        orderEnd:
          type: string
          format: date-time
        orderNumber:
          type: string
        shippedDate:
          type: string
          format: date-time
        receivedDate:
          type: string
          format: date-time
        completedDate:
          type: string
          format: date-time
        note:
          type: string
        order:
          $ref: '#/components/schemas/Order'
        discountAmount:
          type: number
          format: double
        salesFeeInfo:
          $ref: '#/components/schemas/SalesFeeInfo'
    CartItem:
      type: object
      description: Model for cart item information
      required:
        - id
        - productId
        - userId
      properties:
        id:
          type: string
          description: Unique identifier of the cart item
          example: 123e4567-e89b-12d3-a456-************
        productId:
          type: string
          description: ID of the product in cart
          example: 123e4567-e89b-12d3-a456-426614174001
        product:
          $ref: '#/components/schemas/Product'
        isFavoriteSeller:
          type: boolean
          description: Whether the product is of a favorite seller
        userId:
          type: string
          description: Unique identifier of the user
          example: 123e4567-e89b-12d3-a456-************
        note:
          type: string
          description: Note for seller
        amount:
          type: number
          format: double
          description: Amount (excluding tax)
        discountAmount:
          type: number
          format: double
          description: Discount amount applied to this item
        taxAmount:
          type: number
          format: double
          description: Tax amount
        taxRate:
          type: number
          format: double
          description: Tax rate
        totalAmount:
          type: number
          format: double
          description: Total amount
        taxInfos:
          type: array
          items:
            $ref: '#/components/schemas/TaxItemInfo'
    Attachment:
      type: object
      required:
        - filePath
      properties:
        fileName:
          type: string
        fileType:
          type: string
        fileSize:
          type: integer
        filePath:
          type: string
    CreditCard:
      type: object
      properties:
        brand:
          type: string
        cardNumber:
          type: string
        expiryYear:
          type: string
        expiryMonth:
          type: string
        cardHolderName:
          type: string
    Order:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        orderNumber:
          type: string
        amount:
          type: number
          format: double
        totalAmount:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        administrativeFee:
          type: number
          format: double
        administrativeFeeRate:
          type: number
          format: double
        administrativeFeeTaxRate:
          type: number
          format: double
        administrativeFeeTaxAmount:
          type: number
          format: double
        purchaseFee:
          type: number
          format: double
        purchaseFeeTaxRate:
          type: number
          format: double
        purchaseFeeTaxAmount:
          type: number
          format: double
        items:
          type: array
          items:
            $ref: '#/components/schemas/PurchasedItem'
        paymentMethod:
          $ref: '#/components/schemas/CreditCard'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        taxInfo:
          $ref: '#/components/schemas/TaxInfo'
        buyer:
          $ref: '#/components/schemas/User'
          description: Information about the buyer
        isUserBlockedBySeller:
          type: boolean
          description: Whether the seller has blocked this buyer
    TaxDetail:
      type: object
      properties:
        amount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
    Subscription:
      type: object
      properties:
        userId:
          type: string
        id:
          type: string
        platform:
          type: string
        subscriptionStatus:
          type: string
        startDate:
          type: string
          format: date-time
        expireDate:
          type: string
          format: date-time
        transactionId:
          type: string
        autoRenewEnabled:
          type: boolean
    FollowingUser:
      type: object
      description: Model for following users
      properties:
        id:
          type: string
        userId:
          type: string
        sellerId:
          type: string
        user:
          $ref: '#/components/schemas/User'
    TaxInfo:
      type: object
      description: Tax information for an order
      properties:
        taxName:
          $ref: '#/components/schemas/TaxName'
        country:
          $ref: '#/components/schemas/Country'
        regions:
          type: array
          items:
            $ref: '#/components/schemas/Region'
        taxDetails:
          type: array
          items:
            $ref: '#/components/schemas/TaxDetail'
    TaxItemInfo:
      type: object
      description: Tax information for an order
      properties:
        taxName:
          $ref: '#/components/schemas/TaxName'
        country:
          $ref: '#/components/schemas/Country'
        region:
          $ref: '#/components/schemas/Region'
        district:
          $ref: '#/components/schemas/Region'
        taxDetails:
          type: array
          items:
            $ref: '#/components/schemas/TaxDetail'
    TaxName:
      type: string
      enum:
        - CONSUMPTION_TAX
        - SALES_TAX
        - VAT
        - GST
    SalesFeeInfo:
      type: object
      description: Sales fee information for an order detail
      properties:
        salesFee:
          type: number
          format: double
          description: The sales fee amount charged to the seller
        salesFeeRate:
          type: number
          format: double
          description: The sales fee rate percentage
        salesTaxAmount:
          type: number
          format: double
          description: The tax amount on the sales fee
        salesTaxRate:
          type: number
          format: double
          description: The tax rate applied to the sales fee
        salesTaxInfos:
          type: array
          description: Detailed tax information for the sales fee
          items:
            $ref: '#/components/schemas/TaxItemInfo'
    Cart:
      type: object
      description: Response schema for cart
      required:
        - items
      properties:
        items:
          type: array
          description: List of items in cart
          items:
            $ref: '#/components/schemas/CartItem'
        amount:
          type: number
          format: double
        discountAmount:
          type: number
          format: double
        totalAmount:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        administrativeFee:
          type: number
          format: double
        administrativeFeeRate:
          type: number
          format: double
        administrativeFeeTaxRate:
          type: number
          format: double
        administrativeFeeTaxAmount:
          type: number
          format: double
        purchaseFee:
          type: number
          format: double
        purchaseFeeTaxRate:
          type: number
          format: double
        purchaseFeeTaxAmount:
          type: number
          format: double
        paymentMethod:
          $ref: '#/components/schemas/CreditCard'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        taxInfo:
          $ref: '#/components/schemas/TaxInfo'
    BankAccountType:
      type: string
      enum:
        - SWIFT_CODE
        - IBAN
        - JAPAN_ACCOUNT
        - UAE_ACCOUNT
    Currency:
      type: object
      properties:
        code:
          type: string
          description: Currency code
          example: USD
          enum:
            - USD
            - EUR
            - JPY
            - GBP
            - KRW
            - HKD
            - AUD
            - AED
            - SAR
            - SGD
        isBankAccountCurrency:
          type: boolean
          description: Currency is bank account currency
          example: true
    BannerProducts:
      type: object
      properties:
        banner:
          $ref: '#/components/schemas/Banner'
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'
    KeywordProducts:
      type: object
      properties:
        keyword:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'
    ShopResult:
      type: object
      description: Response schema for the shop results
      properties:
        views:
          type: integer
        shopFavorites:
          type: integer
        productFavorites:
          type: integer
        orderAmountTaxExcluded:
          type: number
          format: double
        orderAmountTaxIncluded:
          type: number
          format: double
        salesAmount:
          type: number
          format: double
    ProductSize:
      type: object
      required:
        - lange
        - size
      properties:
        lange:
          type: integer
          example: -3
        size:
          type: string
          example: XXS
    SizeTable:
      type: string
      enum:
        - general
        - pants
        - shoes
        - hat
        - ring
    SalesSummaryOrderDetail:
      type: object
      properties:
        orderId:
          type: string
          description: The ID of the order
        orderNumber:
          type: string
          description: The number of the order
        orderAmount:
          type: number
          description: The amount of the order
    SalesSummaryOrderCounts:
      type: object
      description: Order count metrics
      properties:
        total:
          type: integer
          description: Total number of orders
          example: 150
        domestic:
          type: integer
          description: Number of domestic orders
          example: 100
        international:
          type: integer
          description: Number of international orders
          example: 50
      required:
        - total
        - domestic
        - international
    SalesSummaryOrderAmounts:
      type: object
      description: Order amount metrics
      properties:
        total:
          type: number
          format: double
          description: Total amount
          example: 15000.5
        domestic:
          type: number
          format: double
          description: Domestic amount
          example: 10000.25
        international:
          type: number
          format: double
          description: International amount
          example: 5000.25
      required:
        - total
        - domestic
        - international
    SalesSummarySimpleOrderTotals:
      type: object
      description: Simple order totals without domestic/international breakdown
      properties:
        totalOrdersCount:
          type: integer
          description: Total number of orders
          example: 1500
        totalOrderAmount:
          type: number
          format: double
          description: Total order amount
          example: 150000.75
      required:
        - totalOrdersCount
        - totalOrderAmount
    SalesSummaryQuarterlySummary:
      type: object
      description: Quarterly sales summary with domestic/international breakdown
      properties:
        year:
          type: integer
          description: Year of the quarter
          example: 2024
        quarter:
          type: integer
          minimum: 1
          maximum: 4
          description: Quarter number (1-4)
          example: 1
        orderCounts:
          $ref: '#/components/schemas/SalesSummaryOrderCounts'
        orderAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
      required:
        - year
        - quarter
        - orderCounts
        - orderAmounts
    CountryFollowerStats:
      type: object
      description: Follower statistics for a specific country
      properties:
        countryCode:
          type: string
          description: ISO 3166-1 alpha-3 country code
          example: JPN
        total:
          type: integer
          description: Total number of followers from this country
          example: 1250
        genderBreakdown:
          $ref: '#/components/schemas/GenderBreakdown'
        ageStats:
          type: array
          description: Age-based statistics for this country
          items:
            $ref: '#/components/schemas/AgeGroupStats'
      required:
        - countryCode
        - total
        - genderBreakdown
        - ageStats
    GenderBreakdown:
      type: object
      description: Gender breakdown statistics
      properties:
        female:
          type: integer
          description: Number of female followers
          example: 750
        male:
          type: integer
          description: Number of male followers
          example: 450
        other:
          type: integer
          description: Number of followers with other/unspecified gender
          example: 50
      required:
        - female
        - male
        - other
    AgeGroupStats:
      type: object
      description: Statistics for a specific age group
      properties:
        age:
          type: string
          description: Age group range
          enum:
            - under-20
            - 20-29
            - 30-39
            - 40-49
            - 50-59
            - 60-and-over
          example: 20-29
        total:
          type: integer
          description: Total number of followers in this age group
          example: 320
        genderBreakdown:
          $ref: '#/components/schemas/GenderBreakdown'
      required:
        - age
        - total
        - genderBreakdown
    FollowerTrends:
      type: object
      description: Follower statistics and trends
      properties:
        countries:
          type: array
          description: List of countries with follower statistics
          items:
            $ref: '#/components/schemas/CountryFollowerStats'
      required:
        - countries
    AuthRequest:
      type: object
      description: Auth Request
      required:
        - grantType
      properties:
        grantType:
          type: string
          enum:
            - password
            - refreshToken
          x-oapi-codegen-extra-tags:
            validate: oneof=password refreshToken
        username:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,gt=0'
        password:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,gt=0'
        refreshToken:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,gt=0'
    RegisterUserRequest:
      type: object
      required:
        - password
      properties:
        id:
          type: string
          description: User's unique identifier
          example: 123e4567-e89b-12d3-a456-************
        email:
          type: string
          description: User's email address
          example: <EMAIL>
        nickname:
          type: string
          description: Nickname of the user
          example: John Doe
        password:
          type: string
          description: Password of the user
          example: password123
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        gender:
          type: string
          description: Gender of the user
          example: Male
        dateOfBirth:
          type: string
          description: 'Date of birth of the user. Format: YYYY-MM-DD'
          example: '1990-01-01'
        countryCode:
          type: string
          description: Country code of the user
          example: us
        regionId:
          type: string
          description: Region ID of the user
          example: 123e4567-e89b-12d3-a456-************
        receiveNewsletter:
          type: boolean
          description: Whether the user wants to receive newsletter
          example: true
        lang:
          type: string
          description: Language of the user
          example: en
        homeAddress:
          $ref: '#/components/schemas/Address'
          description: Home address of the user
        shippingAddress:
          $ref: '#/components/schemas/Address'
          description: Shipping address of the user
        billingAddress:
          $ref: '#/components/schemas/Address'
          description: Billing address of the user
        creditCardAccessId:
          type: string
          description: Credit card access ID of the user
          example: 123e4567-e89b-12d3-a456-************
        registerSellerRequest:
          $ref: '#/components/schemas/RegisterSellerRequest'
          description: Register seller request of the user
    RegisterSellerRequest:
      type: object
      description: Request schema for enabling seller functionality
      properties:
        seller:
          $ref: '#/components/schemas/Seller'
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        kyc:
          $ref: '#/components/schemas/Kyc'
        address:
          $ref: '#/components/schemas/Address'
        bankAccount:
          $ref: '#/components/schemas/BankAccount'
      required:
        - seller
    SignupRequest:
      type: object
      description: Signup Request
      required:
        - email
      properties:
        email:
          type: string
          description: Email
          x-oapi-codegen-extra-tags:
            validate: 'required,email'
        registerType:
          type: string
          description: Register type
          enum:
            - general
            - seller
            - business
        receiveNewsletter:
          type: boolean
          description: Agree to receive news
        lang:
          type: string
          description: Language
        countryCode:
          type: string
          description: Country code
        regionId:
          type: string
          description: Region id
    SignupVerifyRequest:
      type: object
      description: Verify signup token
      required:
        - token
      properties:
        token:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'required,min=1'
    ResetPasswordRequestRequest:
      type: object
      description: Request to initiate password reset
      required:
        - email
      properties:
        email:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'required,email'
    ResetPasswordVerifyRequest:
      type: object
      description: Request to verify reset password token
      required:
        - token
      properties:
        token:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'required,min=1'
    ResetPasswordConfirmRequest:
      type: object
      description: Request to confirm new password
      required:
        - token
        - newPassword
        - confirmPassword
      properties:
        token:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'required,min=1'
        newPassword:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'required,min=8'
        confirmPassword:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'required,eqfield=NewPassword'
    UpdateUserProfileRequest:
      type: object
      description: Request schema for updating user profile information
      properties:
        user:
          $ref: '#/components/schemas/User'
          description: User's profile information
        homeAddress:
          $ref: '#/components/schemas/Address'
          description: Home address of the user
        shippingAddress:
          $ref: '#/components/schemas/Address'
          description: Shipping address of the user
        billingAddress:
          $ref: '#/components/schemas/Address'
          description: Billing address of the user
    VerifyCreditCardRequest:
      type: object
      required:
        - token
      properties:
        payer:
          $ref: '#/components/schemas/User'
        token:
          type: string
          description: The token of the credit card
        address:
          $ref: '#/components/schemas/Address'
    CreateVaultSetupTokenRequest:
      type: object
      properties:
        payment_source:
          type: object
          properties:
            card:
              type: object
              description: Empty card object for setup token creation
              properties: {}
          required:
            - card
      required:
        - payment_source
    CreateVaultPaymentTokenRequest:
      type: object
      properties:
        vaultSetupToken:
          type: string
          description: The vault setup token ID to convert to payment token
          example: 7GH53639GA425732B
      required:
        - vaultSetupToken
    UpdatePaymentMethodsRequest:
      type: object
      description: Request schema for updating user payment methods
      required:
        - accessId
      properties:
        accessId:
          type: string
          description: The access ID of the credit card
        billingAddress:
          $ref: '#/components/schemas/Address'
    UpdateSellerRequest:
      type: object
      description: Update seller information
      properties:
        seller:
          $ref: '#/components/schemas/Seller'
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        kyc:
          $ref: '#/components/schemas/Kyc'
        address:
          $ref: '#/components/schemas/Address'
        bankAccount:
          $ref: '#/components/schemas/BankAccount'
    UpdateSellerBankAccountRequest:
      $ref: '#/components/schemas/BankAccount'
    SettingPinRequest:
      type: object
      description: Request schema for setting up admin PIN
      required:
        - pin
      properties:
        pin:
          $ref: '#/components/schemas/Pin'
    UpdatePinRequest:
      type: object
      description: Request schema for setting up admin PIN
      required:
        - pin
        - newPin
      properties:
        pin:
          $ref: '#/components/schemas/Pin'
        newPin:
          $ref: '#/components/schemas/Pin'
    CheckPinRequest:
      type: object
      required:
        - pin
      properties:
        pin:
          $ref: '#/components/schemas/Pin'
    UpdatePasswordRequest:
      type: object
      required:
        - currentPassword
        - newPassword
      properties:
        currentPassword:
          type: string
          description: The current password of the user.
        newPassword:
          type: string
          description: The new password to be set.
    UpdateEmailRequest:
      type: object
      required:
        - email
      properties:
        email:
          type: string
          description: The new email to be set
          x-oapi-codegen-extra-tags:
            validate: 'required,email'
    VerifyEmailRequest:
      type: object
      required:
        - token
      properties:
        token:
          type: string
          description: The token to verify the email
    NotificationSettingsRequest:
      type: object
      properties:
        pushNotifications:
          $ref: '#/components/schemas/PushNotificationSettings'
        emailNotifications:
          $ref: '#/components/schemas/EmailNotificationSettings'
    CreateOrderRequest:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CartItem'
        isAddFavoriteSeller:
          type: boolean
          description: Whether to add favorite seller
    PurchaseOrderRequest:
      type: object
      properties:
        accessId:
          type: string
    AddFavoriteProductRequest:
      type: object
      description: Request body for adding a favorite product
      required:
        - productId
      properties:
        productId:
          type: string
          description: Product ID to add to favorites
          example: 123e4567-e89b-12d3-a456-************
    DeleteFavoriteProductsRequest:
      type: object
      description: Request body for deleting multiple favorite products
      required:
        - productIds
      properties:
        productIds:
          type: array
          description: List of product IDs to remove from favorites
          items:
            type: string
          example:
            - 123e4567-e89b-12d3-a456-************
            - 123e4567-e89b-12d3-a456-426614174001
    AddFavoriteSellersRequest:
      type: object
      description: Request body for adding a favorite seller
      required:
        - sellerId
      properties:
        sellerId:
          type: string
          description: Seller ID to add to favorites
          example: 123e4567-e89b-12d3-a456-************
    DeleteFavoriteSellersRequest:
      type: object
      description: Request body for deleting multiple favorite sellers
      required:
        - sellerIds
      properties:
        sellerIds:
          type: array
          description: List of seller IDs to remove from favorites
          items:
            type: string
          example:
            - 123e4567-e89b-12d3-a456-************
            - 123e4567-e89b-12d3-a456-426614174001
    SearchProductsRequest:
      type: object
      description: Search products request object
      properties:
        keyword:
          type: string
          description: Keyword to search for
        categoryId:
          type: string
          description: Category ID to search for
        categoryIds:
          type: array
          items:
            type: string
          description: Category IDs to search for
        target:
          $ref: '#/components/schemas/ProductTarget'
        targets:
          type: array
          items:
            $ref: '#/components/schemas/ProductTarget'
          description: Targets to search for
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/ProductCondition'
          description: Conditions to search for
        size:
          type: string
          description: Size to search for
        sizes:
          type: array
          items:
            type: string
          description: Sizes to search for
        sizeLanges:
          type: array
          items:
            type: integer
          description: Size langes to search for
        brandId:
          type: string
          description: Brand ID to search for
        brandIds:
          type: array
          items:
            type: string
          description: Brand IDs to search for
        brandNameOther:
          type: string
          description: Brand name to search for
        countryCode:
          type: string
          description: Country code to search for
        countryCodes:
          type: array
          items:
            type: string
          description: Country codes to search for
        regionId:
          type: string
          description: Region ID to search for
        minPrice:
          type: number
          format: double
          description: Minimum price to search for
        maxPrice:
          type: number
          format: double
          description: Maximum price to search for
        isDiscount:
          type: boolean
          description: Whether to search for discount products
        isSold:
          type: boolean
          description: Whether to search for sold products
        isUpcoming:
          type: boolean
          description: Whether to search for upcoming products
        isOnlyDiscount:
          type: boolean
          description: Whether to search for only discount products
        sellerId:
          type: string
          description: Seller ID to search for
        sort:
          type: string
          description: Sort by
          enum:
            - newest
            - views
            - favorites
            - priceLow
            - priceHigh
        pagination:
          $ref: '#/components/schemas/Pagination'
    CreateThreadRequest:
      type: object
      properties:
        sellerId:
          type: string
          description: ID of the seller to create thread with
        userId:
          type: string
          description: ID of the user to create thread with
        productId:
          type: string
          description: Optional product ID if the thread is related to a product
        initialMessage:
          type: string
          description: Initial message content for the thread
    SendMessageRequest:
      type: object
      properties:
        content:
          type: string
          description: The content of the message
        threadId:
          type: string
          description: The id of the thread
        productId:
          type: string
          description: The id of the product
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/Attachment'
          description: The attachments of the message
      required:
        - content
        - threadId
    InquiryCreateRequest:
      type: object
      description: Inquiry request object
      required:
        - categoryCode
        - content
      properties:
        categoryCode:
          $ref: '#/components/schemas/InquiryCategory'
        content:
          $ref: '#/components/schemas/InquiryContent'
    SubscriptionMembershipRequest:
      type: object
      required:
        - purchaseToken
        - subscriptionID
        - platform
        - transactionID
      properties:
        purchaseToken:
          type: string
        subscriptionID:
          type: string
        platform:
          type: string
        transactionID:
          type: string
    UpdateUserTermsRequest:
      type: object
      required:
        - confirmTermDate
      properties:
        confirmTermDate:
          type: string
    ProductUpdateRequest:
      type: object
      description: Product request update object
      properties:
        name:
          type: object
          additionalProperties:
            type: string
          description: Name of the product
          example:
            en: iPhone 15
            ko: 아이폰 15
        description:
          type: object
          additionalProperties:
            type: string
          description: Description of the product
        categoryId:
          type: string
          description: Category of the product
        target:
          $ref: '#/components/schemas/ProductTarget'
        size:
          type: string
          description: Size information (if applicable)
        sizeLange:
          type: integer
          description: Size lange
          example: -3
        brandId:
          type: string
          description: Brand of the product
        brandNameOther:
          type: string
          description: Brand other of the product in case of not in the list
          example: Apple
        condition:
          $ref: '#/components/schemas/ProductCondition'
        images:
          type: array
          description: List of product images
          items:
            type: string
        releaseDate:
          type: string
          description: Timestamp of when the product was released
          format: date-time
          example: '2025-03-13T14:00:00Z'
        salesStatus:
          $ref: '#/components/schemas/ProductSalesStatus'
        price:
          type: number
          format: double
          description: Price of the product
          example: 999.99
        isDiscount:
          type: boolean
          description: Does the product have a discount?
          example: true
        discountRate:
          type: number
          format: double
          description: Discount percentage applied to the product
          example: 10.5
          minimum: 0
          maximum: 100
        discountAmount:
          type: number
          format: double
          description: Discount amount applied to the product
          example: 30000
          minimum: 0
        sizeDetail:
          type: string
        shippingMethod:
          type: string
    UploadFileRequest:
      type: object
      required:
        - file
      properties:
        file:
          type: array
          items:
            type: string
            format: binary
            description: The file to upload
    BlockUserRequest:
      type: object
      description: Block User Request
      required:
        - userId
      properties:
        userId:
          type: string
          description: Name of the product
    ProductRequest:
      type: object
      description: Product response object
      required:
        - name
        - price
      properties:
        name:
          type: object
          additionalProperties:
            type: string
          description: Name of the product
          example:
            en: iPhone 15
            ko: 아이폰 15
        categoryId:
          type: string
          description: Category of the product
        target:
          $ref: '#/components/schemas/ProductTarget'
        size:
          type: string
          description: Size information (if applicable)
        sizeLange:
          type: integer
          description: Size lange
          example: -3
        brandId:
          type: string
          description: Brand of the product
        brandNameOther:
          type: string
          description: Brand other of the product in case of not in the list
          example: Apple
        condition:
          type: string
          description: Condition of the product
          enum:
            - level1
            - level2
            - level3
            - level4
            - level5
            - level6
        description:
          type: object
          additionalProperties:
            type: string
          description: Description of the product
        images:
          type: array
          description: List of product images
          items:
            type: string
        releaseDate:
          type: string
          description: Timestamp of when the product was released
          format: date-time
          example: '2025-03-13T14:00:00Z'
        salesStatus:
          type: string
          description: Status of the product
          enum:
            - available
            - draft
          example: public
        price:
          type: number
          format: double
          description: Price of the product
          example: 999.99
        isDiscount:
          type: boolean
          description: Does the product have a discount?
          example: true
        discountAmount:
          type: number
          format: double
          description: Discount amount applied to the product
          example: 30000
          minimum: 0
        discountRate:
          type: number
          format: double
          description: Discount percentage applied to the product
          example: 10.5
          minimum: 0
          maximum: 100
        sizeDetail:
          type: string
        shippingMethod:
          type: string
    CreateReviewRequest:
      type: object
      description: Create Review Request
      properties:
        rating:
          type: number
          format: double
        politeRating:
          type: number
          format: double
        speedRating:
          type: number
          format: double
        packRating:
          type: number
          format: double
    UpdatePublishSellerRequest:
      type: object
      description: Publish Status Seller request
      properties:
        publicStatus:
          type: string
          description: Status of the public
          enum:
            - draft
            - public
          example: public
    CancelSubscriptionAndroidRequest:
      type: object
      required:
        - message
        - data
      properties:
        message:
          type: object
          properties:
            data:
              type: string
        signedPayload:
          type: string
    CancelSubscriptionIOSRequest:
      type: object
      required:
        - message
        - data
      properties:
        signedPayload:
          type: string
    CancelAccountRequest:
      type: object
      description: Request body for canceling an account
      properties:
        pricesTooHigh:
          type: boolean
          description: Whether the price is too high
        productsUnavailable:
          type: boolean
          description: Whether the product or brand is unavailable
        deliveryTooLong:
          type: boolean
          description: Whether the delivery is too long
        itemProblem:
          type: boolean
          description: Whether the problem is with the item
        sellerUntrustworthy:
          type: boolean
          description: Whether the seller is untrustworthy
        appDifficultToUse:
          type: boolean
          description: Whether the app is difficult to use
        tooManyEmails:
          type: boolean
          description: Whether the user receives too many emails
        situationChanged:
          type: boolean
          description: Whether the situation has situation changed
        dontKnowHowToList:
          type: boolean
          description: Whether the user does not know how to list
        itemsDidNotSell:
          type: boolean
          description: Whether the items did not sell
        otherReason:
          type: string
          description: Other reason for canceling the account
    UpdateSoldItemStatusRequest:
      type: object
      required:
        - transactionStatus
      properties:
        transactionStatus:
          $ref: '#/components/schemas/ProductTransactionStatus'
          description: New transaction status (unshipped -> preparingForShipment -> shipped -> received)
        waybillNumber:
          type: string
          description: Waybill/tracking number (required for shipped and received status)
          maxLength: 255
    UpsertFCMTokenRequest:
      type: object
      required:
        - fcmToken
      properties:
        fcmToken:
          type: string
          description: Firebase Cloud Messaging token
          example: dGhpcyBpcyBhIGZha2UgZmNtIHRva2Vu
        deviceId:
          type: string
          description: Device identifier (optional)
          example: device-123456
        deviceType:
          type: string
          enum:
            - android
            - ios
            - web
          description: Type of device
          example: android
        appVersion:
          type: string
          description: Application version (optional)
          example: 1.0.0
    DeleteFCMTokenRequest:
      type: object
      required:
        - fcmToken
      properties:
        fcmToken:
          type: string
          description: Firebase Cloud Messaging token to be removed
          example: dGhpcyBpcyBhIGZha2UgZmNtIHRva2Vu
    ErrorResponse:
      type: object
      description: Response when abnormal
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        request_id:
          type: string
          description: Request id where the error occurred
        debug_message:
          type: string
          description: Debug message (for development)
    AuthResponse:
      type: object
      description: Auth Response
      required:
        - accessToken
        - refreshToken
      properties:
        accessToken:
          type: string
          description: Access token
        refreshToken:
          type: string
          description: Refresh token
    GetThreadsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Thread'
    GetThreadByIdResponse:
      type: object
      properties:
        id:
          type: string
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
        user:
          $ref: '#/components/schemas/User'
        seller:
          $ref: '#/components/schemas/Seller'
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        updatedAt:
          type: string
        createdAt:
          type: string
    CreateThreadResponse:
      $ref: '#/components/schemas/Thread'
    GetMessagesResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Message'
    StatusResponse:
      type: object
      required:
        - networkStatus
        - serverStatus
        - latestVersionIOS
        - latestVersionAndroid
        - forceUpdateIOS
        - forceUpdateAndroid
        - isMaintenance
      properties:
        networkStatus:
          type: boolean
          description: Network connection status
          example: true
        serverStatus:
          type: boolean
          description: Backend server status
          example: true
        latestVersionIOS:
          type: string
          description: Latest app version available for iOS
          example: 1.2.3
        latestVersionAndroid:
          type: string
          description: Latest app version available for Android
          example: 1.2.3
        forceUpdateIOS:
          type: boolean
          description: Whether user must update app to continue for iOS
          example: false
        forceUpdateAndroid:
          type: boolean
          description: Whether user must update app to continue for Android
          example: false
        isMaintenance:
          type: boolean
          description: Whether app is in maintenance mode
          example: false
        maintenanceDescription:
          description: Maintenance description
          example: We are currently performing maintenance on the app. Please check back soon.
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceDescription'
        message:
          type: string
          description: Additional status message
    HomeFeedsResponse:
      type: object
      description: Response schema for the timeline feed
      properties:
        topBanners:
          type: array
          description: List of top banners
          items:
            $ref: '#/components/schemas/Banner'
        bannerProducts:
          type: array
          description: List of body banners
          items:
            $ref: '#/components/schemas/BannerProducts'
        favoriteSellersProducts:
          type: array
          description: List of favorite sellers products
          items:
            $ref: '#/components/schemas/Product'
        keywordProducts:
          $ref: '#/components/schemas/KeywordProducts'
        targetProducts:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/Product'
    SellerDashboardResponse:
      type: object
      description: Response schema for the seller dashboard
      properties:
        yesterday:
          $ref: '#/components/schemas/ShopResult'
    GetBrandsResponse:
      type: object
      description: Response schema for retrieving a list of brands
      properties:
        items:
          type: array
          description: List of available brands
          items:
            $ref: '#/components/schemas/ProductBrand'
    GetProductCategoriesResponse:
      type: object
      description: Response schema for retrieving a list of product categories
      properties:
        items:
          type: array
          description: List of available product categories
          items:
            $ref: '#/components/schemas/ProductCategory'
    GetNotificationsResponse:
      type: object
      properties:
        items:
          type: array
          description: List of notifications for the user
          items:
            $ref: '#/components/schemas/Notification'
    CountUnreadNotificationsResponse:
      type: object
      properties:
        countNotifications:
          type: integer
          description: The number of unread notifications
        countNotices:
          type: integer
          description: The number of unread notices
      required:
        - countNotifications
        - countNotices
    CountUnreadThreadsResponse:
      type: object
      properties:
        countUnreadThreads:
          type: integer
          description: The number of unread threads
      required:
        - countUnreadThreads
    UserProfileResponse:
      type: object
      description: Comprehensive user profile response with optional seller profile details
      required:
        - user
      properties:
        user:
          $ref: '#/components/schemas/User'
        homeAddress:
          $ref: '#/components/schemas/Address'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        billingAddress:
          $ref: '#/components/schemas/Address'
        creditCard:
          $ref: '#/components/schemas/CreditCard'
        seller:
          $ref: '#/components/schemas/Seller'
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
    SellerProfileResponse:
      type: object
      description: Seller info
      required:
        - seller
      properties:
        seller:
          $ref: '#/components/schemas/Seller'
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        kyc:
          $ref: '#/components/schemas/Kyc'
        address:
          $ref: '#/components/schemas/Address'
        bankAccount:
          $ref: '#/components/schemas/BankAccount'
    NotificationSettingsResponse:
      type: object
      properties:
        pushNotifications:
          $ref: '#/components/schemas/PushNotificationSettings'
        emailNotifications:
          $ref: '#/components/schemas/EmailNotificationSettings'
    VerifyCreditCardResponse:
      type: object
      properties:
        accessId:
          type: string
          description: The access ID of the credit card
        redirectUrl:
          type: string
          description: The redirect URL to the credit card verification page
    CreateVaultSetupTokenResponse:
      type: object
      properties:
        id:
          type: string
          description: The setup token ID
          example: 7GH53639GA425732B
        status:
          type: string
          description: The status of the setup token
          example: CREATED
        payment_source:
          type: object
          properties:
            card:
              type: object
              description: Card information for the setup token
              properties: {}
        links:
          type: array
          items:
            type: object
            properties:
              href:
                type: string
                description: The complete target URL
              rel:
                type: string
                description: The link relation type
              method:
                type: string
                description: The HTTP method required to make the related call
      required:
        - id
        - status
    CreateVaultPaymentTokenResponse:
      type: object
      properties:
        id:
          type: string
          description: The payment token ID
          example: 8EC8D4G67VAS2
        status:
          type: string
          description: The status of the payment token
          example: CREATED
        customer:
          type: object
          properties:
            id:
              type: string
              description: The customer ID
              example: customer_4029352050
        payment_source:
          type: object
          properties:
            card:
              type: object
              properties:
                last_digits:
                  type: string
                  description: Last 4 digits of the card
                  example: '1234'
                brand:
                  type: string
                  description: Card brand
                  example: VISA
                type:
                  type: string
                  description: Card type
                  example: CREDIT
        links:
          type: array
          items:
            type: object
            properties:
              href:
                type: string
                description: The complete target URL
              rel:
                type: string
                description: The link relation type
              method:
                type: string
                description: The HTTP method required to make the related call
      required:
        - id
        - status
    UpdatePaymentMethodsResponse:
      type: object
      properties:
        cardResult:
          $ref: '#/components/schemas/CreditCard'
    GetFavoriteProductsResponse:
      type: object
      description: Response schema for favorite products list
      properties:
        items:
          type: array
          description: List of favorite products
          items:
            $ref: '#/components/schemas/Product'
    GetFavoriteSellersResponse:
      type: object
      description: Response schema for favorite seller list
      properties:
        items:
          type: array
          description: List of favorite sellers
          items:
            $ref: '#/components/schemas/Seller'
    CartItemsResponse:
      type: object
      description: Response schema for cart
      required:
        - items
      properties:
        items:
          type: array
          description: List of items in cart
          items:
            $ref: '#/components/schemas/CartItem'
        amount:
          type: number
          format: double
        discountAmount:
          type: number
          format: double
        totalAmount:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        administrativeFee:
          type: number
          format: double
        administrativeFeeRate:
          type: number
          format: double
        administrativeFeeTaxRate:
          type: number
          format: double
        administrativeFeeTaxAmount:
          type: number
          format: double
        purchaseFee:
          type: number
          format: double
        purchaseFeeTaxRate:
          type: number
          format: double
        purchaseFeeTaxAmount:
          type: number
          format: double
        paymentMethod:
          $ref: '#/components/schemas/CreditCard'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        taxInfo:
          $ref: '#/components/schemas/TaxInfo'
    CountriesResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Country'
    FaqsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Faq'
    TermsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Term'
    SearchProductsResponse:
      type: object
      description: Search products response object
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'
    SellerProductsResponse:
      type: object
      description: Get products response object
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'
    RatingSellerResponse:
      type: object
      description: Get rating seller response object
      properties:
        avgRating:
          type: number
          format: double
        avgPoliteRating:
          type: number
          format: double
        avgSpeedRating:
          type: number
          format: double
        avgPackRating:
          type: number
          format: double
        totalOrders:
          type: integer
        avgDaysToShip:
          type: number
          format: double
    UploadFileResponse:
      type: object
      properties:
        key:
          deprecated: true
          type: string
          description: The key or path of the file
        keys:
          type: array
          items:
            type: string
            description: The key or path of the file
      required:
        - key
        - keys
    GetBannersResponse:
      type: object
      description: Get banners response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Banner'
    BlockUsersResponse:
      type: object
      description: Get block users response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BlockUser'
    PurchasedItemsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/PurchasedItem'
    CreateOrderResponse:
      type: object
      properties:
        id:
          type: string
        accessId:
          type: string
        redirectUrl:
          type: string
        status:
          type: integer
    GetStatusOrderDetailResponse:
      type: object
      description: Get result status order detail
      properties:
        result:
          type: boolean
    FollowingUserResponse:
      type: object
      description: Get following users response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/FollowingUser'
    DailySalesSummaryResponse:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date of the summary in YYYY-MM-DD format
          example: '2024-01-15'
        orderCounts:
          $ref: '#/components/schemas/SalesSummaryOrderCounts'
        orderAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        orderAmountsTaxIncluded:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        salesAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        vat:
          type: number
          format: double
          description: Total VAT amount
          example: 100.5
        salesFee:
          type: number
          format: double
          description: Total sales fee (platform commission)
          example: 50.25
        orderDetails:
          type: array
          items:
            $ref: '#/components/schemas/SalesSummaryOrderDetail'
      required:
        - date
        - orderCounts
        - orderAmounts
        - orderAmountsTaxIncluded
        - salesAmounts
        - vat
        - salesFee
    MonthlySalesSummaryResponse:
      type: object
      properties:
        year:
          type: integer
          description: Year of the summary
          example: 2024
        month:
          type: integer
          minimum: 1
          maximum: 12
          description: Month of the summary (1-12)
          example: 1
        orderCounts:
          $ref: '#/components/schemas/SalesSummaryOrderCounts'
        orderAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        orderAmountsTaxIncluded:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        salesAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        vat:
          type: number
          format: double
          description: Total VAT amount for the month
          example: 1500.75
        salesFee:
          type: number
          format: double
          description: Total sales fee for the month
          example: 750.5
        dailyBreakdown:
          type: array
          description: Daily breakdown for the month (optional)
          items:
            $ref: '#/components/schemas/DailySalesSummaryResponse'
      required:
        - year
        - month
        - orderCounts
        - orderAmounts
        - orderAmountsTaxIncluded
        - salesAmounts
        - vat
        - salesFee
    YearlySalesSummaryResponse:
      type: object
      properties:
        year:
          type: integer
          description: Year of the summary
          example: 2024
        orderCounts:
          $ref: '#/components/schemas/SalesSummaryOrderCounts'
        orderAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        orderAmountsTaxIncluded:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        salesAmounts:
          $ref: '#/components/schemas/SalesSummaryOrderAmounts'
        vat:
          type: number
          format: double
          description: Total VAT amount for the year
          example: 18000.9
        salesFee:
          type: number
          format: double
          description: Total sales fee for the year
          example: 9000.45
        monthlyBreakdown:
          type: array
          description: Monthly breakdown for the year (optional)
          items:
            $ref: '#/components/schemas/MonthlySalesSummaryResponse'
      required:
        - year
        - orderCounts
        - orderAmounts
        - orderAmountsTaxIncluded
        - salesAmounts
        - vat
        - salesFee
    OverallSalesSummaryResponse:
      type: object
      properties:
        totalOrdersCount:
          type: integer
          description: Total number of orders across all time
          example: 1500
        totalOrderAmount:
          type: number
          format: double
          description: Total order amount across all time
          example: 150000.75
        finalizedSalesAmount:
          type: number
          format: double
          description: Total finalized sales amount (seller revenue after fees)
          example: 50000.75
        itemPrice:
          type: number
          format: double
          description: Total item price (product value)
          example: 60000
        vat:
          type: number
          format: double
          description: Total VAT amount across all time
          example: 6000
        salesFee:
          type: number
          format: double
          description: Total sales fee across all time
          example: 4000.25
        lastFourQuarters:
          type: array
          description: Summary for the last 4 quarters with domestic/international breakdown
          items:
            $ref: '#/components/schemas/SalesSummaryQuarterlySummary'
        yearlyBreakdown:
          type: array
          description: Yearly breakdown (optional)
          items:
            $ref: '#/components/schemas/YearlySalesSummaryResponse'
      required:
        - totalOrdersCount
        - totalOrderAmount
        - finalizedSalesAmount
        - itemPrice
        - vat
        - salesFee
    SalesInsightsResponse:
      type: object
      properties:
        followerTrends:
          $ref: '#/components/schemas/FollowerTrends'
      required:
        - followerTrends
    PostalCodesResponse:
      type: object
      description: Response containing list of valid US postal codes
      properties:
        postalCodes:
          type: array
          description: Array of valid US postal codes (5-digit ZIP codes)
          items:
            type: string
            pattern: '^[0-9]{5}$'
            example: '10001'
          example:
            - '10001'
            - '10002'
            - '10003'
            - '90210'
            - '94102'
        count:
          type: integer
          description: Total number of postal codes returned
          example: 41692
      required:
        - postalCodes
        - count
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
