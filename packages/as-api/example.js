import "dotenv/config";
import express from "express";
const { PORT = 8888 } = process.env;
const app = express();
app.set("view engine", "ejs");
app.use(express.static("public"));
// Create setup token
app.post("/api/vault/token", async (req, res) => {
try {
    // Use your access token to securely generate a setup token
    // with an empty payment_source
    const vaultResponse = await fetch("https://api-m.sandbox.paypal.com/v3/vault/setup-tokens", {
        method: "POST",
        body: JSON.stringify({ payment_source: { card: {}} }),
        headers: {
            Authorization: 'Bearer ${ACCESS-TOKEN}',
            "PayPal-Request-Id": Date.now()
        }
    })
    // Return the reponse to the client
    res.json(vaultResponse);
} catch (err) {
    res.status(500).send(err.message);
}
})
// Create payment token from a setup token
app.post("/api/vault/payment-token", async (req, res) => {
    try {
    const paymentTokenResult = await fetch(
        "https://api-m.sandbox.paypal.com/v3/vault/payment-tokens",
        {
        method: "POST",
        body: {
            payment_source: {
            token: {
                id: req.body.vaultSetupToken,
                type: "SETUP_TOKEN"
            }
            }
        },
        headers: {
            Authorization: 'Bearer ${ACCESS-TOKEN}',
            "PayPal-Request-Id": Date.now()
        }
        })
    const paymentMethodToken = paymentTokenResult.id
    const customerId = paymentTokenResult.customer.id
    await save(paymentMethodToken, customerId)
    res.json(captureData);
} catch (err) {
    res.status(500).send(err.message);
}
})
const save = async function(paymentMethodToken, customerId) {
    // Specify where to save the payment method token
}
app.listen(PORT, () => {
console.log('Server listening at http://localhost:${PORT}/');
})
